@extends('admin.layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between">
            <h1 class="h3 mb-3 text-gray-800">{{$page_title}}</h1>
        </div>
        @include('admin.message')
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    {{__('Total Bookings')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['total'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-calendar fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    {{__('Confirmed')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['confirmed'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    {{__('Pending')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['pending'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    {{__('Cancelled')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['cancelled'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-times fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{{__('Filters')}}</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{route('ratehawk.admin.bookings.index')}}">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>{{__('Status')}}</label>
                                <select name="status" class="form-control">
                                    <option value="">{{__('All Statuses')}}</option>
                                    @foreach($statuses as $key => $label)
                                        <option value="{{$key}}" {{($filters['status'] ?? '') == $key ? 'selected' : ''}}>
                                            {{$label}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>{{__('From Date')}}</label>
                                <input type="date" name="from_date" class="form-control" value="{{$filters['from_date'] ?? ''}}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>{{__('To Date')}}</label>
                                <input type="date" name="to_date" class="form-control" value="{{$filters['to_date'] ?? ''}}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>{{__('Search')}}</label>
                                <input type="text" name="search" class="form-control" placeholder="{{__('Order ID, Guest Name, Hotel...')}}" value="{{$filters['search'] ?? ''}}">
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">{{__('Filter')}}</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Bookings Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{__('Bookings')}}</h6>
                <div>
                    <a href="{{route('ratehawk.admin.bookings.export')}}" class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> {{__('Export')}}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>{{__('Order ID')}}</th>
                                <th>{{__('Guest')}}</th>
                                <th>{{__('Hotel')}}</th>
                                <th>{{__('Check-in')}}</th>
                                <th>{{__('Check-out')}}</th>
                                <th>{{__('Status')}}</th>
                                <th>{{__('Total')}}</th>
                                <th>{{__('Created')}}</th>
                                <th>{{__('Actions')}}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($bookings as $booking)
                                <tr>
                                    <td>
                                        <strong>{{$booking->order_id}}</strong>
                                    </td>
                                    <td>
                                        {{$booking->guest_name}}<br>
                                        <small class="text-muted">{{$booking->guest_email}}</small>
                                    </td>
                                    <td>
                                        <strong>{{$booking->hotel_name}}</strong><br>
                                        <small class="text-muted">{{$booking->hotel_location}}</small>
                                    </td>
                                    <td>
                                        {{$booking->check_in_date ? \Carbon\Carbon::parse($booking->check_in_date)->format('Y-m-d') : '-'}}
                                    </td>
                                    <td>
                                        {{$booking->check_out_date ? \Carbon\Carbon::parse($booking->check_out_date)->format('Y-m-d') : '-'}}
                                    </td>
                                    <td>
                                        @if($booking->status == 'confirmed')
                                            <span class="badge badge-success">{{__('Confirmed')}}</span>
                                        @elseif($booking->status == 'cancelled')
                                            <span class="badge badge-danger">{{__('Cancelled')}}</span>
                                        @elseif($booking->status == 'pending')
                                            <span class="badge badge-warning">{{__('Pending')}}</span>
                                        @else
                                            <span class="badge badge-secondary">{{ucfirst($booking->status)}}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($booking->total_amount)
                                            {{$booking->currency}} {{number_format($booking->total_amount, 2)}}
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        {{$booking->created_at->format('Y-m-d H:i')}}
                                    </td>
                                    <td>
                                        <a href="{{route('ratehawk.admin.bookings.show', $booking->id)}}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        @if($booking->status == 'confirmed')
                                        <button type="button" class="btn btn-warning btn-sm" onclick="cancelBooking({{$booking->id}})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        @endif
                                        <button type="button" class="btn btn-secondary btn-sm" onclick="checkStatus({{$booking->id}})">
                                            <i class="fas fa-sync"></i>
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="9" class="text-center">{{__('No bookings found')}}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{$bookings->appends(request()->query())->links()}}
                </div>
            </div>
        </div>
    </div>

    <script>
        function cancelBooking(id) {
            if (confirm('{{__("Are you sure you want to cancel this booking?")}}')) {
                $.post('{{route("ratehawk.admin.bookings.cancel", ":id")}}'.replace(':id', id), {
                    _token: '{{csrf_token()}}'
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || '{{__("Error occurred")}}');
                    }
                });
            }
        }

        function checkStatus(id) {
            $.post('{{route("ratehawk.admin.bookings.check-status", ":id")}}'.replace(':id', id), {
                _token: '{{csrf_token()}}'
            }).done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || '{{__("Error occurred")}}');
                }
            });
        }
    </script>
@endsection
