@extends('admin.layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between">
            <h1 class="h3 mb-3 text-gray-800">{{$page_title}}</h1>
            <div>
                <button type="button" class="btn btn-primary" onclick="runCacheTest()">
                    <i class="fas fa-play"></i> {{__('Run Tests')}}
                </button>
                <button type="button" class="btn btn-warning" onclick="clearCache()">
                    <i class="fas fa-trash"></i> {{__('Clear Cache')}}
                </button>
                <button type="button" class="btn btn-info" onclick="refreshStats()">
                    <i class="fas fa-sync"></i> {{__('Refresh Stats')}}
                </button>
            </div>
        </div>
        @include('admin.message')

        <!-- Cache Configuration -->
        <div class="row mb-4">
            <div class="col-lg-6">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Cache Configuration')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Driver')}}:</strong><br>
                                <span class="badge badge-info">{{$cacheStats['config']['driver'] ?? 'Unknown'}}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Status')}}:</strong><br>
                                @if($cacheStats['config']['enabled'] ?? false)
                                    <span class="badge badge-success">{{__('Enabled')}}</span>
                                @else
                                    <span class="badge badge-danger">{{__('Disabled')}}</span>
                                @endif
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Fallback')}}:</strong><br>
                                @if($cacheStats['config']['fallback_enabled'] ?? false)
                                    <span class="badge badge-success">{{__('Enabled')}}</span>
                                @else
                                    <span class="badge badge-secondary">{{__('Disabled')}}</span>
                                @endif
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Memory Cache')}}:</strong><br>
                                <span class="badge badge-info">{{$cacheStats['memory_cache_size'] ?? 0}} {{__('items')}}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Connectivity Status')}}</h6>
                    </div>
                    <div class="card-body">
                        @if(isset($cacheStats['connectivity']))
                            <div class="row">
                                <div class="col-md-4">
                                    <strong>{{__('Primary')}}:</strong><br>
                                    @if($cacheStats['connectivity']['primary'] ?? false)
                                        <span class="badge badge-success">{{__('Connected')}}</span>
                                    @else
                                        <span class="badge badge-danger">{{__('Failed')}}</span>
                                    @endif
                                </div>
                                <div class="col-md-4">
                                    <strong>{{__('Secondary')}}:</strong><br>
                                    @if($cacheStats['connectivity']['secondary'] ?? false)
                                        <span class="badge badge-success">{{__('Connected')}}</span>
                                    @else
                                        <span class="badge badge-danger">{{__('Failed')}}</span>
                                    @endif
                                </div>
                                <div class="col-md-4">
                                    <strong>{{__('Memory')}}:</strong><br>
                                    @if($cacheStats['connectivity']['memory'] ?? false)
                                        <span class="badge badge-success">{{__('Available')}}</span>
                                    @else
                                        <span class="badge badge-danger">{{__('Failed')}}</span>
                                    @endif
                                </div>
                            </div>
                        @else
                            <p class="text-muted">{{__('Connectivity test not available')}}</p>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Test Results -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{{__('Test Results')}}</h6>
            </div>
            <div class="card-body">
                <div id="test-results">
                    @if(isset($testResults))
                        @foreach($testResults as $testName => $result)
                            <div class="test-result mb-3">
                                <h6>
                                    {{ucwords(str_replace('_', ' ', $testName))}}
                                    @if($result['success'] ?? false)
                                        <span class="badge badge-success">{{__('Passed')}}</span>
                                    @else
                                        <span class="badge badge-danger">{{__('Failed')}}</span>
                                    @endif
                                </h6>
                                @if(isset($result['error']))
                                    <div class="alert alert-danger">
                                        <strong>{{__('Error')}}:</strong> {{$result['error']}}
                                    </div>
                                @endif
                                @if(isset($result['details']))
                                    <div class="collapse" id="details-{{$loop->index}}">
                                        <div class="card card-body bg-light">
                                            <pre><code>{{json_encode($result['details'], JSON_PRETTY_PRINT)}}</code></pre>
                                        </div>
                                    </div>
                                    <button class="btn btn-sm btn-outline-secondary" type="button" data-toggle="collapse" data-target="#details-{{$loop->index}}">
                                        {{__('Show Details')}}
                                    </button>
                                @endif
                            </div>
                        @endforeach
                    @else
                        <p class="text-muted">{{__('No test results available. Click "Run Tests" to start.')}}</p>
                    @endif
                </div>
            </div>
        </div>

        <!-- TTL Configuration -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{{__('TTL Configuration')}}</h6>
            </div>
            <div class="card-body">
                @if(isset($cacheStats['ttl_config']))
                    <div class="row">
                        @foreach($cacheStats['ttl_config'] as $type => $ttl)
                            <div class="col-md-3 mb-3">
                                <strong>{{ucwords(str_replace('_', ' ', $type))}}:</strong><br>
                                <span class="badge badge-secondary">{{$ttl}} {{__('minutes')}}</span>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted">{{__('TTL configuration not available')}}</p>
                @endif
            </div>
        </div>
    </div>

    <script>
        function runCacheTest() {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> {{__("Running...")}}';
            button.disabled = true;

            $.post('{{route("ratehawk.admin.cache-test.test")}}', {
                _token: '{{csrf_token()}}'
            }).done(function(response) {
                if (response.success) {
                    updateTestResults(response.data);
                    showAlert('success', '{{__("Cache tests completed successfully")}}');
                } else {
                    showAlert('danger', response.message || '{{__("Test failed")}}');
                }
            }).fail(function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || '{{__("Test failed")}}');
            }).always(function() {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        function clearCache() {
            if (confirm('{{__("Are you sure you want to clear all cache?")}}')) {
                $.post('{{route("ratehawk.admin.cache-test.clear")}}', {
                    _token: '{{csrf_token()}}'
                }).done(function(response) {
                    if (response.success) {
                        showAlert('success', response.message);
                        refreshStats();
                    } else {
                        showAlert('danger', response.message || '{{__("Failed to clear cache")}}');
                    }
                }).fail(function(xhr) {
                    const response = xhr.responseJSON;
                    showAlert('danger', response?.message || '{{__("Failed to clear cache")}}');
                });
            }
        }

        function refreshStats() {
            $.get('{{route("ratehawk.admin.cache-test.stats")}}').done(function(response) {
                if (response.success) {
                    // Reload the page to show updated stats
                    location.reload();
                } else {
                    showAlert('danger', response.message || '{{__("Failed to refresh stats")}}');
                }
            }).fail(function(xhr) {
                const response = xhr.responseJSON;
                showAlert('danger', response?.message || '{{__("Failed to refresh stats")}}');
            });
        }

        function updateTestResults(results) {
            let html = '';
            Object.keys(results).forEach(function(testName, index) {
                const result = results[testName];
                const success = result.success || false;
                const badgeClass = success ? 'badge-success' : 'badge-danger';
                const badgeText = success ? '{{__("Passed")}}' : '{{__("Failed")}}';

                html += `
                    <div class="test-result mb-3">
                        <h6>
                            ${testName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            <span class="badge ${badgeClass}">${badgeText}</span>
                        </h6>
                `;

                if (result.error) {
                    html += `
                        <div class="alert alert-danger">
                            <strong>{{__('Error')}}:</strong> ${result.error}
                        </div>
                    `;
                }

                if (result.details) {
                    html += `
                        <div class="collapse" id="details-${index}">
                            <div class="card card-body bg-light">
                                <pre><code>${JSON.stringify(result.details, null, 2)}</code></pre>
                            </div>
                        </div>
                        <button class="btn btn-sm btn-outline-secondary" type="button" data-toggle="collapse" data-target="#details-${index}">
                            {{__('Show Details')}}
                        </button>
                    `;
                }

                html += '</div>';
            });

            $('#test-results').html(html);
        }

        function showAlert(type, message) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="close" data-dismiss="alert">
                        <span>&times;</span>
                    </button>
                </div>
            `;
            $('.container-fluid').prepend(alertHtml);
        }
    </script>
@endsection
