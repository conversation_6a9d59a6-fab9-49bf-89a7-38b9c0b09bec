<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for RateHawk module.
|
*/

Route::group(['prefix' => 'ratehawk'], function () {
    
    // Hotel Search API
    Route::group(['prefix' => 'search'], function () {
        Route::post('/region', 'Api\SearchController@searchByRegion')->name('ratehawk.api.search.region');
        Route::post('/hotels', 'Api\SearchController@searchByHotels')->name('ratehawk.api.search.hotels');
        Route::post('/coordinates', 'Api\SearchController@searchByCoordinates')->name('ratehawk.api.search.coordinates');
        Route::post('/suggest', 'Api\SearchController@suggest')->name('ratehawk.api.search.suggest');
    });
    
    // Hotel Details
    Route::group(['prefix' => 'hotel'], function () {
        Route::get('/{id}', 'Api\HotelController@show')->name('ratehawk.api.hotel.show');
        Route::post('/{id}/rates', 'Api\HotelController@getRates')->name('ratehawk.api.hotel.rates');
        Route::post('/prebook', 'Api\HotelController@prebook')->name('ratehawk.api.hotel.prebook');
    });
    
    // Booking API
    Route::group(['prefix' => 'booking'], function () {
        Route::get('/', 'Api\BookingController@index')->name('ratehawk.api.booking.index');
        Route::post('/create', 'Api\BookingController@create')->name('ratehawk.api.booking.create');
        Route::post('/start', 'Api\BookingController@start')->name('ratehawk.api.booking.start');
        Route::get('/{id}/status', 'Api\BookingController@status')->name('ratehawk.api.booking.status');
        Route::get('/{id}', 'Api\BookingController@show')->name('ratehawk.api.booking.show');
        Route::post('/{id}/cancel', 'Api\BookingController@cancel')->name('ratehawk.api.booking.cancel');
        Route::get('/{id}/voucher', 'Api\BookingController@voucher')->name('ratehawk.api.booking.voucher');
        Route::get('/{id}/invoice', 'Api\BookingController@invoice')->name('ratehawk.api.booking.invoice');
        Route::get('/local/list', 'Api\BookingController@localBookings')->name('ratehawk.api.booking.local');
        Route::post('/cc-token', 'Api\BookingController@createCreditCardToken')->name('ratehawk.api.booking.cc-token');
    });
    
    // Static Content
    Route::group(['prefix' => 'static'], function () {
        Route::get('/regions', 'Api\StaticController@regions')->name('ratehawk.api.static.regions');
        Route::post('/regions/search', 'Api\StaticController@searchRegions')->name('ratehawk.api.static.regions.search');
        Route::get('/regions/{id}', 'Api\StaticController@getRegion')->name('ratehawk.api.static.regions.show');
        Route::get('/hotels/dump', 'Api\StaticController@hotelsDump')->name('ratehawk.api.static.hotels-dump');
        Route::get('/endpoints', 'Api\StaticController@endpoints')->name('ratehawk.api.static.endpoints');
        Route::post('/cache/clear', 'Api\StaticController@clearCache')->name('ratehawk.api.static.cache.clear');
    });
    
    // Webhooks
    Route::post('/webhook/booking-status', 'Api\WebhookController@bookingStatus')->name('ratehawk.api.webhook.booking-status');
});
