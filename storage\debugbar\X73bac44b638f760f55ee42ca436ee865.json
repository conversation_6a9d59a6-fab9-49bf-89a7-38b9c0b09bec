{"__meta": {"id": "X73bac44b638f760f55ee42ca436ee865", "datetime": "2025-07-09 18:40:41", "utime": **********.811548, "method": "GET", "uri": "/admin/ratehawk", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752086440.76372, "end": **********.811569, "duration": 1.0478489398956299, "duration_str": "1.05s", "measures": [{"label": "Booting", "start": 1752086440.76372, "relative_start": 0, "end": **********.167749, "relative_end": **********.167749, "duration": 0.40402889251708984, "duration_str": "404ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.167784, "relative_start": 0.4040639400482178, "end": **********.811572, "relative_end": 3.0994415283203125e-06, "duration": 0.6437880992889404, "duration_str": "644ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 9229024, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 9, "templates": [{"name": "1x RateHawk::admin.index", "param_count": null, "params": [], "start": **********.405975, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules\\RateHawk/Views/admin/index.blade.phpRateHawk::admin.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FViews%2Fadmin%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "RateHawk::admin.index"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.410395, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x Layout::admin.app", "param_count": null, "params": [], "start": **********.411057, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/app.blade.phpLayout::admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.app"}, {"name": "1x Layout::admin.parts.global-script", "param_count": null, "params": [], "start": **********.41362, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/global-script.blade.phpLayout::admin.parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.global-script"}, {"name": "1x Layout::admin.parts.header", "param_count": null, "params": [], "start": **********.429367, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.phpLayout::admin.parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.header"}, {"name": "1x Layout::admin.parts.sidebar", "param_count": null, "params": [], "start": **********.451035, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.phpLayout::admin.parts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.sidebar"}, {"name": "1x Layout::admin.parts.bc", "param_count": null, "params": [], "start": **********.795594, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/bc.blade.phpLayout::admin.parts.bc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fbc.blade.php&line=1", "ajax": false, "filename": "bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.bc"}, {"name": "1x Media::browser", "param_count": null, "params": [], "start": **********.796975, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Media/Views/browser.blade.phpMedia::browser", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FViews%2Fbrowser.blade.php&line=1", "ajax": false, "filename": "browser.blade.php", "line": "?"}, "render_count": 1, "name_original": "Media::browser"}, {"name": "1x Ai::frontend.text-generate", "param_count": null, "params": [], "start": **********.801673, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules\\Ai/Views/frontend/text-generate.blade.phpAi::frontend.text-generate", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FAi%2FViews%2Ffrontend%2Ftext-generate.blade.php&line=1", "ajax": false, "filename": "text-generate.blade.php", "line": "?"}, "render_count": 1, "name_original": "Ai::frontend.text-generate"}]}, "route": {"uri": "GET admin/ratehawk", "middleware": "web, auth", "controller": "Modules\\RateHawk\\Admin\\RateHawkController@index", "namespace": "Modules\\RateHawk\\Admin", "prefix": "admin/ratehawk", "where": [], "as": "ratehawk.admin.index", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=36\" onclick=\"\">modules/RateHawk/Admin/RateHawkController.php:36-67</a>"}, "queries": {"nb_statements": 48, "nb_failed_statements": 0, "accumulated_duration": 0.03102999999999999, "accumulated_duration_str": "31.03ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.2050219, "duration": 0.00514, "duration_str": "5.14ms", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.2139351, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.219193, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.22389, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.228065, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.232161, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.240046, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.244641, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.250554, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.255248, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.259768, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.263919, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.269639, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.27458, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.280798, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.285953, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.2911692, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.295237, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.2998781, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.30526, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.309474, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.313616, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.32017, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.324388, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Authenticate.php", "line": 28}], "start": **********.342907, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.348501, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_bookings`", "type": "query", "params": [], "bindings": [], "hints": ["The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 206}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.354847, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:206", "source": "modules/RateHawk/Admin/RateHawkController.php:206", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=206", "ajax": false, "filename": "RateHawkController.php", "line": "206"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_bookings` where `created_at` >= '2025-07-09 00:00:00'", "type": "query", "params": [], "bindings": ["2025-07-09 00:00:00"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 207}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3586051, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:207", "source": "modules/RateHawk/Admin/RateHawkController.php:207", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=207", "ajax": false, "filename": "RateHawkController.php", "line": "207"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_bookings` where `created_at` >= '2025-07-01 00:00:00'", "type": "query", "params": [], "bindings": ["2025-07-01 00:00:00"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 208}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.362428, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:208", "source": "modules/RateHawk/Admin/RateHawkController.php:208", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=208", "ajax": false, "filename": "RateHawkController.php", "line": "208"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_bookings` where `status` = 'confirmed'", "type": "query", "params": [], "bindings": ["confirmed"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 209}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.366033, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:209", "source": "modules/RateHawk/Admin/RateHawkController.php:209", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=209", "ajax": false, "filename": "RateHawkController.php", "line": "209"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_bookings` where `status` = 'cancelled'", "type": "query", "params": [], "bindings": ["cancelled"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 210}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3712778, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:210", "source": "modules/RateHawk/Admin/RateHawkController.php:210", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=210", "ajax": false, "filename": "RateHawkController.php", "line": "210"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_bookings` where `status` = 'failed'", "type": "query", "params": [], "bindings": ["failed"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 211}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.375233, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:211", "source": "modules/RateHawk/Admin/RateHawkController.php:211", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=211", "ajax": false, "filename": "RateHawkController.php", "line": "211"}, "connection": "mazar_travel"}, {"sql": "select sum(`total_amount`) as aggregate from `ratehawk_bookings` where `status` = 'confirmed'", "type": "query", "params": [], "bindings": ["confirmed"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 212}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.379006, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:212", "source": "modules/RateHawk/Admin/RateHawkController.php:212", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=212", "ajax": false, "filename": "RateHawkController.php", "line": "212"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_api_logs` where `created_at` >= '2025-07-09 00:00:00'", "type": "query", "params": [], "bindings": ["2025-07-09 00:00:00"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 213}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.382975, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:213", "source": "modules/RateHawk/Admin/RateHawkController.php:213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=213", "ajax": false, "filename": "RateHawkController.php", "line": "213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `ratehawk_api_logs` where `created_at` >= '2025-07-09 00:00:00' and `status` = 'error'", "type": "query", "params": [], "bindings": ["2025-07-09 00:00:00", "error"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 214}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.387324, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:214", "source": "modules/RateHawk/Admin/RateHawkController.php:214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=214", "ajax": false, "filename": "RateHawkController.php", "line": "214"}, "connection": "mazar_travel"}, {"sql": "select avg(`duration`) as aggregate from `ratehawk_api_logs` where `created_at` >= '2025-07-09 00:00:00'", "type": "query", "params": [], "bindings": ["2025-07-09 00:00:00"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 215}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3912108, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:215", "source": "modules/RateHawk/Admin/RateHawkController.php:215", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=215", "ajax": false, "filename": "RateHawkController.php", "line": "215"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_bookings` order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.395556, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:48", "source": "modules/RateHawk/Admin/RateHawkController.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=48", "ajax": false, "filename": "RateHawkController.php", "line": "48"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_api_logs` order by `created_at` desc limit 10", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 53}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.399376, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "RateHawkController.php:53", "source": "modules/RateHawk/Admin/RateHawkController.php:53", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=53", "ajax": false, "filename": "RateHawkController.php", "line": "53"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.431013, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.435297, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.442383, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 132}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.44586, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_news` where `status` = 'pending' and `core_news`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/News/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\ModuleProvider.php", "line": 31}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.533607, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:31", "source": "modules/News/ModuleProvider.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModuleProvider.php&line=31", "ajax": false, "filename": "ModuleProvider.php", "line": "31"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `users` where `verify_submit_status` in ('new', 'partial') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["new", "partial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 407}, {"index": 17, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 47}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.540196, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "User.php:407", "source": "app/User.php:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=407", "ajax": false, "filename": "User.php", "line": "407"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `user_upgrade_request` where `status` = 'pending' and exists (select * from `users` where `user_upgrade_request`.`user_id` = `users`.`id` and `users`.`deleted_at` is null) and `user_upgrade_request`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 48}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.544234, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:48", "source": "modules/User/ModuleProvider.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=48", "ajax": false, "filename": "ModuleProvider.php", "line": "48"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `bravo_booking_payments` where `object_model` = 'plan' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["plan", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 93}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.549371, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:93", "source": "modules/User/ModuleProvider.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=93", "ajax": false, "filename": "ModuleProvider.php", "line": "93"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_booking_payments` where `object_model` = 'wallet_deposit' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["wallet_deposit", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Wallet/DepositPayment.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Wallet\\DepositPayment.php", "line": 15}, {"index": 17, "namespace": null, "name": "modules/Report/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Report\\ModuleProvider.php", "line": 18}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.554048, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "DepositPayment.php:15", "source": "modules/User/Models/Wallet/DepositPayment.php:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FWallet%2FDepositPayment.php&line=15", "ajax": false, "filename": "DepositPayment.php", "line": "15"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_payouts` where `status` = 'initial'", "type": "query", "params": [], "bindings": ["initial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Vendor/Models/VendorPayout.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\Models\\VendorPayout.php", "line": 63}, {"index": 17, "namespace": null, "name": "modules/Vendor/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\ModuleProvider.php", "line": 27}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.5587552, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "VendorPayout.php:63", "source": "modules/Vendor/Models/VendorPayout.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FModels%2FVendorPayout.php&line=63", "ajax": false, "filename": "VendorPayout.php", "line": "63"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\RateHawk\\Models\\RateHawkApiLog": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkApiLog.php&line=1", "ajax": false, "filename": "RateHawkApiLog.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/ratehawk\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "bc_current_currency": "", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/ratehawk", "status_code": "<pre class=sf-dump id=sf-dump-1709830438 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1709830438\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1892769357 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1892769357\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1683338402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1683338402\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://127.0.0.1:8000/admin/ratehawk/settings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2130 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik1zTzYzZmJLRE52MEg0S3duTExHbHc9PSIsInZhbHVlIjoia2ZEd3VOWDg1NzlLRUhWZFkvVVUwUnBOUTNMUzZRTnJqNW1BaEhHUWxhYlVtUXdtSkhQbDNlcTV5S3pZYTFHUnE5bEJHNjVFelUwV1pBaEJwYUFLanRUaTErUjhER3NYLzlXaHJiRTJnK29MVnU0SHd2bnhUWVFCMTJFKzdyaFkiLCJtYWMiOiIwZDY0MTEzNDc5YTA2M2FlZjA3MTE3ZWI4ZDY3Y2YzZDZlZjZjOWY1NzExNjlhY2Y1ZTRlMTMyODBjMWQ4ZWZlIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6Imw2b3J1Z1JEMGZhcFJUVTN5ZDdxa0E9PSIsInZhbHVlIjoiVG1IdXBSR0lSejlYVzR3UTEvWUpCdGJTTFBPM2daRkJuVTQxY3JWbHJmRU85T2x2WG0xWHhGMVJoa2NlZTB6Z01vbmdmSDFsNURYZ1YzaWp5QWw2VHpUR25zcjk3bHhEUEcwdlRUZUVaaE9VdVNmM241ZFNpODdPeW53N1U2NjgiLCJtYWMiOiIzMzYyNDE3NmQ4MjAzYjM2YjY0OWJmNDMyZjkxM2EyM2VlNDJiMzU0ZDQ3YzA5ZjdlZmI1ZmI3YWMwNDYwMzBjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1645355481 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ncJc6JL7D4OUdNqnMyW2JaSVePbveurWfiadoF7Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1645355481\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 18:40:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNkSnVzMnhwOHJ2ZXRneWM5ekFSRlE9PSIsInZhbHVlIjoiOTJwVUxJRWlDcWY0cURNK1RubW9BdTgrVHhwNXZ0U0l1aERiZ1MrWncwY1drMUFYTlpVRU5pbUxQdy9UTWtOVkxROEF1ZmppUFVQb0lGNTFIT0NwMlQ0ZDE5T1A0bnB4dDdWUmRvdHJYUWYvNUFWVVNDaG5MR1YwTWxyV0RtM1IiLCJtYWMiOiJjMzcwMTU5NzU5ZDRkNDQ3NGJkMmJkZGM4ODEwZDVmNGQ0YWY3NTUwNmVkYTRjOTQ0ZGE0YTcwMGFmOGNhMzEwIiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:40:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IlM4TzlPbTdnNFNFeTBYN1p0NEE1S2c9PSIsInZhbHVlIjoiK29YdEU2UXl1SDVDK0xTd2RiOWdJWkJpZjRNUkhnelZqWHBlaEVDV3ZGZElVQ2g2ckpJaUNtL0tKNWZXK2V6dWxBQjloNUlBZVJ1dkk3cXRlSVl5MWJ2cUdSYzdvRDNyQnhrSTJRc0xCK1l3Mkl2V3hXRFIxaDFwN1hZVk12VVYiLCJtYWMiOiI0YjQ5ZDgyZjQ0MjNjNzY3ODlmNDhjMzY2NDAyY2IzMzlhMWVkNzg2ODU5YWU5MjlkZTc1OTRiNzU4MTFmMWM4IiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:40:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNkSnVzMnhwOHJ2ZXRneWM5ekFSRlE9PSIsInZhbHVlIjoiOTJwVUxJRWlDcWY0cURNK1RubW9BdTgrVHhwNXZ0U0l1aERiZ1MrWncwY1drMUFYTlpVRU5pbUxQdy9UTWtOVkxROEF1ZmppUFVQb0lGNTFIT0NwMlQ0ZDE5T1A0bnB4dDdWUmRvdHJYUWYvNUFWVVNDaG5MR1YwTWxyV0RtM1IiLCJtYWMiOiJjMzcwMTU5NzU5ZDRkNDQ3NGJkMmJkZGM4ODEwZDVmNGQ0YWY3NTUwNmVkYTRjOTQ0ZGE0YTcwMGFmOGNhMzEwIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:40:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IlM4TzlPbTdnNFNFeTBYN1p0NEE1S2c9PSIsInZhbHVlIjoiK29YdEU2UXl1SDVDK0xTd2RiOWdJWkJpZjRNUkhnelZqWHBlaEVDV3ZGZElVQ2g2ckpJaUNtL0tKNWZXK2V6dWxBQjloNUlBZVJ1dkk3cXRlSVl5MWJ2cUdSYzdvRDNyQnhrSTJRc0xCK1l3Mkl2V3hXRFIxaDFwN1hZVk12VVYiLCJtYWMiOiI0YjQ5ZDgyZjQ0MjNjNzY3ODlmNDhjMzY2NDAyY2IzMzlhMWVkNzg2ODU5YWU5MjlkZTc1OTRiNzU4MTFmMWM4IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:40:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1675327769 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"36 characters\">http://127.0.0.1:8000/admin/ratehawk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1675327769\", {\"maxDepth\":0})</script>\n"}}