<?php

namespace Modules\RateHawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\Crypt;

class RateHawkSetting extends Model
{
    use HasFactory;

    protected $table = 'ratehawk_settings';

    protected $fillable = [
        'key',
        'value',
        'type',
        'description',
        'is_public',
        'is_encrypted',
    ];

    protected $casts = [
        'is_public' => 'boolean',
        'is_encrypted' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the value attribute with proper type casting and decryption
     */
    public function getValueAttribute($value)
    {
        // Decrypt if encrypted
        if ($this->is_encrypted && !empty($value)) {
            try {
                $value = Crypt::decryptString($value);
            } catch (\Exception $e) {
                // If decryption fails, return the original value
                return $value;
            }
        }

        // Cast to appropriate type
        return $this->castValue($value, $this->type);
    }

    /**
     * Set the value attribute with proper encryption
     */
    public function setValueAttribute($value)
    {
        // Convert value to string for storage
        $stringValue = $this->valueToString($value, $this->type);

        // Encrypt if needed
        if ($this->is_encrypted && !empty($stringValue)) {
            $stringValue = Crypt::encryptString($stringValue);
        }

        $this->attributes['value'] = $stringValue;
    }

    /**
     * Cast value to appropriate type
     */
    protected function castValue($value, string $type)
    {
        if (is_null($value) || $value === '') {
            return null;
        }

        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);
            case 'integer':
                return (int) $value;
            case 'float':
            case 'decimal':
                return (float) $value;
            case 'json':
                return json_decode($value, true);
            case 'array':
                return is_array($value) ? $value : json_decode($value, true);
            default:
                return $value;
        }
    }

    /**
     * Convert value to string for storage
     */
    protected function valueToString($value, string $type): string
    {
        if (is_null($value)) {
            return '';
        }

        switch ($type) {
            case 'boolean':
                return $value ? '1' : '0';
            case 'json':
            case 'array':
                return json_encode($value);
            default:
                return (string) $value;
        }
    }

    /**
     * Scope for public settings
     */
    public function scopePublic($query)
    {
        return $query->where('is_public', true);
    }

    /**
     * Scope for private settings
     */
    public function scopePrivate($query)
    {
        return $query->where('is_public', false);
    }

    /**
     * Scope for encrypted settings
     */
    public function scopeEncrypted($query)
    {
        return $query->where('is_encrypted', true);
    }

    /**
     * Get setting by key
     */
    public static function get(string $key, $default = null)
    {
        $setting = static::where('key', $key)->first();
        
        if (!$setting) {
            return $default;
        }

        return $setting->value;
    }

    /**
     * Set setting value
     */
    public static function set(string $key, $value, string $type = 'string', string $description = null): self
    {
        $setting = static::updateOrCreate(
            ['key' => $key],
            [
                'value' => $value,
                'type' => $type,
                'description' => $description,
            ]
        );

        return $setting;
    }

    /**
     * Get all public settings as key-value pairs
     */
    public static function getPublicSettings(): array
    {
        return static::public()
            ->get()
            ->pluck('value', 'key')
            ->toArray();
    }

    /**
     * Get all settings as key-value pairs
     */
    public static function getAllSettings(): array
    {
        return static::all()
            ->pluck('value', 'key')
            ->toArray();
    }

    /**
     * Check if setting exists
     */
    public static function has(string $key): bool
    {
        return static::where('key', $key)->exists();
    }

    /**
     * Delete setting by key
     */
    public static function forget(string $key): bool
    {
        return static::where('key', $key)->delete() > 0;
    }

    /**
     * Get settings by prefix
     */
    public static function getByPrefix(string $prefix): array
    {
        return static::where('key', 'like', $prefix . '%')
            ->get()
            ->pluck('value', 'key')
            ->toArray();
    }
}
