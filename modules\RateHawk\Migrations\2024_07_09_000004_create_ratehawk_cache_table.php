<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratehawk_cache', function (Blueprint $table) {
            $table->id();
            $table->string('cache_key')->unique()->index();
            $table->string('cache_type', 50)->index(); // search, hotel, region, etc.
            $table->longText('cache_data');
            $table->timestamp('expires_at')->index();
            $table->json('metadata')->nullable(); // Additional metadata about the cached data
            $table->timestamps();

            // Indexes for efficient cleanup and retrieval
            $table->index(['cache_type', 'expires_at']);
            $table->index(['expires_at', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratehawk_cache');
    }
};
