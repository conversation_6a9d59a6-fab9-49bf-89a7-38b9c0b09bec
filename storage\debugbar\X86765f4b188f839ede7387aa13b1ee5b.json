{"__meta": {"id": "X86765f4b188f839ede7387aa13b1ee5b", "datetime": "2025-07-09 18:02:51", "utime": **********.832955, "method": "POST", "uri": "/admin/ratehawk/test-connection", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[18:02:51] LOG.info: [RateHawk] API Request {\n    \"request_id\": \"rh_686eaecb187c38.05377045\",\n    \"method\": \"GET\",\n    \"endpoint\": \"\\/api\\/b2b\\/v3\\/overview\\/\",\n    \"data\": {\n        \"query\": []\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.133249, "xdebug_link": null, "collector": "log"}, {"message": "[18:02:51] LOG.info: [RateHawk] API Response {\n    \"request_id\": \"rh_686eaecb187c38.05377045\",\n    \"status_code\": 200,\n    \"duration\": 0.6749730110168457,\n    \"response_size\": 5471\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.77569, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752084170.42331, "end": **********.832993, "duration": 1.4096829891204834, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1752084170.42331, "relative_start": 0, "end": 1752084170.881142, "relative_end": 1752084170.881142, "duration": 0.45783185958862305, "duration_str": "458ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752084170.881195, "relative_start": 0.4578850269317627, "end": **********.832996, "relative_end": 2.86102294921875e-06, "duration": 0.9518008232116699, "duration_str": "952ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5936616, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/ratehawk/test-connection", "middleware": "web, dashboard, auth", "controller": "Modules\\RateHawk\\Admin\\RateHawkController@testConnection", "namespace": "Modules\\RateHawk\\Admin", "prefix": "admin/ratehawk", "where": [], "as": "ratehawk.admin.test-connection", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=167\" onclick=\"\">modules/RateHawk/Admin/RateHawkController.php:167-194</a>"}, "queries": {"nb_statements": 40, "nb_failed_statements": 0, "accumulated_duration": 0.05754, "accumulated_duration_str": "57.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.909547, "duration": 0.02564, "duration_str": "25.64ms", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.93932, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.946346, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.951966, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.956613, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.961778, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.9696438, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.974093, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.982265, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.987101, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.9919472, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": 1752084170.999115, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.003794, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.0080771, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.016776, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.0214732, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.026984, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.032778, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.037596, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.041995, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.048843, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.0533, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.059801, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.065711, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Authenticate.php", "line": 28}], "start": **********.086584, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.092552, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 158}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.101015, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 159}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.105572, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 162}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.111511, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 163}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.116852, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 166}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.1215851, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 167}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.126709, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "insert into `ratehawk_api_logs` (`request_id`, `method`, `endpoint`, `request_data`, `status`, `user_id`, `ip_address`, `user_agent`, `updated_at`, `created_at`) values ('rh_686eaecb187c38.05377045', 'GET', '/api/b2b/v3/overview/', '{\\\"query\\\":[]}', 'pending', 7, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-09 18:02:51', '2025-07-09 18:02:51')", "type": "query", "params": [], "bindings": ["rh_686eaecb187c38.05377045", "GET", "/api/b2b/v3/overview/", "{&quot;query&quot;:[]}", "pending", "7", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "2025-07-09 18:02:51", "2025-07-09 18:02:51"], "hints": [], "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 42}, {"index": 23, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 106}, {"index": 24, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 65}, {"index": 25, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 304}, {"index": 26, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 224}], "start": **********.135117, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "LoggingService.php:42", "source": "modules/RateHawk/Services/LoggingService.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FServices%2FLoggingService.php&line=42", "ajax": false, "filename": "LoggingService.php", "line": "42"}, "connection": "mazar_travel"}, {"sql": "update `ratehawk_api_logs` set `status_code` = 200, `response_data` = '{\\\"data\\\":[{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/info\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/create\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/delete\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/disable\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/edit\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/list\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/restore\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/ordergroup\\/document\\/invoice\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/document\\/info_invoice\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/document\\/single_act\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/create\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/list\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/incremental_reviews\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/info\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/info\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/info\\/incremental_dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/custom\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/status\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":false,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/form\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/cancel\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/document\\/voucher\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/info\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/reviews\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/poi\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/region\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/static\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/serp\\/hotels\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":150,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/hp\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":10,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/multicomplete\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/overview\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/serp\\/region\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":10,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/serp\\/geo\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":10,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/prebook\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60}],\\\"debug\\\":null,\\\"error\\\":null,\\\"status\\\":\\\"ok\\\"}', `duration` = 0.67497301101685, `status` = 'completed', `ratehawk_api_logs`.`updated_at` = '2025-07-09 18:02:51' where `request_id` = 'rh_686eaecb187c38.05377045'", "type": "query", "params": [], "bindings": ["200", "{&quot;data&quot;:[{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/info\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/create\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/delete\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/disable\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/edit\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/list\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/restore\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/ordergroup\\/document\\/invoice\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/document\\/info_invoice\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/document\\/single_act\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/create\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/list\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/incremental_reviews\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/info\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/info\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/info\\/incremental_dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/custom\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/status\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:false,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/form\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/cancel\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/document\\/voucher\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/info\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/reviews\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/poi\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/region\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/static\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/serp\\/hotels\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:150,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/hp\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:10,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/multicomplete\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/overview\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/serp\\/region\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:10,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/serp\\/geo\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:10,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/prebook\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60}],&quot;debug&quot;:null,&quot;error&quot;:null,&quot;status&quot;:&quot;ok&quot;}", "0.67497301101685", "completed", "2025-07-09 18:02:51", "rh_686eaecb187c38.05377045"], "hints": [], "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 78}, {"index": 13, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 116}, {"index": 14, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 65}, {"index": 15, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 304}, {"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 224}], "start": **********.777127, "duration": 0.00546, "duration_str": "5.46ms", "memory": 0, "memory_str": null, "filename": "LoggingService.php:78", "source": "modules/RateHawk/Services/LoggingService.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FServices%2FLoggingService.php&line=78", "ajax": false, "filename": "LoggingService.php", "line": "78"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 158}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": **********.7914288, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 159}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": **********.798957, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 162}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": **********.8058739, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 163}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": **********.811492, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 166}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": **********.817656, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 167}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": **********.822153, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "bc_current_currency": "", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/ratehawk/test-connection", "status_code": "<pre class=sf-dump id=sf-dump-1633043524 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1633043524\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1691281532 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1691281532\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-557210693 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-557210693\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-99702751 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/admin/module/core/settings/index/ratehawk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2130 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImpTMFZiblgrWXY4M1FCaUxidmNPYUE9PSIsInZhbHVlIjoiWWw4eEpjN1FER3BzTERYUmtpOXdYZDJFQlAzeDdhdVlOWk5Ic1JTamhoMUFYbTArbnJ4Zk9hVmFhcXJzU1ZMQUl3Y2t6cnZuVXNBUU1RWHRWWEM1aFBQa3Ryd2xNQzlvdGdNaHA2elNXYytubXQ2ekgrejVLcEk1TndhK29OeEQiLCJtYWMiOiI2ZWEwY2FlMGJiMWRkYjA2ZjM2MTA4NzJiNzg5N2VjYzIyNjI4ZDI5ZjAyZDNlOTM2MDQ4NjhlZWJiNmIxNTA4IiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6Ik1oaGxGM1NnRUtNVXNJVUFGeWk2Wnc9PSIsInZhbHVlIjoia1JIcDdleTVyNjh0WFg4QVA4V3IwQzFNaUJXVWw2VnBTNGgrYTV4LzBBaHNSckNiMzZSNytLZXZXZk5HRVJFS0M1eGNSYm9ReFdrK2lYcUluekR3dWNlREVHdFhML3RyNmowS1hOYUtzVkNDdEdUaVQzRFRlbW41eGdZK1NnRnkiLCJtYWMiOiIxYjliNmZlMjc0MzMzMjEwNjZjMjIzMjYzNTdjMmQ0MTM1YmFlYjE0YWY0OGQ3ZDA5Zjg5YzBhNjQwMmI0OTI2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-99702751\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ncJc6JL7D4OUdNqnMyW2JaSVePbveurWfiadoF7Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 18:02:51 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkFJdlB3WkRZR3lTYWlvLzk2MWkwRHc9PSIsInZhbHVlIjoiWU0zZUNCbVFubGtSYVE2cWVDTmxiRnFMR29oWlVncHFYSkpNNzg5TkVFV1BMSmRKREc4c1JuTk1TcmJjWk05OTNCQkFPRkVkTGRYWk4vZXhzbXRJcUMwNVhZY0p5NWY2alRlZlpFY0xCQVZnSjZYTHhodk9TZHhIbTFTbWdPQ3YiLCJtYWMiOiJmMWFiYzkyNWUzYzhkZGVhNDE1NzNlNWQwNWYyNjcwMzAxYjczMmRkOTc5ZDU4NDI2OTY3NDlhOTBlODNiNWMwIiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:02:51 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6Ik1NQjU2eGlrTHlURDh0ckdRY014Tnc9PSIsInZhbHVlIjoiN3BBOTIwNGNJOFY2b2dmK2t6aHJhclFydk9mTEtYSWZsS2luN24yK2NKbVVrRmxDV0IwSExkZE5zUEh3TVBtdnA2N01RZmhzeXpuVWlNSXJmZkdLOStGZmdJYnNDQTg1OTJjWGUzT2tYVXZKT3ZZVDRGejI0ZmQzS3JlS3BaRnciLCJtYWMiOiI0ODllNDNhMDcxNzVkNWQzNzY0YWY5MmVmYTczOWFiNzM3ODNkZjA5NmE2NTRhMmQ3Y2Y5NDk3NTc4NjlkOGYxIiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:02:51 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkFJdlB3WkRZR3lTYWlvLzk2MWkwRHc9PSIsInZhbHVlIjoiWU0zZUNCbVFubGtSYVE2cWVDTmxiRnFMR29oWlVncHFYSkpNNzg5TkVFV1BMSmRKREc4c1JuTk1TcmJjWk05OTNCQkFPRkVkTGRYWk4vZXhzbXRJcUMwNVhZY0p5NWY2alRlZlpFY0xCQVZnSjZYTHhodk9TZHhIbTFTbWdPQ3YiLCJtYWMiOiJmMWFiYzkyNWUzYzhkZGVhNDE1NzNlNWQwNWYyNjcwMzAxYjczMmRkOTc5ZDU4NDI2OTY3NDlhOTBlODNiNWMwIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:02:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6Ik1NQjU2eGlrTHlURDh0ckdRY014Tnc9PSIsInZhbHVlIjoiN3BBOTIwNGNJOFY2b2dmK2t6aHJhclFydk9mTEtYSWZsS2luN24yK2NKbVVrRmxDV0IwSExkZE5zUEh3TVBtdnA2N01RZmhzeXpuVWlNSXJmZkdLOStGZmdJYnNDQTg1OTJjWGUzT2tYVXZKT3ZZVDRGejI0ZmQzS3JlS3BaRnciLCJtYWMiOiI0ODllNDNhMDcxNzVkNWQzNzY0YWY5MmVmYTczOWFiNzM3ODNkZjA5NmE2NTRhMmQ3Y2Y5NDk3NTc4NjlkOGYxIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:02:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1402570246 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1402570246\", {\"maxDepth\":0})</script>\n"}}