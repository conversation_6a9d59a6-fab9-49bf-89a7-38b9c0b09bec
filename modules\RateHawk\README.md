# RateHawk API Integration Module

A comprehensive Laravel module for integrating with the RateHawk B2B API, providing hotel search, booking management, and administrative features.

## Features

- **Hotel Search**: Search hotels by region, coordinates, or specific hotel IDs
- **Booking Management**: Create, manage, and track hotel bookings
- **Real-time Updates**: Webhook support for booking status updates
- **Admin Interface**: Complete admin panel for configuration and monitoring
- **API Logging**: Comprehensive logging and debugging capabilities
- **Error Handling**: Robust error handling with detailed reporting
- **Caching**: Intelligent caching for improved performance
- **Rate Limiting**: Built-in rate limiting to respect API quotas

## Installation

### Requirements

- PHP 8.0 or higher
- Laravel 8.0 or higher
- MySQL 5.7 or higher
- cURL extension
- JSON extension
- OpenSSL extension

### Setup

1. **Install the module** (if using a package manager or manual installation)

2. **Run migrations**:
   ```bash
   php artisan migrate
   ```

3. **Publish configuration** (if needed):
   ```bash
   php artisan vendor:publish --tag=ratehawk-config
   ```

4. **Configure API credentials** in the admin panel or environment variables:
   ```env
   RATEHAWK_ENABLE=true
   RATEHAWK_ENVIRONMENT=test
   RATEHAWK_KEY_ID=your_key_id
   RATEHAWK_API_KEY=your_api_key
   ```

## Configuration

### Admin Configuration

Access the admin panel at `/admin/ratehawk` to configure:

- API credentials (Key ID and API Key)
- Environment (test/production)
- Default search parameters (currency, language, residency)
- Caching settings
- Logging preferences
- Webhook configuration

### Environment Variables

```env
# Basic Configuration
RATEHAWK_ENABLE=true
RATEHAWK_ENVIRONMENT=test
RATEHAWK_KEY_ID=your_key_id
RATEHAWK_API_KEY=your_api_key

# Default Settings
RATEHAWK_DEFAULT_CURRENCY=USD
RATEHAWK_DEFAULT_LANGUAGE=en
RATEHAWK_DEFAULT_RESIDENCY=us

# Performance Settings
RATEHAWK_CACHE_ENABLED=true
RATEHAWK_LOGGING_ENABLED=true

# Webhook Settings
RATEHAWK_WEBHOOKS_ENABLED=true
RATEHAWK_WEBHOOK_SECRET=your_webhook_secret
```

## Usage

### Hotel Search

#### Search by Region

```php
use Modules\RateHawk\Services\HotelSearchService;

$searchService = app(HotelSearchService::class);

$params = [
    'checkin' => '2024-08-01',
    'checkout' => '2024-08-05',
    'region_id' => 6176, // Paris
    'guests' => [
        ['adults' => 2, 'children' => []]
    ],
    'currency' => 'USD',
    'language' => 'en',
    'residency' => 'us'
];

$results = $searchService->searchByRegion($params);
```

#### Search by Coordinates

```php
$params = [
    'checkin' => '2024-08-01',
    'checkout' => '2024-08-05',
    'latitude' => 48.8566,
    'longitude' => 2.3522,
    'radius' => 5, // km
    'guests' => [
        ['adults' => 2, 'children' => []]
    ]
];

$results = $searchService->searchByCoordinates($params);
```

#### Get Hotel Details

```php
use Modules\RateHawk\Services\StaticContentService;

$staticService = app(StaticContentService::class);

$hotelData = $staticService->getHotelStaticData('hotel_id', [
    'language' => 'en'
]);
```

### Booking Management

#### Create Booking

```php
use Modules\RateHawk\Services\BookingService;

$bookingService = app(BookingService::class);

// Step 1: Create booking process
$bookingParams = [
    'search_hash' => 'search_hash_from_search_results',
    'match_hash' => 'match_hash_from_selected_rate',
    'language' => 'en',
    'user_ip' => request()->ip()
];

$booking = $bookingService->createBookingProcess($bookingParams);

// Step 2: Start booking with guest details
$startParams = [
    'order_id' => $booking['data']['order_id'],
    'book_hash' => $booking['data']['book_hash'],
    'guests' => [
        [
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>'
        ]
    ],
    'payment_type' => 'deposit'
];

$result = $bookingService->startBookingProcess($startParams);
```

#### Check Booking Status

```php
$status = $bookingService->checkBookingStatus($orderId);
```

#### Cancel Booking

```php
$result = $bookingService->cancelBooking($orderId);
```

### API Endpoints

#### Public API Endpoints

```
POST /api/ratehawk/search/region          - Search hotels by region
POST /api/ratehawk/search/hotels          - Search specific hotels
POST /api/ratehawk/search/coordinates     - Search by coordinates
POST /api/ratehawk/suggest                - Get hotel/region suggestions
GET  /api/ratehawk/static/regions         - Get regions list
GET  /api/ratehawk/hotels/{id}            - Get hotel details
POST /api/ratehawk/hotels/{id}/rates      - Get hotel rates
POST /api/ratehawk/prebook                - Prebook a rate
```

#### Booking API Endpoints

```
POST /api/ratehawk/booking/create         - Create booking
POST /api/ratehawk/booking/start          - Start booking process
GET  /api/ratehawk/booking/{id}/status    - Check booking status
POST /api/ratehawk/booking/{id}/cancel    - Cancel booking
GET  /api/ratehawk/booking/{id}/voucher   - Get booking voucher
GET  /api/ratehawk/booking/{id}/invoice   - Get booking invoice
```

#### Admin API Endpoints

```
GET  /api/admin/ratehawk/dashboard/stats  - Get dashboard statistics
GET  /api/admin/ratehawk/bookings         - List bookings
GET  /api/admin/ratehawk/logs             - List API logs
GET  /api/admin/ratehawk/settings         - Get settings
POST /api/admin/ratehawk/settings         - Update settings
```

### Frontend Integration

#### JavaScript Example

```javascript
// Search hotels
async function searchHotels() {
    const response = await fetch('/api/ratehawk/search/region', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            checkin: '2024-08-01',
            checkout: '2024-08-05',
            region_id: 6176,
            guests: [{ adults: 2, children: [] }]
        })
    });
    
    const data = await response.json();
    if (data.success) {
        displaySearchResults(data.data);
    }
}

// Create booking
async function createBooking(searchHash, matchHash) {
    const response = await fetch('/api/ratehawk/booking/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            search_hash: searchHash,
            match_hash: matchHash,
            user_ip: userIP
        })
    });
    
    return await response.json();
}
```

## Database Schema

### Tables

- **ratehawk_bookings**: Store booking information
- **ratehawk_api_logs**: Log all API requests and responses
- **ratehawk_settings**: Store configuration settings
- **ratehawk_cache**: Custom cache storage (optional)

### Key Fields

#### Bookings Table
- `order_id`: RateHawk order identifier
- `status`: Booking status (created, confirmed, cancelled, etc.)
- `hotel_data`: JSON field with hotel information
- `guest_data`: JSON field with guest information
- `price_data`: JSON field with pricing information

#### API Logs Table
- `request_id`: Unique request identifier
- `method`: HTTP method (GET, POST, etc.)
- `endpoint`: API endpoint called
- `status`: Request status (pending, completed, error)
- `duration`: Response time in seconds

## Error Handling

The module includes comprehensive error handling:

### Exception Types

- `RateHawkApiException`: General API errors
- `RateHawkAuthException`: Authentication failures
- `RateHawkRateLimitException`: Rate limit exceeded

### Error Logging

All errors are logged with:
- Error type and message
- Request context
- Stack trace
- User information
- Timestamp

### Error Recovery

- Automatic retry for transient errors
- Graceful degradation for non-critical failures
- User-friendly error messages

## Monitoring and Debugging

### Admin Dashboard

Access comprehensive monitoring at `/admin/ratehawk`:

- Real-time statistics
- Recent bookings and API calls
- Error rates and performance metrics
- Configuration status

### API Testing

Built-in API testing interface at `/admin/ratehawk/api-test`:

- Test API connectivity
- Validate search functionality
- Check endpoint availability
- Clear cache and logs

### Logging

Detailed logging includes:
- All API requests and responses
- Booking operations
- Configuration changes
- Error occurrences
- Performance metrics

## Webhooks

### Setup

Configure webhook URL in RateHawk partner panel:
```
https://yourdomain.com/api/ratehawk/webhook/booking-status
```

### Security

Webhooks are secured with:
- HMAC signature verification
- IP address validation (optional)
- Request rate limiting

### Handling

Webhook events automatically:
- Update booking status
- Trigger notifications
- Log all webhook activity

## Performance Optimization

### Caching

Intelligent caching for:
- Search results (5 minutes)
- Hotel static data (1 hour)
- Regions data (24 hours)

### Rate Limiting

Built-in rate limiting:
- Search: 10 requests per minute
- Booking: 5 requests per minute
- General: 100 requests per hour

### Database Optimization

- Indexed fields for fast queries
- Automatic log cleanup
- Optimized JSON field usage

## Security

### API Security

- Encrypted credential storage
- Secure HTTP communication
- Request signature validation

### Data Protection

- Sensitive data redaction in logs
- Secure webhook handling
- User data encryption

## Support

### Documentation

- API reference documentation
- Integration examples
- Troubleshooting guides

### Debugging

- Comprehensive error reporting
- Debug mode with detailed logging
- API connectivity testing

### Monitoring

- Real-time performance metrics
- Error rate monitoring
- Usage analytics

## License

This module is proprietary software. All rights reserved.
