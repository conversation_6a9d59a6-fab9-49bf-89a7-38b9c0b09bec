<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class BookingController extends Controller
{
    /**
     * Process a hotel booking request
     */
    public function processBooking(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'hotel' => 'required|array',
                'hotel.name' => 'required|string',
                'hotel.location' => 'nullable|string',
                'hotel.price' => 'nullable|numeric',
                'hotel.currency' => 'nullable|string',
                'search_params' => 'required|array',
                'search_params.destination' => 'required|string',
                'search_params.checkin' => 'required|date',
                'search_params.checkout' => 'required|date|after:search_params.checkin',
                'search_params.adults' => 'required|integer|min:1',
                'search_params.children' => 'nullable|integer|min:0',
                'search_params.rooms' => 'required|integer|min:1',
                'guest_info' => 'required|array',
                'guest_info.name' => 'required|string|max:255',
                'guest_info.email' => 'required|email|max:255',
                'guest_info.phone' => 'required|string|max:20',
                'guest_info.special_requests' => 'nullable|string|max:1000',
            ]);

            // Generate booking reference
            $bookingReference = 'RH-' . strtoupper(Str::random(8));

            // Calculate booking details
            $checkinDate = new \DateTime($validatedData['search_params']['checkin']);
            $checkoutDate = new \DateTime($validatedData['search_params']['checkout']);
            $nights = $checkinDate->diff($checkoutDate)->days;
            
            $totalPrice = 0;
            if (isset($validatedData['hotel']['price'])) {
                $totalPrice = $validatedData['hotel']['price'] * $nights * $validatedData['search_params']['rooms'];
            }

            // Create booking record (you would save this to database)
            $bookingData = [
                'reference' => $bookingReference,
                'hotel' => $validatedData['hotel'],
                'search_params' => $validatedData['search_params'],
                'guest_info' => $validatedData['guest_info'],
                'nights' => $nights,
                'total_price' => $totalPrice,
                'currency' => $validatedData['hotel']['currency'] ?? 'USD',
                'status' => 'pending',
                'created_at' => now(),
            ];

            // Log the booking for now (in production, save to database)
            Log::info('New booking request', $bookingData);

            // Send confirmation email (implement this based on your email setup)
            $this->sendBookingConfirmation($bookingData);

            return response()->json([
                'success' => true,
                'message' => __('Booking request submitted successfully!'),
                'data' => [
                    'booking_reference' => $bookingReference,
                    'total_price' => $totalPrice,
                    'currency' => $validatedData['hotel']['currency'] ?? 'USD',
                    'nights' => $nights,
                ]
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('Invalid booking data'),
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Booking processing error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Failed to process booking'),
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get booking details by reference
     */
    public function getBooking(Request $request, string $reference): JsonResponse
    {
        try {
            // In production, fetch from database
            // For now, return sample data
            $booking = [
                'reference' => $reference,
                'status' => 'confirmed',
                'hotel' => [
                    'name' => 'Sample Hotel',
                    'location' => 'Sample Location',
                ],
                'guest_info' => [
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                ],
                'created_at' => now()->toISOString(),
            ];

            return response()->json([
                'success' => true,
                'data' => $booking
            ]);

        } catch (\Exception $e) {
            Log::error('Booking retrieval error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Booking not found')
            ], 404);
        }
    }

    /**
     * Cancel a booking
     */
    public function cancelBooking(Request $request, string $reference): JsonResponse
    {
        try {
            $request->validate([
                'reason' => 'nullable|string|max:500'
            ]);

            // In production, update booking status in database
            Log::info('Booking cancellation request', [
                'reference' => $reference,
                'reason' => $request->input('reason'),
                'cancelled_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => __('Booking cancelled successfully')
            ]);

        } catch (\Exception $e) {
            Log::error('Booking cancellation error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Failed to cancel booking')
            ], 500);
        }
    }

    /**
     * Send booking confirmation email
     */
    private function sendBookingConfirmation(array $bookingData): void
    {
        try {
            // In production, implement proper email sending
            // For now, just log the email content
            $emailContent = [
                'to' => $bookingData['guest_info']['email'],
                'subject' => 'Booking Confirmation - ' . $bookingData['reference'],
                'content' => [
                    'booking_reference' => $bookingData['reference'],
                    'hotel_name' => $bookingData['hotel']['name'],
                    'guest_name' => $bookingData['guest_info']['name'],
                    'checkin' => $bookingData['search_params']['checkin'],
                    'checkout' => $bookingData['search_params']['checkout'],
                    'nights' => $bookingData['nights'],
                    'total_price' => $bookingData['total_price'],
                    'currency' => $bookingData['currency'],
                ]
            ];

            Log::info('Booking confirmation email', $emailContent);

            // Uncomment and implement when email is set up:
            // Mail::send('emails.booking-confirmation', $emailContent['content'], function ($message) use ($emailContent) {
            //     $message->to($emailContent['to'])
            //             ->subject($emailContent['subject']);
            // });

        } catch (\Exception $e) {
            Log::error('Failed to send booking confirmation email: ' . $e->getMessage());
        }
    }

    /**
     * Get user's booking history (public access for email-based lookup)
     */
    public function getBookingHistory(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'email' => 'required|email'
            ]);

            $email = $request->input('email');

            // In production, fetch from database based on email
            // For demo purposes, return sample data for any email
            $bookings = [
                [
                    'reference' => 'RH-ABC12345',
                    'hotel_name' => 'Sample Hotel Paris',
                    'location' => 'Paris, France',
                    'checkin' => '2025-08-15',
                    'checkout' => '2025-08-17',
                    'status' => 'confirmed',
                    'total_price' => 250.00,
                    'currency' => 'USD',
                    'created_at' => '2025-07-10T10:00:00Z'
                ],
                [
                    'reference' => 'RH-XYZ67890',
                    'hotel_name' => 'Grand Hotel London',
                    'location' => 'London, UK',
                    'checkin' => '2025-09-01',
                    'checkout' => '2025-09-03',
                    'status' => 'pending',
                    'total_price' => 180.00,
                    'currency' => 'USD',
                    'created_at' => '2025-07-09T15:30:00Z'
                ]
            ];

            // Filter bookings for the specific email (in production, this would be a database query)
            // For demo, return bookings for any valid email
            if (filter_var($email, FILTER_VALIDATE_EMAIL)) {
                return response()->json([
                    'success' => true,
                    'data' => $bookings
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => []
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => __('Invalid email address'),
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
            Log::error('Booking history error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to retrieve booking history')
            ], 500);
        }
    }
}
