@extends('admin.layouts.app')

@section('content')
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">{{ $page_title }}</h1>
        <div class="btn-group">
            <button type="button" class="btn btn-info btn-sm" id="test-connection">
                <i class="fas fa-plug"></i> {{ __('Test Connection') }}
            </button>
            <a href="{{ route('ratehawk.admin.index') }}" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> {{ __('Back to Dashboard') }}
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('RateHawk API Configuration') }}</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('ratehawk.admin.settings.save') }}" id="settings-form">
                        @csrf

                        <!-- Enable/Disable -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_enable" 
                                       name="ratehawk_enable" value="1" 
                                       {{ ($settings['ratehawk_enable']->value ?? false) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="ratehawk_enable">
                                    {{ __('Enable RateHawk API Integration') }}
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                {{ __('Enable or disable the RateHawk API integration') }}
                            </small>
                        </div>

                        <hr>

                        <!-- API Credentials -->
                        <h6 class="text-primary mb-3">{{ __('API Credentials') }}</h6>
                        
                        <div class="form-group">
                            <label for="ratehawk_environment">{{ __('Environment') }}</label>
                            <select class="form-control" id="ratehawk_environment" name="ratehawk_environment">
                                <option value="test" {{ ($settings['ratehawk_environment']->value ?? 'test') === 'test' ? 'selected' : '' }}>
                                    {{ __('Test/Sandbox') }}
                                </option>
                                <option value="production" {{ ($settings['ratehawk_environment']->value ?? 'test') === 'production' ? 'selected' : '' }}>
                                    {{ __('Production') }}
                                </option>
                            </select>
                            <small class="form-text text-muted">
                                {{ __('Select the API environment to use') }}
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="ratehawk_key_id">{{ __('API Key ID') }}</label>
                            <input type="text" class="form-control" id="ratehawk_key_id" name="ratehawk_key_id" 
                                   value="{{ $settings['ratehawk_key_id']->value ?? '' }}" placeholder="{{ __('Enter your API Key ID') }}">
                            <small class="form-text text-muted">
                                {{ __('Your RateHawk API Key ID') }}
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="ratehawk_api_key">{{ __('API Key') }}</label>
                            <input type="password" class="form-control" id="ratehawk_api_key" name="ratehawk_api_key" 
                                   value="{{ $settings['ratehawk_api_key']->value ?? '' }}" placeholder="{{ __('Enter your API Key') }}">
                            <small class="form-text text-muted">
                                {{ __('Your RateHawk API Key (will be encrypted when saved)') }}
                            </small>
                        </div>

                        <hr>

                        <!-- Default Settings -->
                        <h6 class="text-primary mb-3">{{ __('Default Settings') }}</h6>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ratehawk_default_currency">{{ __('Default Currency') }}</label>
                                    <select class="form-control" id="ratehawk_default_currency" name="ratehawk_default_currency">
                                        @foreach($supported_currencies as $currency)
                                            <option value="{{ $currency }}" 
                                                {{ ($settings['ratehawk_default_currency']->value ?? 'USD') === $currency ? 'selected' : '' }}>
                                                {{ $currency }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ratehawk_default_language">{{ __('Default Language') }}</label>
                                    <select class="form-control" id="ratehawk_default_language" name="ratehawk_default_language">
                                        @foreach($supported_languages as $code => $name)
                                            <option value="{{ $code }}" 
                                                {{ ($settings['ratehawk_default_language']->value ?? 'en') === $code ? 'selected' : '' }}>
                                                {{ $name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ratehawk_default_residency">{{ __('Default Residency') }}</label>
                                    <input type="text" class="form-control" id="ratehawk_default_residency" 
                                           name="ratehawk_default_residency" maxlength="2" 
                                           value="{{ $settings['ratehawk_default_residency']->value ?? 'us' }}" 
                                           placeholder="us">
                                    <small class="form-text text-muted">
                                        {{ __('2-letter country code') }}
                                    </small>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Advanced Settings -->
                        <h6 class="text-primary mb-3">{{ __('Advanced Settings') }}</h6>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_cache_enabled" 
                                       name="ratehawk_cache_enabled" value="1" 
                                       {{ ($settings['ratehawk_cache_enabled']->value ?? true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="ratehawk_cache_enabled">
                                    {{ __('Enable Caching') }}
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                {{ __('Cache API responses to improve performance') }}
                            </small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_logging_enabled" 
                                       name="ratehawk_logging_enabled" value="1" 
                                       {{ ($settings['ratehawk_logging_enabled']->value ?? true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="ratehawk_logging_enabled">
                                    {{ __('Enable API Logging') }}
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                {{ __('Log all API requests and responses for debugging') }}
                            </small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_webhooks_enabled" 
                                       name="ratehawk_webhooks_enabled" value="1" 
                                       {{ ($settings['ratehawk_webhooks_enabled']->value ?? true) ? 'checked' : '' }}>
                                <label class="custom-control-label" for="ratehawk_webhooks_enabled">
                                    {{ __('Enable Webhooks') }}
                                </label>
                            </div>
                            <small class="form-text text-muted">
                                {{ __('Receive booking status updates via webhooks') }}
                            </small>
                        </div>

                        <div class="form-group">
                            <label for="ratehawk_webhook_secret">{{ __('Webhook Secret') }}</label>
                            <input type="password" class="form-control" id="ratehawk_webhook_secret" 
                                   name="ratehawk_webhook_secret" 
                                   value="{{ $settings['ratehawk_webhook_secret']->value ?? '' }}" 
                                   placeholder="{{ __('Enter webhook secret for signature verification') }}">
                            <small class="form-text text-muted">
                                {{ __('Secret key for webhook signature verification (optional but recommended)') }}
                            </small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {{ __('Save Settings') }}
                            </button>
                            <button type="button" class="btn btn-secondary" onclick="window.location.reload()">
                                <i class="fas fa-undo"></i> {{ __('Reset') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('Connection Status') }}</h6>
                </div>
                <div class="card-body">
                    <div id="connection-status">
                        <p class="text-muted">{{ __('Click "Test Connection" to check API connectivity') }}</p>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">{{ __('Quick Links') }}</h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="{{ route('ratehawk.admin.api-test.index') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-flask"></i> {{ __('API Testing') }}
                        </a>
                        <a href="{{ route('ratehawk.admin.bookings.index') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-check"></i> {{ __('View Bookings') }}
                        </a>
                        <a href="{{ route('ratehawk.admin.logs.index') }}" class="list-group-item list-group-item-action">
                            <i class="fas fa-list"></i> {{ __('API Logs') }}
                        </a>
                        <a href="https://docs.emergingtravel.com/docs/b2b-api/" target="_blank" class="list-group-item list-group-item-action">
                            <i class="fas fa-external-link-alt"></i> {{ __('API Documentation') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    $('#test-connection').click(function() {
        var btn = $(this);
        var originalText = btn.html();
        
        btn.html('<i class="fas fa-spinner fa-spin"></i> {{ __("Testing...") }}').prop('disabled', true);
        
        $.ajax({
            url: '{{ route("ratehawk.admin.test-connection") }}',
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                if (response.success) {
                    $('#connection-status').html(
                        '<div class="alert alert-success">' +
                        '<i class="fas fa-check-circle"></i> ' + response.message +
                        '</div>'
                    );
                } else {
                    $('#connection-status').html(
                        '<div class="alert alert-danger">' +
                        '<i class="fas fa-times-circle"></i> ' + response.message +
                        '</div>'
                    );
                }
            },
            error: function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '{{ __("Connection test failed") }}';
                $('#connection-status').html(
                    '<div class="alert alert-danger">' +
                    '<i class="fas fa-times-circle"></i> ' + message +
                    '</div>'
                );
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
@endpush
