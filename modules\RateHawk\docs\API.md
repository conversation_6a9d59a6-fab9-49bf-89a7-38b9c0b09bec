# RateHawk API Documentation

## Overview

The RateHawk module provides RESTful API endpoints for hotel search, booking management, and administrative functions.

## Authentication

### Public Endpoints
Most search endpoints are publicly accessible but may require CSRF tokens for POST requests.

### Admin Endpoints
Admin endpoints require authentication and appropriate permissions:
- `ratehawk_view`: View bookings and logs
- `ratehawk_manage`: Manage settings and perform admin actions
- `ratehawk_settings`: Modify configuration settings

## Base URLs

- **Public API**: `/api/ratehawk/`
- **Admin API**: `/api/admin/ratehawk/`
- **Web Interface**: `/ratehawk/`
- **Admin Interface**: `/admin/ratehawk/`

## Response Format

All API responses follow this format:

```json
{
    "success": true,
    "message": "Operation completed successfully",
    "data": {
        // Response data
    },
    "timestamp": "2024-07-09T12:00:00.000000Z"
}
```

Error responses:

```json
{
    "success": false,
    "message": "Error description",
    "error_details": {
        // Additional error information
    },
    "timestamp": "2024-07-09T12:00:00.000000Z"
}
```

## Hotel Search Endpoints

### Search Hotels by Region

**POST** `/api/ratehawk/search/region`

Search for hotels in a specific region.

#### Request Body

```json
{
    "checkin": "2024-08-01",
    "checkout": "2024-08-05",
    "region_id": 6176,
    "guests": [
        {
            "adults": 2,
            "children": [8, 12]
        }
    ],
    "currency": "USD",
    "language": "en",
    "residency": "us",
    "hotels_limit": 50
}
```

#### Parameters

- `checkin` (required): Check-in date (YYYY-MM-DD)
- `checkout` (required): Check-out date (YYYY-MM-DD)
- `region_id` (required): RateHawk region ID
- `guests` (required): Array of guest configurations
- `currency` (optional): Currency code (default: USD)
- `language` (optional): Language code (default: en)
- `residency` (optional): Residency country code (default: us)
- `hotels_limit` (optional): Maximum hotels to return

#### Response

```json
{
    "success": true,
    "data": {
        "search_hash": "abc123...",
        "hotels": [
            {
                "id": "hotel_123",
                "name": "Hotel Example",
                "star_rating": 4,
                "location": {
                    "latitude": 48.8566,
                    "longitude": 2.3522
                },
                "rates": [
                    {
                        "match_hash": "def456...",
                        "room_name": "Standard Double Room",
                        "price": {
                            "amount": 150.00,
                            "currency": "USD"
                        }
                    }
                ]
            }
        ]
    }
}
```

### Search Hotels by Coordinates

**POST** `/api/ratehawk/search/coordinates`

Search for hotels near specific coordinates.

#### Request Body

```json
{
    "checkin": "2024-08-01",
    "checkout": "2024-08-05",
    "latitude": 48.8566,
    "longitude": 2.3522,
    "radius": 5,
    "guests": [
        {
            "adults": 2,
            "children": []
        }
    ]
}
```

#### Additional Parameters

- `latitude` (required): Latitude coordinate
- `longitude` (required): Longitude coordinate
- `radius` (optional): Search radius in kilometers (default: 5)

### Search Specific Hotels

**POST** `/api/ratehawk/search/hotels`

Search for specific hotels by their IDs.

#### Request Body

```json
{
    "checkin": "2024-08-01",
    "checkout": "2024-08-05",
    "ids": ["hotel_123", "hotel_456"],
    "guests": [
        {
            "adults": 2,
            "children": []
        }
    ]
}
```

#### Parameters

- `ids` (required): Array of hotel IDs to search

### Get Hotel Suggestions

**POST** `/api/ratehawk/suggest`

Get hotel and region suggestions based on a search query.

#### Request Body

```json
{
    "query": "Paris",
    "language": "en"
}
```

#### Response

```json
{
    "success": true,
    "data": {
        "hotels": [
            {
                "id": "hotel_123",
                "name": "Hotel Paris",
                "location": "Paris, France"
            }
        ],
        "regions": [
            {
                "id": 6176,
                "name": "Paris",
                "type": "city",
                "country_code": "FR"
            }
        ]
    }
}
```

## Hotel Information Endpoints

### Get Hotel Details

**GET** `/api/ratehawk/hotels/{id}`

Get detailed information about a specific hotel.

#### Parameters

- `id` (path): Hotel ID
- `language` (query): Language code

#### Response

```json
{
    "success": true,
    "data": {
        "static_data": {
            "id": "hotel_123",
            "name": "Hotel Example",
            "description": "A beautiful hotel...",
            "amenities": ["wifi", "pool", "gym"],
            "images": [
                {
                    "url": "https://example.com/image1.jpg",
                    "description": "Hotel exterior"
                }
            ]
        },
        "content_data": {
            "rooms": [
                {
                    "name": "Standard Room",
                    "description": "Comfortable room...",
                    "amenities": ["wifi", "tv"]
                }
            ]
        }
    }
}
```

### Get Hotel Rates

**POST** `/api/ratehawk/hotels/{id}/rates`

Get available rates for a specific hotel.

#### Request Body

```json
{
    "checkin": "2024-08-01",
    "checkout": "2024-08-05",
    "guests": [
        {
            "adults": 2,
            "children": []
        }
    ]
}
```

## Booking Endpoints

### Create Booking

**POST** `/api/ratehawk/booking/create`

Initialize a new booking process.

#### Request Body

```json
{
    "search_hash": "abc123...",
    "match_hash": "def456...",
    "language": "en",
    "partner_order_id": "your_order_123"
}
```

#### Response

```json
{
    "success": true,
    "data": {
        "order_id": "RH123456789",
        "book_hash": "ghi789...",
        "hotel": {
            "name": "Hotel Example",
            "address": "123 Example St"
        },
        "room": {
            "name": "Standard Double Room"
        },
        "price": {
            "amount": 150.00,
            "currency": "USD"
        }
    }
}
```

### Start Booking Process

**POST** `/api/ratehawk/booking/start`

Complete the booking with guest details and payment information.

#### Request Body

```json
{
    "order_id": "RH123456789",
    "book_hash": "ghi789...",
    "guests": [
        {
            "first_name": "John",
            "last_name": "Doe",
            "email": "<EMAIL>",
            "phone": "+1234567890"
        }
    ],
    "payment_type": "deposit",
    "cc_token": "token_123"
}
```

### Check Booking Status

**GET** `/api/ratehawk/booking/{orderId}/status`

Check the current status of a booking.

#### Response

```json
{
    "success": true,
    "data": {
        "order_id": "RH123456789",
        "status": "confirmed",
        "hotel": {
            "name": "Hotel Example"
        },
        "checkin_date": "2024-08-01",
        "checkout_date": "2024-08-05"
    }
}
```

### Cancel Booking

**POST** `/api/ratehawk/booking/{orderId}/cancel`

Cancel an existing booking.

#### Response

```json
{
    "success": true,
    "message": "Booking cancelled successfully",
    "data": {
        "order_id": "RH123456789",
        "status": "cancelled",
        "cancellation_fee": 0.00
    }
}
```

### Get Booking Voucher

**GET** `/api/ratehawk/booking/{orderId}/voucher`

Get the booking voucher document.

### Get Booking Invoice

**GET** `/api/ratehawk/booking/{orderId}/invoice`

Get the booking invoice document.

## Static Content Endpoints

### Get Regions

**GET** `/api/ratehawk/static/regions`

Get list of available regions.

#### Query Parameters

- `country_code` (optional): Filter by country
- `type` (optional): Filter by region type
- `search` (optional): Search by name

### Search Regions

**POST** `/api/ratehawk/static/regions/search`

Search regions by name.

#### Request Body

```json
{
    "query": "Paris",
    "limit": 10
}
```

## Admin API Endpoints

### Dashboard Statistics

**GET** `/api/admin/ratehawk/dashboard/stats`

Get dashboard statistics and metrics.

#### Query Parameters

- `period` (optional): Period in days (default: 30)

#### Response

```json
{
    "success": true,
    "data": {
        "bookings": {
            "total": 150,
            "confirmed": 120,
            "cancelled": 20,
            "success_rate": 80.0
        },
        "api": {
            "total_requests": 1000,
            "successful_requests": 950,
            "error_rate": 5.0,
            "avg_response_time": 0.5
        },
        "revenue": {
            "total_revenue": 15000.00,
            "avg_booking_value": 125.00
        }
    }
}
```

### Manage Bookings

**GET** `/api/admin/ratehawk/bookings`

List and filter bookings.

#### Query Parameters

- `status` (optional): Filter by status
- `from_date` (optional): Start date filter
- `to_date` (optional): End date filter
- `search` (optional): Search term
- `per_page` (optional): Results per page

### Manage API Logs

**GET** `/api/admin/ratehawk/logs`

List and filter API logs.

#### Query Parameters

- `status` (optional): Filter by status
- `method` (optional): Filter by HTTP method
- `endpoint` (optional): Filter by endpoint
- `from_date` (optional): Start date filter
- `to_date` (optional): End date filter

### Settings Management

**GET** `/api/admin/ratehawk/settings`

Get current configuration settings.

**POST** `/api/admin/ratehawk/settings`

Update configuration settings.

#### Request Body

```json
{
    "settings": [
        {
            "key": "ratehawk_enable",
            "value": true,
            "type": "boolean"
        },
        {
            "key": "ratehawk_default_currency",
            "value": "EUR",
            "type": "string"
        }
    ]
}
```

## Error Codes

| Code | Description |
|------|-------------|
| 400 | Bad Request - Invalid parameters |
| 401 | Unauthorized - Authentication required |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 422 | Validation Error - Invalid input data |
| 429 | Rate Limit Exceeded |
| 500 | Internal Server Error |
| 502 | Bad Gateway - RateHawk API error |
| 503 | Service Unavailable |

## Rate Limits

- **Search endpoints**: 10 requests per minute
- **Booking endpoints**: 5 requests per minute
- **General endpoints**: 100 requests per hour
- **Admin endpoints**: 200 requests per hour

## Webhooks

### Booking Status Updates

**POST** `/api/ratehawk/webhook/booking-status`

Receives booking status updates from RateHawk.

#### Payload

```json
{
    "order_id": "RH123456789",
    "status": "confirmed",
    "booking_data": {
        "hotel": {
            "name": "Hotel Example"
        },
        "checkin_date": "2024-08-01",
        "checkout_date": "2024-08-05"
    }
}
```

#### Security

Webhooks are secured with HMAC-SHA256 signature verification using the configured webhook secret.

## SDK Examples

### PHP Laravel

```php
// Search hotels
$searchService = app(\Modules\RateHawk\Services\HotelSearchService::class);
$results = $searchService->searchByRegion($params);

// Create booking
$bookingService = app(\Modules\RateHawk\Services\BookingService::class);
$booking = $bookingService->createBookingProcess($params);
```

### JavaScript/jQuery

```javascript
// Search hotels
$.post('/api/ratehawk/search/region', searchParams)
    .done(function(response) {
        if (response.success) {
            displayResults(response.data);
        }
    });

// Create booking
$.post('/api/ratehawk/booking/create', bookingParams)
    .done(function(response) {
        if (response.success) {
            proceedToPayment(response.data);
        }
    });
```

## Testing

Use the built-in API testing interface at `/admin/ratehawk/api-test` to:

- Test API connectivity
- Validate search functionality
- Check booking processes
- Debug API responses

## Support

For technical support and integration assistance, refer to:

- Module documentation
- Admin dashboard diagnostics
- API logs and error reports
- Debug information panel
