<?php

namespace Modules\RateHawk\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\FrontendController;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Services\StaticContentService;
use Modules\RateHawk\Exceptions\RateHawkApiException;

class RateHawkController extends FrontendController
{
    protected $hotelSearchService;
    protected $staticContentService;

    public function __construct(
        HotelSearchService $hotelSearchService,
        StaticContentService $staticContentService
    ) {
        parent::__construct();
        $this->hotelSearchService = $hotelSearchService;
        $this->staticContentService = $staticContentService;
    }

    /**
     * Display the main RateHawk page
     */
    public function index(Request $request)
    {
        $data = [
            'page_title' => __('Hotel Search'),
            'breadcrumbs' => [
                ['name' => __('Home'), 'url' => url('/')],
                ['name' => __('Hotels'), 'class' => 'active'],
            ],
            'supported_currencies' => config('ratehawk.supported_currencies'),
            'supported_languages' => config('ratehawk.supported_languages'),
            'default_currency' => config('ratehawk.defaults.currency'),
            'default_language' => config('ratehawk.defaults.language'),
        ];

        return view('RateHawk::frontend.index', $data);
    }

    /**
     * Handle hotel search requests
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $searchType = $request->input('search_type', 'region');
            $params = $request->all();

            // Add default values
            $params['currency'] = $params['currency'] ?? config('ratehawk.defaults.currency');
            $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');
            $params['residency'] = $params['residency'] ?? config('ratehawk.defaults.residency');

            switch ($searchType) {
                case 'region':
                    $results = $this->hotelSearchService->searchByRegion($params);
                    break;
                
                case 'hotels':
                    $results = $this->hotelSearchService->searchByHotels($params);
                    break;
                
                case 'coordinates':
                    $results = $this->hotelSearchService->searchByCoordinates($params);
                    break;
                
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid search type'
                    ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $results,
                'search_params' => $params
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            \Log::error('RateHawk search error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while searching hotels'
            ], 500);
        }
    }

    /**
     * Get hotel details
     */
    public function hotel(Request $request, string $id): JsonResponse
    {
        try {
            $params = $request->all();
            $params['id'] = $id;
            $params['currency'] = $params['currency'] ?? config('ratehawk.defaults.currency');
            $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');

            $hotelPage = $this->hotelSearchService->getHotelPage($params);
            $hotelStatic = $this->staticContentService->getHotelStaticData($id, $params);

            return response()->json([
                'success' => true,
                'data' => [
                    'hotel_page' => $hotelPage,
                    'hotel_static' => $hotelStatic
                ]
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            \Log::error('RateHawk hotel details error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching hotel details'
            ], 500);
        }
    }

    /**
     * Get hotel suggestions
     */
    public function suggest(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query');
            $language = $request->input('language', config('ratehawk.defaults.language'));

            if (strlen($query) < 2) {
                return response()->json([
                    'success' => false,
                    'message' => 'Query must be at least 2 characters long'
                ], 400);
            }

            $suggestions = $this->hotelSearchService->suggest([
                'query' => $query,
                'language' => $language
            ]);

            return response()->json([
                'success' => true,
                'data' => $suggestions
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            \Log::error('RateHawk suggestions error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching suggestions'
            ], 500);
        }
    }

    /**
     * Get regions
     */
    public function regions(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['country_code', 'type', 'search']);
            $regions = $this->staticContentService->getRegions($filters);

            return response()->json([
                'success' => true,
                'data' => $regions
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            \Log::error('RateHawk regions error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while fetching regions'
            ], 500);
        }
    }

    /**
     * Search regions
     */
    public function searchRegions(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query');
            $limit = $request->input('limit', 10);

            if (strlen($query) < 2) {
                return response()->json([
                    'success' => false,
                    'message' => 'Query must be at least 2 characters long'
                ], 400);
            }

            $regions = $this->staticContentService->searchRegions($query, $limit);

            return response()->json([
                'success' => true,
                'data' => $regions
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            \Log::error('RateHawk region search error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while searching regions'
            ], 500);
        }
    }

    /**
     * Prebook a rate
     */
    public function prebook(Request $request): JsonResponse
    {
        try {
            $params = $request->only(['search_hash', 'match_hash', 'language']);
            $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');

            $prebookResult = $this->hotelSearchService->prebook($params);

            return response()->json([
                'success' => true,
                'data' => $prebookResult
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            \Log::error('RateHawk prebook error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while prebooking'
            ], 500);
        }
    }
}
