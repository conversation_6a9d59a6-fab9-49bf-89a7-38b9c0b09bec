<?php

namespace Modules\RateHawk\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\RateHawk\Services\BookingService;
use Modules\RateHawk\Models\RateHawkBooking;
use Modules\RateHawk\Events\BookingStatusUpdated;

class ProcessBookingOperation implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $operation;
    protected $orderId;
    protected $params;

    /**
     * Create a new job instance.
     */
    public function __construct(string $operation, string $orderId, array $params = [])
    {
        $this->operation = $operation;
        $this->orderId = $orderId;
        $this->params = $params;
    }

    /**
     * Execute the job.
     */
    public function handle(BookingService $bookingService)
    {
        try {
            Log::info('Processing booking operation', [
                'operation' => $this->operation,
                'order_id' => $this->orderId,
                'params' => $this->params
            ]);

            switch ($this->operation) {
                case 'check_status':
                    $this->checkBookingStatus($bookingService);
                    break;
                
                case 'cancel':
                    $this->cancelBooking($bookingService);
                    break;
                
                case 'get_voucher':
                    $this->getVoucher($bookingService);
                    break;
                
                case 'get_invoice':
                    $this->getInvoice($bookingService);
                    break;
                
                default:
                    Log::warning('Unknown booking operation', [
                        'operation' => $this->operation,
                        'order_id' => $this->orderId
                    ]);
            }

        } catch (\Exception $e) {
            Log::error('Booking operation failed', [
                'operation' => $this->operation,
                'order_id' => $this->orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Mark the job as failed
            $this->fail($e);
        }
    }

    /**
     * Check booking status
     */
    protected function checkBookingStatus(BookingService $bookingService): void
    {
        try {
            $response = $bookingService->checkBookingStatus($this->orderId);
            
            Log::info('Booking status checked', [
                'order_id' => $this->orderId,
                'status' => $response['data']['status'] ?? 'unknown'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to check booking status', [
                'order_id' => $this->orderId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Cancel booking
     */
    protected function cancelBooking(BookingService $bookingService): void
    {
        try {
            $response = $bookingService->cancelBooking($this->orderId);
            
            Log::info('Booking cancelled', [
                'order_id' => $this->orderId,
                'response' => $response
            ]);

            // Fire event for booking cancellation
            $booking = $bookingService->getLocalBooking($this->orderId);
            if ($booking) {
                event(new BookingStatusUpdated($booking, 'cancelled', $response['data'] ?? []));
            }

        } catch (\Exception $e) {
            Log::error('Failed to cancel booking', [
                'order_id' => $this->orderId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get booking voucher
     */
    protected function getVoucher(BookingService $bookingService): void
    {
        try {
            $response = $bookingService->getVoucher($this->orderId);
            
            // Update local booking with voucher URL
            $booking = RateHawkBooking::where('order_id', $this->orderId)->first();
            if ($booking && isset($response['data']['voucher_url'])) {
                $booking->update([
                    'voucher_url' => $response['data']['voucher_url']
                ]);
            }

            Log::info('Booking voucher retrieved', [
                'order_id' => $this->orderId,
                'voucher_available' => isset($response['data']['voucher_url'])
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get booking voucher', [
                'order_id' => $this->orderId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get booking invoice
     */
    protected function getInvoice(BookingService $bookingService): void
    {
        try {
            $response = $bookingService->getInvoice($this->orderId);
            
            // Update local booking with invoice URL
            $booking = RateHawkBooking::where('order_id', $this->orderId)->first();
            if ($booking && isset($response['data']['invoice_url'])) {
                $booking->update([
                    'invoice_url' => $response['data']['invoice_url']
                ]);
            }

            Log::info('Booking invoice retrieved', [
                'order_id' => $this->orderId,
                'invoice_available' => isset($response['data']['invoice_url'])
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get booking invoice', [
                'order_id' => $this->orderId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception)
    {
        Log::error('Booking operation job failed', [
            'operation' => $this->operation,
            'order_id' => $this->orderId,
            'error' => $exception->getMessage()
        ]);

        // Update local booking with error status if applicable
        if (in_array($this->operation, ['cancel'])) {
            $booking = RateHawkBooking::where('order_id', $this->orderId)->first();
            if ($booking) {
                $booking->update([
                    'notes' => 'Operation failed: ' . $exception->getMessage()
                ]);
            }
        }
    }
}
