{"__meta": {"id": "X0ca850997b2c17d3357a1d5d349abc49", "datetime": "2025-07-10 21:35:15", "utime": 1752183315.306903, "method": "GET", "uri": "/api/ratehawk-booking/history?email=<EMAIL>", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752183314.179596, "end": 1752183315.306951, "duration": 1.1273550987243652, "duration_str": "1.13s", "measures": [{"label": "Booting", "start": 1752183314.179596, "relative_start": 0, "end": 1752183315.204253, "relative_end": 1752183315.204253, "duration": 1.0246570110321045, "duration_str": "1.02s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752183315.204291, "relative_start": 1.0246951580047607, "end": 1752183315.306958, "relative_end": 6.9141387939453125e-06, "duration": 0.10266685485839844, "duration_str": "103ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5419952, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/ratehawk-booking/{reference}", "middleware": "web", "controller": "App\\Http\\Controllers\\BookingController@getBooking", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "api.ratehawk.booking.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=103\" onclick=\"\">app/Http/Controllers/BookingController.php:103-135</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "YUFtRszTjxVM5FyzGnxSDEMtTOO7YN4Jg6SWHmG8", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/api/ratehawk-booking/history?email=test%40example.com\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/api/ratehawk-booking/history", "status_code": "<pre class=sf-dump id=sf-dump-773816315 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-773816315\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-809352333 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-809352333\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2028881975 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2028881975\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1237172536 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">curl/8.9.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1237172536\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-990259890 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-990259890\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2130432594 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 21:35:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im42VU1BYjh4U2lBWFV0ZG10Q2lWT1E9PSIsInZhbHVlIjoiUDE2cm1sdHQ1aU5veFBMbGdvN28rZ3FOMXY0c0Z2MWdkQWRneDRvTTVYck1YU1BGc3BuUzZZbFlBbWgwZnEzelR5aVRNbGF3VnRTbEI0MWwxS01MMlZmaDdnM093dFFrQnRaaVFkOVVVOXIxc2tqcTB6UjlnT3cyYWY3azJ4cnUiLCJtYWMiOiI5MjA2YmE4ZGQwOTI1MjZlNjhkNDFlZTMwYjY1MjliZjdlYzAxZmRlNWZlNzhjZWViZTk1YWQwZjRmZWJmODJjIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:35:15 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6InpzeXVKMWEzQ043cERVUS9lU1RpWmc9PSIsInZhbHVlIjoiUGRLWHFsbnh2K21WY1VNMWNPN0F2bkJRSk5IZDFzR0FFWkdjbG5kczBnNTAwTDRMaWZaSElrTTlTNGt3c1Z0anFqKzJvTkdrVHBLR3NTY2kvaTNmZ3NENUFUODVlTU9IZHI5eXhYVDJpUitmbHlLaGtjWTU5b0pka25iZTVNQW0iLCJtYWMiOiJhOGUyMGY0MmQ2NzA2ZDVhNTczMWMyM2Q1NDNiYmQ1N2MxZjgzNmUwZjE2ZTdmYmE4NGYyNGE0MTZhYjVlNjQxIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:35:15 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im42VU1BYjh4U2lBWFV0ZG10Q2lWT1E9PSIsInZhbHVlIjoiUDE2cm1sdHQ1aU5veFBMbGdvN28rZ3FOMXY0c0Z2MWdkQWRneDRvTTVYck1YU1BGc3BuUzZZbFlBbWgwZnEzelR5aVRNbGF3VnRTbEI0MWwxS01MMlZmaDdnM093dFFrQnRaaVFkOVVVOXIxc2tqcTB6UjlnT3cyYWY3azJ4cnUiLCJtYWMiOiI5MjA2YmE4ZGQwOTI1MjZlNjhkNDFlZTMwYjY1MjliZjdlYzAxZmRlNWZlNzhjZWViZTk1YWQwZjRmZWJmODJjIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:35:15 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6InpzeXVKMWEzQ043cERVUS9lU1RpWmc9PSIsInZhbHVlIjoiUGRLWHFsbnh2K21WY1VNMWNPN0F2bkJRSk5IZDFzR0FFWkdjbG5kczBnNTAwTDRMaWZaSElrTTlTNGt3c1Z0anFqKzJvTkdrVHBLR3NTY2kvaTNmZ3NENUFUODVlTU9IZHI5eXhYVDJpUitmbHlLaGtjWTU5b0pka25iZTVNQW0iLCJtYWMiOiJhOGUyMGY0MmQ2NzA2ZDVhNTczMWMyM2Q1NDNiYmQ1N2MxZjgzNmUwZjE2ZTdmYmE4NGYyNGE0MTZhYjVlNjQxIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:35:15 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2130432594\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1414689980 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YUFtRszTjxVM5FyzGnxSDEMtTOO7YN4Jg6SWHmG8</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"75 characters\">http://127.0.0.1:8000/api/ratehawk-booking/history?email=test%40example.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1414689980\", {\"maxDepth\":0})</script>\n"}}