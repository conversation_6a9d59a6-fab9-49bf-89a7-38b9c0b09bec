<?php

namespace Modules\RateHawk\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Services\StaticContentService;
use Modules\RateHawk\Exceptions\RateHawkApiException;

class HotelController extends Controller
{
    protected $hotelSearchService;
    protected $staticContentService;

    public function __construct(
        HotelSearchService $hotelSearchService,
        StaticContentService $staticContentService
    ) {
        $this->hotelSearchService = $hotelSearchService;
        $this->staticContentService = $staticContentService;
    }

    /**
     * Get hotel details
     */
    public function show(Request $request, string $id): JsonResponse
    {
        try {
            $params = $request->only(['language', 'currency']);
            $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');
            $params['currency'] = $params['currency'] ?? config('ratehawk.defaults.currency');

            // Get hotel static data and content
            $hotelStatic = $this->staticContentService->getHotelStaticData($id, $params);
            $hotelContent = $this->staticContentService->getHotelContent($id, $params);

            $results = [
                'hotel_id' => $id,
                'static_data' => $hotelStatic,
                'content_data' => $hotelContent,
            ];

            return $this->successResponse($results, 'Hotel details retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get hotel details: ' . $e->getMessage());
        }
    }

    /**
     * Get hotel rates
     */
    public function getRates(Request $request, string $id): JsonResponse
    {
        try {
            $params = $request->all();
            $params['id'] = $id;
            $params['currency'] = $params['currency'] ?? config('ratehawk.defaults.currency');
            $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');
            $params['residency'] = $params['residency'] ?? config('ratehawk.defaults.residency');

            $hotelPage = $this->hotelSearchService->getHotelPage($params);

            return $this->successResponse($hotelPage, 'Hotel rates retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get hotel rates: ' . $e->getMessage());
        }
    }

    /**
     * Prebook a hotel rate
     */
    public function prebook(Request $request): JsonResponse
    {
        try {
            $params = $request->only(['search_hash', 'match_hash', 'language']);
            $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');

            $prebookResult = $this->hotelSearchService->prebook($params);

            return $this->successResponse($prebookResult, 'Rate prebooked successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to prebook rate: ' . $e->getMessage());
        }
    }

    /**
     * Return success response
     */
    protected function successResponse(array $data, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Return error response
     */
    protected function errorResponse(
        string $message, 
        int $statusCode = 500, 
        array $errorDetails = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->toISOString()
        ];

        if (!empty($errorDetails)) {
            $response['error_details'] = $errorDetails;
        }

        return response()->json($response, $statusCode);
    }
}
