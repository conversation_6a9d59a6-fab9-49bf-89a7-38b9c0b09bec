<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Cache;
use Modules\RateHawk\Models\RateHawkApiLog;
use Modules\RateHawk\Models\RateHawkBooking;
use Modules\RateHawk\Helpers\ConfigHelper;

class DebugService
{
    protected $loggingService;
    protected $errorHandlingService;

    public function __construct(
        LoggingService $loggingService,
        ErrorHandlingService $errorHandlingService
    ) {
        $this->loggingService = $loggingService;
        $this->errorHandlingService = $errorHandlingService;
    }

    /**
     * Get comprehensive debug information
     */
    public function getDebugInfo(): array
    {
        return [
            'system_status' => $this->getSystemStatus(),
            'configuration' => $this->getConfigurationDebug(),
            'api_health' => $this->getApiHealth(),
            'recent_activity' => $this->getRecentActivity(),
            'performance_metrics' => $this->getPerformanceMetrics(),
            'error_summary' => $this->getErrorSummary(),
            'cache_status' => $this->getCacheStatus(),
            'environment_info' => $this->getEnvironmentInfo(),
        ];
    }

    /**
     * Get system status
     */
    public function getSystemStatus(): array
    {
        $configStatus = ConfigHelper::getStatus();
        
        return [
            'module_enabled' => $configStatus['is_enabled'],
            'configuration_valid' => $configStatus['is_valid'],
            'api_credentials_configured' => $configStatus['has_credentials'],
            'environment' => $configStatus['environment'],
            'cache_enabled' => $configStatus['cache_enabled'],
            'logging_enabled' => $configStatus['logging_enabled'],
            'webhooks_enabled' => $configStatus['webhooks_enabled'],
            'errors' => $configStatus['errors'],
            'last_check' => now()->toISOString(),
        ];
    }

    /**
     * Get configuration debug info
     */
    public function getConfigurationDebug(): array
    {
        return [
            'api_config' => ConfigHelper::getApiConfig(),
            'default_params' => ConfigHelper::getDefaultSearchParams(),
            'cache_config' => ConfigHelper::getCacheConfig(),
            'rate_limits' => ConfigHelper::getRateLimitConfig(),
            'webhook_config' => array_merge(
                ConfigHelper::getWebhookConfig(),
                ['secret' => ConfigHelper::getWebhookConfig()['secret'] ? '***SET***' : '***NOT SET***']
            ),
            'supported_currencies' => ConfigHelper::getSupportedCurrencies(),
            'supported_languages' => array_keys(ConfigHelper::getSupportedLanguages()),
        ];
    }

    /**
     * Get API health status
     */
    public function getApiHealth(): array
    {
        $recentLogs = RateHawkApiLog::where('created_at', '>=', now()->subHours(1))
            ->orderBy('created_at', 'desc')
            ->get();

        $totalRequests = $recentLogs->count();
        $successfulRequests = $recentLogs->where('status', 'completed')->count();
        $failedRequests = $recentLogs->where('status', 'error')->count();

        return [
            'last_hour_requests' => $totalRequests,
            'successful_requests' => $successfulRequests,
            'failed_requests' => $failedRequests,
            'success_rate' => $totalRequests > 0 ? round(($successfulRequests / $totalRequests) * 100, 2) : 0,
            'avg_response_time' => $recentLogs->where('status', 'completed')->avg('duration'),
            'last_successful_request' => $recentLogs->where('status', 'completed')->first()?->created_at?->toISOString(),
            'last_failed_request' => $recentLogs->where('status', 'error')->first()?->created_at?->toISOString(),
        ];
    }

    /**
     * Get recent activity
     */
    public function getRecentActivity(): array
    {
        $recentLogs = RateHawkApiLog::orderBy('created_at', 'desc')->limit(10)->get();
        $recentBookings = RateHawkBooking::orderBy('created_at', 'desc')->limit(5)->get();

        return [
            'recent_api_calls' => $recentLogs->map(function ($log) {
                return [
                    'id' => $log->id,
                    'method' => $log->method,
                    'endpoint' => $log->endpoint,
                    'status' => $log->status,
                    'duration' => $log->duration,
                    'created_at' => $log->created_at->toISOString(),
                ];
            }),
            'recent_bookings' => $recentBookings->map(function ($booking) {
                return [
                    'id' => $booking->id,
                    'order_id' => $booking->order_id,
                    'status' => $booking->status,
                    'hotel_name' => $booking->hotel_name,
                    'created_at' => $booking->created_at->toISOString(),
                ];
            }),
        ];
    }

    /**
     * Get performance metrics
     */
    public function getPerformanceMetrics(): array
    {
        $logs = RateHawkApiLog::where('created_at', '>=', now()->subDay())
            ->where('status', 'completed')
            ->get();

        if ($logs->isEmpty()) {
            return [
                'avg_response_time' => 0,
                'min_response_time' => 0,
                'max_response_time' => 0,
                'requests_per_hour' => 0,
                'slowest_endpoints' => [],
            ];
        }

        $durations = $logs->pluck('duration')->filter();
        
        return [
            'avg_response_time' => round($durations->avg(), 3),
            'min_response_time' => round($durations->min(), 3),
            'max_response_time' => round($durations->max(), 3),
            'requests_per_hour' => round($logs->count() / 24, 2),
            'slowest_endpoints' => $logs->groupBy('endpoint')
                ->map(function ($endpointLogs) {
                    return [
                        'avg_duration' => round($endpointLogs->avg('duration'), 3),
                        'count' => $endpointLogs->count(),
                    ];
                })
                ->sortByDesc('avg_duration')
                ->take(5)
                ->toArray(),
        ];
    }

    /**
     * Get error summary
     */
    public function getErrorSummary(): array
    {
        return $this->errorHandlingService->getErrorStatistics(7);
    }

    /**
     * Get cache status
     */
    public function getCacheStatus(): array
    {
        $cacheConfig = ConfigHelper::getCacheConfig();
        
        if (!$cacheConfig['enabled']) {
            return [
                'enabled' => false,
                'status' => 'disabled',
            ];
        }

        // Test cache functionality
        $testKey = 'ratehawk_cache_test_' . time();
        $testValue = 'test_value';
        
        try {
            Cache::put($testKey, $testValue, 60);
            $retrieved = Cache::get($testKey);
            Cache::forget($testKey);
            
            $cacheWorking = $retrieved === $testValue;
        } catch (\Exception $e) {
            $cacheWorking = false;
        }

        return [
            'enabled' => true,
            'working' => $cacheWorking,
            'ttl_settings' => $cacheConfig['ttl'],
            'status' => $cacheWorking ? 'operational' : 'error',
        ];
    }

    /**
     * Get environment information
     */
    public function getEnvironmentInfo(): array
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_time' => now()->toISOString(),
            'timezone' => config('app.timezone'),
            'environment' => app()->environment(),
            'debug_mode' => config('app.debug'),
            'cache_driver' => config('cache.default'),
            'queue_driver' => config('queue.default'),
            'log_channel' => config('logging.default'),
            'memory_usage' => round(memory_get_usage(true) / 1024 / 1024, 2) . ' MB',
            'memory_limit' => ini_get('memory_limit'),
        ];
    }

    /**
     * Test API connectivity
     */
    public function testApiConnectivity(): array
    {
        try {
            $apiClient = app(RateHawkApiClient::class);
            $result = $apiClient->testConnection();
            
            return [
                'status' => $result['success'] ? 'success' : 'failed',
                'message' => $result['message'],
                'response_time' => null, // Could be measured
                'data' => $result['data'],
                'tested_at' => now()->toISOString(),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
                'response_time' => null,
                'data' => null,
                'tested_at' => now()->toISOString(),
            ];
        }
    }

    /**
     * Validate system requirements
     */
    public function validateSystemRequirements(): array
    {
        $requirements = [
            'php_version' => [
                'required' => '8.0',
                'current' => PHP_VERSION,
                'status' => version_compare(PHP_VERSION, '8.0', '>=') ? 'pass' : 'fail',
            ],
            'curl_extension' => [
                'required' => true,
                'current' => extension_loaded('curl'),
                'status' => extension_loaded('curl') ? 'pass' : 'fail',
            ],
            'json_extension' => [
                'required' => true,
                'current' => extension_loaded('json'),
                'status' => extension_loaded('json') ? 'pass' : 'fail',
            ],
            'openssl_extension' => [
                'required' => true,
                'current' => extension_loaded('openssl'),
                'status' => extension_loaded('openssl') ? 'pass' : 'fail',
            ],
            'database_connection' => [
                'required' => true,
                'current' => $this->testDatabaseConnection(),
                'status' => $this->testDatabaseConnection() ? 'pass' : 'fail',
            ],
        ];

        $allPassed = collect($requirements)->every(function ($req) {
            return $req['status'] === 'pass';
        });

        return [
            'all_requirements_met' => $allPassed,
            'requirements' => $requirements,
        ];
    }

    /**
     * Test database connection
     */
    protected function testDatabaseConnection(): bool
    {
        try {
            \DB::connection()->getPdo();
            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Generate debug report
     */
    public function generateDebugReport(): array
    {
        $debugInfo = $this->getDebugInfo();
        $connectivity = $this->testApiConnectivity();
        $requirements = $this->validateSystemRequirements();

        return [
            'report_generated_at' => now()->toISOString(),
            'system_requirements' => $requirements,
            'api_connectivity' => $connectivity,
            'debug_info' => $debugInfo,
            'recommendations' => $this->generateRecommendations($debugInfo, $connectivity, $requirements),
        ];
    }

    /**
     * Generate recommendations based on debug info
     */
    protected function generateRecommendations(array $debugInfo, array $connectivity, array $requirements): array
    {
        $recommendations = [];

        // Check system requirements
        if (!$requirements['all_requirements_met']) {
            $recommendations[] = [
                'type' => 'system_requirements',
                'priority' => 'critical',
                'message' => 'System requirements not met',
                'action' => 'Install missing PHP extensions and update PHP version if needed',
            ];
        }

        // Check API connectivity
        if ($connectivity['status'] !== 'success') {
            $recommendations[] = [
                'type' => 'api_connectivity',
                'priority' => 'high',
                'message' => 'API connectivity issues detected',
                'action' => 'Check API credentials and network connectivity',
            ];
        }

        // Check configuration
        if (!$debugInfo['system_status']['configuration_valid']) {
            $recommendations[] = [
                'type' => 'configuration',
                'priority' => 'high',
                'message' => 'Configuration issues detected',
                'action' => 'Review and fix configuration errors',
            ];
        }

        // Check error rates
        $errorRate = $debugInfo['api_health']['success_rate'] ?? 100;
        if ($errorRate < 95) {
            $recommendations[] = [
                'type' => 'error_rate',
                'priority' => 'medium',
                'message' => 'High error rate detected',
                'action' => 'Review recent errors and implement better error handling',
            ];
        }

        // Check cache status
        if ($debugInfo['cache_status']['enabled'] && !$debugInfo['cache_status']['working']) {
            $recommendations[] = [
                'type' => 'cache',
                'priority' => 'medium',
                'message' => 'Cache is enabled but not working',
                'action' => 'Check cache configuration and driver',
            ];
        }

        return $recommendations;
    }
}
