<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Log;
use Modules\RateHawk\Models\RateHawkApiLog;
use Modules\RateHawk\Helpers\ConfigHelper;

class LoggingService
{
    protected $loggingEnabled;
    protected $logLevel;
    protected $logChannel;

    public function __construct()
    {
        $loggingConfig = ConfigHelper::getLoggingConfig();
        $this->loggingEnabled = $loggingConfig['enabled'];
        $this->logLevel = $loggingConfig['level'];
        $this->logChannel = $loggingConfig['channel'];
    }

    /**
     * Log API request
     */
    public function logApiRequest(string $requestId, string $method, string $endpoint, array $data = []): void
    {
        if (!$this->loggingEnabled) {
            return;
        }

        try {
            // Log to file
            $this->logToFile('info', 'API Request', [
                'request_id' => $requestId,
                'method' => $method,
                'endpoint' => $endpoint,
                'data' => $this->sanitizeLogData($data),
            ]);

            // Store in database
            RateHawkApiLog::create([
                'request_id' => $requestId,
                'method' => $method,
                'endpoint' => $endpoint,
                'request_data' => $data,
                'status' => 'pending',
                'user_id' => auth()->id(),
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'created_at' => now(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log API request: ' . $e->getMessage());
        }
    }

    /**
     * Log API response
     */
    public function logApiResponse(string $requestId, int $statusCode, array $responseData, float $duration): void
    {
        if (!$this->loggingEnabled) {
            return;
        }

        try {
            // Log to file
            $this->logToFile('info', 'API Response', [
                'request_id' => $requestId,
                'status_code' => $statusCode,
                'duration' => $duration,
                'response_size' => strlen(json_encode($responseData)),
            ]);

            // Update database record
            RateHawkApiLog::where('request_id', $requestId)->update([
                'status_code' => $statusCode,
                'response_data' => $responseData,
                'duration' => $duration,
                'status' => 'completed',
                'updated_at' => now(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log API response: ' . $e->getMessage());
        }
    }

    /**
     * Log API error
     */
    public function logApiError(string $requestId, string $errorType, string $errorMessage, float $duration = null, int $statusCode = null, array $responseData = null): void
    {
        if (!$this->loggingEnabled) {
            return;
        }

        try {
            // Log to file
            $this->logToFile('error', 'API Error', [
                'request_id' => $requestId,
                'error_type' => $errorType,
                'error_message' => $errorMessage,
                'status_code' => $statusCode,
                'duration' => $duration,
            ]);

            // Update database record
            RateHawkApiLog::where('request_id', $requestId)->update([
                'status_code' => $statusCode,
                'error_type' => $errorType,
                'error_message' => $errorMessage,
                'response_data' => $responseData,
                'duration' => $duration,
                'status' => 'error',
                'updated_at' => now(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to log API error: ' . $e->getMessage());
        }
    }

    /**
     * Log booking operation
     */
    public function logBookingOperation(string $operation, string $orderId, array $data = [], string $status = 'info'): void
    {
        if (!$this->loggingEnabled) {
            return;
        }

        $this->logToFile($status, "Booking {$operation}", [
            'order_id' => $orderId,
            'operation' => $operation,
            'data' => $this->sanitizeLogData($data),
            'user_id' => auth()->id(),
        ]);
    }

    /**
     * Log search operation
     */
    public function logSearchOperation(string $searchType, array $params, array $results = null): void
    {
        if (!$this->loggingEnabled) {
            return;
        }

        $logData = [
            'search_type' => $searchType,
            'params' => $this->sanitizeLogData($params),
            'user_id' => auth()->id(),
        ];

        if ($results) {
            $logData['results_count'] = count($results['data'] ?? []);
            $logData['search_hash'] = $results['search_hash'] ?? null;
        }

        $this->logToFile('info', 'Hotel Search', $logData);
    }

    /**
     * Log webhook received
     */
    public function logWebhook(string $type, array $payload, string $status = 'received'): void
    {
        if (!$this->loggingEnabled) {
            return;
        }

        $this->logToFile('info', 'Webhook Received', [
            'type' => $type,
            'status' => $status,
            'payload' => $this->sanitizeLogData($payload),
            'ip_address' => request()->ip(),
        ]);
    }

    /**
     * Log configuration change
     */
    public function logConfigurationChange(string $key, $oldValue, $newValue, int $userId = null): void
    {
        if (!$this->loggingEnabled) {
            return;
        }

        $this->logToFile('info', 'Configuration Changed', [
            'setting_key' => $key,
            'old_value' => $this->sanitizeConfigValue($key, $oldValue),
            'new_value' => $this->sanitizeConfigValue($key, $newValue),
            'user_id' => $userId ?? auth()->id(),
        ]);
    }

    /**
     * Log performance metrics
     */
    public function logPerformanceMetrics(string $operation, float $duration, array $metrics = []): void
    {
        if (!$this->loggingEnabled || !$this->shouldLogLevel('debug')) {
            return;
        }

        $this->logToFile('debug', 'Performance Metrics', [
            'operation' => $operation,
            'duration' => $duration,
            'metrics' => $metrics,
        ]);
    }

    /**
     * Log cache operation
     */
    public function logCacheOperation(string $operation, string $key, bool $hit = null, float $duration = null): void
    {
        if (!$this->loggingEnabled || !$this->shouldLogLevel('debug')) {
            return;
        }

        $logData = [
            'operation' => $operation,
            'cache_key' => $key,
        ];

        if ($hit !== null) {
            $logData['cache_hit'] = $hit;
        }

        if ($duration !== null) {
            $logData['duration'] = $duration;
        }

        $this->logToFile('debug', 'Cache Operation', $logData);
    }

    /**
     * Get log statistics
     */
    public function getLogStatistics(int $days = 7): array
    {
        $startDate = now()->subDays($days)->startOfDay();

        $logs = RateHawkApiLog::where('created_at', '>=', $startDate)->get();

        return [
            'total_requests' => $logs->count(),
            'successful_requests' => $logs->where('status', 'completed')->count(),
            'failed_requests' => $logs->where('status', 'error')->count(),
            'pending_requests' => $logs->where('status', 'pending')->count(),
            'avg_response_time' => $logs->where('status', 'completed')->avg('duration'),
            'by_method' => $logs->groupBy('method')->map->count(),
            'by_endpoint' => $logs->groupBy('endpoint')->map->count(),
            'by_status' => $logs->groupBy('status')->map->count(),
            'error_rate' => $logs->count() > 0 ? round(($logs->where('status', 'error')->count() / $logs->count()) * 100, 2) : 0,
        ];
    }

    /**
     * Clean old logs
     */
    public function cleanOldLogs(int $daysToKeep = 30): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return RateHawkApiLog::where('created_at', '<', $cutoffDate)->delete();
    }

    /**
     * Export logs
     */
    public function exportLogs(array $filters = []): array
    {
        $query = RateHawkApiLog::query();

        if (isset($filters['start_date'])) {
            $query->where('created_at', '>=', $filters['start_date']);
        }

        if (isset($filters['end_date'])) {
            $query->where('created_at', '<=', $filters['end_date']);
        }

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['method'])) {
            $query->where('method', $filters['method']);
        }

        return $query->orderBy('created_at', 'desc')->get()->toArray();
    }

    /**
     * Log to file with appropriate level
     */
    protected function logToFile(string $level, string $message, array $context = []): void
    {
        if (!$this->shouldLogLevel($level)) {
            return;
        }

        Log::channel($this->logChannel)->log($level, "[RateHawk] {$message}", $context);
    }

    /**
     * Check if we should log at this level
     */
    protected function shouldLogLevel(string $level): bool
    {
        $levels = ['debug' => 0, 'info' => 1, 'warning' => 2, 'error' => 3, 'critical' => 4];
        $currentLevel = $levels[$this->logLevel] ?? 1;
        $requestedLevel = $levels[$level] ?? 1;

        return $requestedLevel >= $currentLevel;
    }

    /**
     * Sanitize log data to remove sensitive information
     */
    protected function sanitizeLogData(array $data): array
    {
        $sensitiveKeys = [
            'password', 'api_key', 'secret', 'token', 'cc_number', 'cc_cvc',
            'cc_holder_name', 'ratehawk_api_key', 'ratehawk_key_id'
        ];

        return $this->recursiveSanitize($data, $sensitiveKeys);
    }

    /**
     * Recursively sanitize array data
     */
    protected function recursiveSanitize(array $data, array $sensitiveKeys): array
    {
        foreach ($data as $key => $value) {
            if (is_array($value)) {
                $data[$key] = $this->recursiveSanitize($value, $sensitiveKeys);
            } elseif (in_array(strtolower($key), $sensitiveKeys)) {
                $data[$key] = '***REDACTED***';
            }
        }

        return $data;
    }

    /**
     * Sanitize configuration values
     */
    protected function sanitizeConfigValue(string $key, $value)
    {
        $sensitiveKeys = ['api_key', 'key_id', 'secret', 'password'];
        
        foreach ($sensitiveKeys as $sensitiveKey) {
            if (strpos(strtolower($key), $sensitiveKey) !== false) {
                return '***REDACTED***';
            }
        }

        return $value;
    }
}
