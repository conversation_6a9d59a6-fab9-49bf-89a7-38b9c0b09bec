{"__meta": {"id": "Xd7bff1aa37b80e348c92f1e04c367b8d", "datetime": "2025-07-09 18:12:06", "utime": **********.04469, "method": "POST", "uri": "/admin/module/user/role/save_permissions", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752084724.393746, "end": **********.044719, "duration": 1.6509730815887451, "duration_str": "1.65s", "measures": [{"label": "Booting", "start": 1752084724.393746, "relative_start": 0, "end": 1752084724.953608, "relative_end": 1752084724.953608, "duration": 0.5598621368408203, "duration_str": "560ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752084724.953626, "relative_start": 0.5598800182342529, "end": **********.044722, "relative_end": 3.0994415283203125e-06, "duration": 1.0910961627960205, "duration_str": "1.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6494776, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/module/user/role/save_permissions", "middleware": "web, dashboard", "controller": "Modules\\User\\Admin\\RoleController@save_permissions", "namespace": "Modules\\User\\Admin", "prefix": "admin/module/user/role", "where": [], "as": "user.admin.role.save_permissions", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FAdmin%2FRoleController.php&line=309\" onclick=\"\">modules/User/Admin/RoleController.php:309-330</a>"}, "queries": {"nb_statements": 185, "nb_failed_statements": 0, "accumulated_duration": 0.16180000000000005, "accumulated_duration_str": "162ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.00514, "duration": 0.01864, "duration_str": "18.64ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.030716, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles`", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "The <code>SELECT</code> statement has no <code>WHERE</code> clause and could examine many more rows than intended"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 319}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.039352, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "RoleController.php:319", "source": "modules/User/Admin/RoleController.php:319", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FAdmin%2FRoleController.php&line=319", "ajax": false, "filename": "RoleController.php", "line": "319"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'report_view') limit 1", "type": "query", "params": [], "bindings": ["1", "report_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.046321, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'contact_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "contact_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.050993, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'newsletter_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "newsletter_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.055377, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'language_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "language_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.06028, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'language_translation') limit 1", "type": "query", "params": [], "bindings": ["1", "language_translation"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.064923, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'booking_view') limit 1", "type": "query", "params": [], "bindings": ["1", "booking_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0695329, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'booking_update') limit 1", "type": "query", "params": [], "bindings": ["1", "booking_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0734148, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'booking_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "booking_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.079333, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'enquiry_view') limit 1", "type": "query", "params": [], "bindings": ["1", "enquiry_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0839121, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'enquiry_update') limit 1", "type": "query", "params": [], "bindings": ["1", "enquiry_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.088382, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'enquiry_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "enquiry_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.0933251, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'template_view') limit 1", "type": "query", "params": [], "bindings": ["1", "template_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.097659, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'template_create') limit 1", "type": "query", "params": [], "bindings": ["1", "template_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.101696, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'template_update') limit 1", "type": "query", "params": [], "bindings": ["1", "template_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.106636, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'template_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "template_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.11268, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_view') limit 1", "type": "query", "params": [], "bindings": ["1", "news_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.117309, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_create') limit 1", "type": "query", "params": [], "bindings": ["1", "news_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.12128, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_update') limit 1", "type": "query", "params": [], "bindings": ["1", "news_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.125579, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "news_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.131639, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "news_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.136285, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'role_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "role_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.140182, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'role_view') limit 1", "type": "query", "params": [], "bindings": ["1", "role_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.145977, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'role_create') limit 1", "type": "query", "params": [], "bindings": ["1", "role_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.150876, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'role_update') limit 1", "type": "query", "params": [], "bindings": ["1", "role_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.155009, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'role_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "role_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.159782, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'permission_view') limit 1", "type": "query", "params": [], "bindings": ["1", "permission_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.164964, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'permission_create') limit 1", "type": "query", "params": [], "bindings": ["1", "permission_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.169251, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'permission_update') limit 1", "type": "query", "params": [], "bindings": ["1", "permission_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.175285, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'permission_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "permission_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1809099, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'dashboard_access') limit 1", "type": "query", "params": [], "bindings": ["1", "dashboard_access"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.185312, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'dashboard_vendor_access') limit 1", "type": "query", "params": [], "bindings": ["1", "dashboard_vendor_access"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.189234, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'setting_update') limit 1", "type": "query", "params": [], "bindings": ["1", "setting_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.193891, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'setting_view') limit 1", "type": "query", "params": [], "bindings": ["1", "setting_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1984282, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'setting_update') limit 1", "type": "query", "params": [], "bindings": ["1", "setting_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.202371, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'menu_view') limit 1", "type": "query", "params": [], "bindings": ["1", "menu_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2062979, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'menu_create') limit 1", "type": "query", "params": [], "bindings": ["1", "menu_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.212094, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'menu_update') limit 1", "type": "query", "params": [], "bindings": ["1", "menu_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.216832, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'menu_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "menu_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.221023, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_view') limit 1", "type": "query", "params": [], "bindings": ["1", "user_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.22631, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_create') limit 1", "type": "query", "params": [], "bindings": ["1", "user_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2306058, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_update') limit 1", "type": "query", "params": [], "bindings": ["1", "user_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.234533, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "user_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.239044, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'page_view') limit 1", "type": "query", "params": [], "bindings": ["1", "page_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.245101, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'page_create') limit 1", "type": "query", "params": [], "bindings": ["1", "page_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2494729, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'page_update') limit 1", "type": "query", "params": [], "bindings": ["1", "page_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.253397, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'page_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "page_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2577822, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'page_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "page_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.262674, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'media_upload') limit 1", "type": "query", "params": [], "bindings": ["1", "media_upload"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.26803, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'media_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "media_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.275343, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'media_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "media_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2809281, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'location_view') limit 1", "type": "query", "params": [], "bindings": ["1", "location_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.285225, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'location_create') limit 1", "type": "query", "params": [], "bindings": ["1", "location_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.289343, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'location_update') limit 1", "type": "query", "params": [], "bindings": ["1", "location_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.294573, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'location_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "location_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.298829, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'location_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "location_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3029501, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'review_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "review_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.307212, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'system_log_view') limit 1", "type": "query", "params": [], "bindings": ["1", "system_log_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.312829, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'theme_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "theme_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.317266, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'social_manage_forum') limit 1", "type": "query", "params": [], "bindings": ["1", "social_manage_forum"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.321832, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'plugin_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "plugin_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.326902, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'vendor_payout_view') limit 1", "type": "query", "params": [], "bindings": ["1", "vendor_payout_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.331044, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'vendor_payout_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "vendor_payout_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.335911, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'popup_view') limit 1", "type": "query", "params": [], "bindings": ["1", "popup_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3412032, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'popup_create') limit 1", "type": "query", "params": [], "bindings": ["1", "popup_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3476741, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'popup_update') limit 1", "type": "query", "params": [], "bindings": ["1", "popup_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.352692, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'popup_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "popup_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.356961, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_view') limit 1", "type": "query", "params": [], "bindings": ["1", "hotel_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3618371, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_create') limit 1", "type": "query", "params": [], "bindings": ["1", "hotel_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.36604, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_update') limit 1", "type": "query", "params": [], "bindings": ["1", "hotel_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.370328, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "hotel_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3743198, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "hotel_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3800042, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["1", "hotel_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.38426, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_view') limit 1", "type": "query", "params": [], "bindings": ["1", "space_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.388181, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_create') limit 1", "type": "query", "params": [], "bindings": ["1", "space_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3929908, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_update') limit 1", "type": "query", "params": [], "bindings": ["1", "space_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.397028, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "space_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.402628, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "space_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.408236, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["1", "space_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.413335, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_view') limit 1", "type": "query", "params": [], "bindings": ["1", "car_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.417661, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_create') limit 1", "type": "query", "params": [], "bindings": ["1", "car_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.421615, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_update') limit 1", "type": "query", "params": [], "bindings": ["1", "car_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.426424, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "car_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.430809, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "car_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.436222, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["1", "car_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.440727, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_view') limit 1", "type": "query", "params": [], "bindings": ["1", "event_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.446886, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_create') limit 1", "type": "query", "params": [], "bindings": ["1", "event_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.451578, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_update') limit 1", "type": "query", "params": [], "bindings": ["1", "event_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.455688, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "event_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.461024, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "event_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.465935, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["1", "event_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.47131, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_view') limit 1", "type": "query", "params": [], "bindings": ["1", "tour_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.477828, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_create') limit 1", "type": "query", "params": [], "bindings": ["1", "tour_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.483152, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_update') limit 1", "type": "query", "params": [], "bindings": ["1", "tour_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.4885778, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "tour_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.497064, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "tour_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.501262, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["1", "tour_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5051112, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'flight_view') limit 1", "type": "query", "params": [], "bindings": ["1", "flight_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5107641, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'flight_create') limit 1", "type": "query", "params": [], "bindings": ["1", "flight_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.515713, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'flight_update') limit 1", "type": "query", "params": [], "bindings": ["1", "flight_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.519858, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'flight_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "flight_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5240068, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'flight_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "flight_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.528965, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'flight_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["1", "flight_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5329819, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'boat_view') limit 1", "type": "query", "params": [], "bindings": ["1", "boat_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.536911, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'boat_create') limit 1", "type": "query", "params": [], "bindings": ["1", "boat_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.541831, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'boat_update') limit 1", "type": "query", "params": [], "bindings": ["1", "boat_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.54776, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'boat_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "boat_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.551984, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'boat_manage_others') limit 1", "type": "query", "params": [], "bindings": ["1", "boat_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.556489, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'boat_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["1", "boat_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.561549, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'coupon_view') limit 1", "type": "query", "params": [], "bindings": ["1", "coupon_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.565886, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'coupon_create') limit 1", "type": "query", "params": [], "bindings": ["1", "coupon_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.569845, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'coupon_update') limit 1", "type": "query", "params": [], "bindings": ["1", "coupon_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.574029, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'coupon_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "coupon_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5795321, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ai_text_generate') limit 1", "type": "query", "params": [], "bindings": ["1", "ai_text_generate"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.5837278, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_view') limit 1", "type": "query", "params": [], "bindings": ["1", "support_topic_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.588304, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_create') limit 1", "type": "query", "params": [], "bindings": ["1", "support_topic_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.592874, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_update') limit 1", "type": "query", "params": [], "bindings": ["1", "support_topic_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.596898, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "support_topic_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6019502, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_category') limit 1", "type": "query", "params": [], "bindings": ["1", "support_topic_category"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.607385, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_view') limit 1", "type": "query", "params": [], "bindings": ["1", "support_ticket_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6128402, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_create') limit 1", "type": "query", "params": [], "bindings": ["1", "support_ticket_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6171432, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_update') limit 1", "type": "query", "params": [], "bindings": ["1", "support_ticket_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.621074, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_delete') limit 1", "type": "query", "params": [], "bindings": ["1", "support_ticket_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6255732, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_reply') limit 1", "type": "query", "params": [], "bindings": ["1", "support_ticket_reply"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.629811, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_category') limit 1", "type": "query", "params": [], "bindings": ["1", "support_ticket_category"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.634084, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "support_ticket_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.638109, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ratehawk_view') limit 1", "type": "query", "params": [], "bindings": ["1", "ratehawk_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.643339, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "insert into `core_role_permissions` (`role_id`, `permission`, `create_user`, `updated_at`, `created_at`) values (1, 'ratehawk_view', 7, '2025-07-09 18:12:05', '2025-07-09 18:12:05')", "type": "query", "params": [], "bindings": ["1", "ratehawk_view", "7", "2025-07-09 18:12:05", "2025-07-09 18:12:05"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 60}, {"index": 27, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 28, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.650238, "duration": 0.00361, "duration_str": "3.61ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:60", "source": "app/BaseModel.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=60", "ajax": false, "filename": "BaseModel.php", "line": "60"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ratehawk_manage') limit 1", "type": "query", "params": [], "bindings": ["1", "ratehawk_manage"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6573522, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "insert into `core_role_permissions` (`role_id`, `permission`, `create_user`, `updated_at`, `created_at`) values (1, 'ratehawk_manage', 7, '2025-07-09 18:12:05', '2025-07-09 18:12:05')", "type": "query", "params": [], "bindings": ["1", "ratehawk_manage", "7", "2025-07-09 18:12:05", "2025-07-09 18:12:05"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 60}, {"index": 27, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 28, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6627781, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:60", "source": "app/BaseModel.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=60", "ajax": false, "filename": "BaseModel.php", "line": "60"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ratehawk_settings') limit 1", "type": "query", "params": [], "bindings": ["1", "ratehawk_settings"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.667694, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "insert into `core_role_permissions` (`role_id`, `permission`, `create_user`, `updated_at`, `created_at`) values (1, 'ratehawk_settings', 7, '2025-07-09 18:12:05', '2025-07-09 18:12:05')", "type": "query", "params": [], "bindings": ["1", "ratehawk_settings", "7", "2025-07-09 18:12:05", "2025-07-09 18:12:05"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 60}, {"index": 27, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 28, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.672101, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:60", "source": "app/BaseModel.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=60", "ajax": false, "filename": "BaseModel.php", "line": "60"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ratehawk_bookings_view') limit 1", "type": "query", "params": [], "bindings": ["1", "ratehawk_bookings_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.678699, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "insert into `core_role_permissions` (`role_id`, `permission`, `create_user`, `updated_at`, `created_at`) values (1, 'ratehawk_bookings_view', 7, '2025-07-09 18:12:05', '2025-07-09 18:12:05')", "type": "query", "params": [], "bindings": ["1", "ratehawk_bookings_view", "7", "2025-07-09 18:12:05", "2025-07-09 18:12:05"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 60}, {"index": 27, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 28, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.683425, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:60", "source": "app/BaseModel.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=60", "ajax": false, "filename": "BaseModel.php", "line": "60"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ratehawk_bookings_create') limit 1", "type": "query", "params": [], "bindings": ["1", "ratehawk_bookings_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.688745, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "insert into `core_role_permissions` (`role_id`, `permission`, `create_user`, `updated_at`, `created_at`) values (1, 'ratehawk_bookings_create', 7, '2025-07-09 18:12:05', '2025-07-09 18:12:05')", "type": "query", "params": [], "bindings": ["1", "ratehawk_bookings_create", "7", "2025-07-09 18:12:05", "2025-07-09 18:12:05"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 60}, {"index": 27, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 28, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6939511, "duration": 0.00226, "duration_str": "2.26ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:60", "source": "app/BaseModel.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=60", "ajax": false, "filename": "BaseModel.php", "line": "60"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 1 and `permission` = 'ratehawk_bookings_cancel') limit 1", "type": "query", "params": [], "bindings": ["1", "ratehawk_bookings_cancel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.699828, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "insert into `core_role_permissions` (`role_id`, `permission`, `create_user`, `updated_at`, `created_at`) values (1, 'ratehawk_bookings_cancel', 7, '2025-07-09 18:12:05', '2025-07-09 18:12:05')", "type": "query", "params": [], "bindings": ["1", "ratehawk_bookings_cancel", "7", "2025-07-09 18:12:05", "2025-07-09 18:12:05"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 60}, {"index": 27, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 28, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.704757, "duration": 0.00261, "duration_str": "2.61ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:60", "source": "app/BaseModel.php:60", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=60", "ajax": false, "filename": "BaseModel.php", "line": "60"}, "connection": "mazar_travel"}, {"sql": "delete from `core_role_permissions` where `role_id` = 1 and `id` not in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 46, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 47, 48, 65, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 124, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 167, 168, 169, 170, 171, 172)", "type": "query", "params": [], "bindings": ["1", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27", "28", "29", "30", "31", "32", "46", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "47", "48", "65", "49", "50", "51", "52", "53", "54", "55", "56", "57", "58", "59", "60", "61", "62", "63", "64", "66", "67", "68", "69", "70", "71", "72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "124", "112", "113", "114", "115", "116", "117", "118", "119", "120", "121", "122", "123", "167", "168", "169", "170", "171", "172"], "hints": [], "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 85}, {"index": 13, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7140038, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "Role.php:85", "source": "modules/User/Models/Role.php:85", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=85", "ajax": false, "filename": "Role.php", "line": "85"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'enquiry_view') limit 1", "type": "query", "params": [], "bindings": ["2", "enquiry_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.781611, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'enquiry_update') limit 1", "type": "query", "params": [], "bindings": ["2", "enquiry_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7884421, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'news_view') limit 1", "type": "query", "params": [], "bindings": ["2", "news_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.7950392, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'news_create') limit 1", "type": "query", "params": [], "bindings": ["2", "news_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.799921, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'news_update') limit 1", "type": "query", "params": [], "bindings": ["2", "news_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8040051, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'news_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "news_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.811801, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'dashboard_vendor_access') limit 1", "type": "query", "params": [], "bindings": ["2", "dashboard_vendor_access"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8168669, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'media_upload') limit 1", "type": "query", "params": [], "bindings": ["2", "media_upload"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8217652, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'hotel_view') limit 1", "type": "query", "params": [], "bindings": ["2", "hotel_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.827379, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'hotel_create') limit 1", "type": "query", "params": [], "bindings": ["2", "hotel_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.831668, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'hotel_update') limit 1", "type": "query", "params": [], "bindings": ["2", "hotel_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.83562, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'hotel_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "hotel_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.840526, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'space_view') limit 1", "type": "query", "params": [], "bindings": ["2", "space_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.845815, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'space_create') limit 1", "type": "query", "params": [], "bindings": ["2", "space_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.849887, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'space_update') limit 1", "type": "query", "params": [], "bindings": ["2", "space_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8543968, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'space_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "space_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.859525, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'car_view') limit 1", "type": "query", "params": [], "bindings": ["2", "car_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.863806, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'car_create') limit 1", "type": "query", "params": [], "bindings": ["2", "car_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8677828, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'car_update') limit 1", "type": "query", "params": [], "bindings": ["2", "car_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.872188, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'car_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "car_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8775208, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'event_view') limit 1", "type": "query", "params": [], "bindings": ["2", "event_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.881794, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'event_create') limit 1", "type": "query", "params": [], "bindings": ["2", "event_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8858578, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'event_update') limit 1", "type": "query", "params": [], "bindings": ["2", "event_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.8897989, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'event_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "event_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.894729, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'tour_view') limit 1", "type": "query", "params": [], "bindings": ["2", "tour_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.899185, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'tour_create') limit 1", "type": "query", "params": [], "bindings": ["2", "tour_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.903488, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'tour_update') limit 1", "type": "query", "params": [], "bindings": ["2", "tour_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.909383, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'tour_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "tour_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9154549, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'flight_view') limit 1", "type": "query", "params": [], "bindings": ["2", "flight_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.920343, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'flight_create') limit 1", "type": "query", "params": [], "bindings": ["2", "flight_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.926723, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'flight_update') limit 1", "type": "query", "params": [], "bindings": ["2", "flight_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.931729, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'flight_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "flight_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.935802, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'boat_view') limit 1", "type": "query", "params": [], "bindings": ["2", "boat_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.939732, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'boat_create') limit 1", "type": "query", "params": [], "bindings": ["2", "boat_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.945796, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'boat_update') limit 1", "type": "query", "params": [], "bindings": ["2", "boat_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9506161, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'boat_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "boat_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.955017, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'boat_manage_others') limit 1", "type": "query", "params": [], "bindings": ["2", "boat_manage_others"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.959773, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'boat_manage_attributes') limit 1", "type": "query", "params": [], "bindings": ["2", "boat_manage_attributes"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.964854, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'coupon_view') limit 1", "type": "query", "params": [], "bindings": ["2", "coupon_view"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.969502, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'coupon_create') limit 1", "type": "query", "params": [], "bindings": ["2", "coupon_create"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.975538, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'coupon_update') limit 1", "type": "query", "params": [], "bindings": ["2", "coupon_update"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9808152, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "select * from `core_role_permissions` where (`role_id` = 2 and `permission` = 'coupon_delete') limit 1", "type": "query", "params": [], "bindings": ["2", "coupon_delete"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 77}, {"index": 21, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.985968, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Role.php:77", "source": "modules/User/Models/Role.php:77", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=77", "ajax": false, "filename": "Role.php", "line": "77"}, "connection": "mazar_travel"}, {"sql": "delete from `core_role_permissions` where `role_id` = 2 and `id` not in (151, 152, 147, 148, 149, 150, 130, 125, 135, 136, 137, 138, 131, 132, 133, 134, 139, 140, 141, 142, 143, 144, 145, 146, 126, 127, 128, 129, 154, 153, 155, 156, 158, 157, 159, 160, 161, 162, 163, 164, 165, 166)", "type": "query", "params": [], "bindings": ["2", "151", "152", "147", "148", "149", "150", "130", "125", "135", "136", "137", "138", "131", "132", "133", "134", "139", "140", "141", "142", "143", "144", "145", "146", "126", "127", "128", "129", "154", "153", "155", "156", "158", "157", "159", "160", "161", "162", "163", "164", "165", "166"], "hints": [], "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 85}, {"index": 13, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 327}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.990468, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "Role.php:85", "source": "modules/User/Models/Role.php:85", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=85", "ajax": false, "filename": "Role.php", "line": "85"}, "connection": "mazar_travel"}, {"sql": "delete from `core_role_permissions` where `role_id` = 3 and 1 = 1", "type": "query", "params": [], "bindings": ["3"], "hints": [], "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 85}, {"index": 13, "namespace": null, "name": "modules/User/Admin/RoleController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Admin\\RoleController.php", "line": 323}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.009382, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Role.php:85", "source": "modules/User/Models/Role.php:85", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=85", "ajax": false, "filename": "Role.php", "line": "85"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\User\\Models\\RolePermission": {"value": 167, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRolePermission.php&line=1", "ajax": false, "filename": "RolePermission.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 172, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/module/user/role/permission_matrix\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => []\n]", "bc_current_currency": "", "PHPDEBUGBAR_STACK_DATA": "[]", "success": "Permission Matrix updated"}, "request": {"path_info": "/admin/module/user/role/save_permissions", "status_code": "<pre class=sf-dump id=sf-dump-109973610 data-indent-pad=\"  \"><span class=sf-dump-num>302</span>\n</pre><script>Sfdump(\"sf-dump-109973610\", {\"maxDepth\":0})</script>\n", "status_text": "Found", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-491557955 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-491557955\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1122103336 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>matrix</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1</span> => <span class=sf-dump-note>array:131</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">report_view</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">contact_manage</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"17 characters\">newsletter_manage</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">language_manage</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"20 characters\">language_translation</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"12 characters\">booking_view</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">booking_update</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"21 characters\">booking_manage_others</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"12 characters\">enquiry_view</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"14 characters\">enquiry_update</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"21 characters\">enquiry_manage_others</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"13 characters\">template_view</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"15 characters\">template_create</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"15 characters\">template_update</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"15 characters\">template_delete</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"9 characters\">news_view</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"11 characters\">news_create</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"11 characters\">news_update</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"11 characters\">news_delete</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"18 characters\">news_manage_others</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"11 characters\">role_manage</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"9 characters\">role_view</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"11 characters\">role_create</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"11 characters\">role_update</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"11 characters\">role_delete</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"15 characters\">permission_view</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"17 characters\">permission_create</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"17 characters\">permission_update</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"17 characters\">permission_delete</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"16 characters\">dashboard_access</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"23 characters\">dashboard_vendor_access</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"14 characters\">setting_update</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"12 characters\">setting_view</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"14 characters\">setting_update</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"9 characters\">menu_view</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"11 characters\">menu_create</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"11 characters\">menu_update</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"11 characters\">menu_delete</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"9 characters\">user_view</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"11 characters\">user_create</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"11 characters\">user_update</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"11 characters\">user_delete</span>\"\n      <span class=sf-dump-index>42</span> => \"<span class=sf-dump-str title=\"9 characters\">page_view</span>\"\n      <span class=sf-dump-index>43</span> => \"<span class=sf-dump-str title=\"11 characters\">page_create</span>\"\n      <span class=sf-dump-index>44</span> => \"<span class=sf-dump-str title=\"11 characters\">page_update</span>\"\n      <span class=sf-dump-index>45</span> => \"<span class=sf-dump-str title=\"11 characters\">page_delete</span>\"\n      <span class=sf-dump-index>46</span> => \"<span class=sf-dump-str title=\"18 characters\">page_manage_others</span>\"\n      <span class=sf-dump-index>47</span> => \"<span class=sf-dump-str title=\"12 characters\">media_upload</span>\"\n      <span class=sf-dump-index>48</span> => \"<span class=sf-dump-str title=\"12 characters\">media_manage</span>\"\n      <span class=sf-dump-index>49</span> => \"<span class=sf-dump-str title=\"19 characters\">media_manage_others</span>\"\n      <span class=sf-dump-index>50</span> => \"<span class=sf-dump-str title=\"13 characters\">location_view</span>\"\n      <span class=sf-dump-index>51</span> => \"<span class=sf-dump-str title=\"15 characters\">location_create</span>\"\n      <span class=sf-dump-index>52</span> => \"<span class=sf-dump-str title=\"15 characters\">location_update</span>\"\n      <span class=sf-dump-index>53</span> => \"<span class=sf-dump-str title=\"15 characters\">location_delete</span>\"\n      <span class=sf-dump-index>54</span> => \"<span class=sf-dump-str title=\"22 characters\">location_manage_others</span>\"\n      <span class=sf-dump-index>55</span> => \"<span class=sf-dump-str title=\"20 characters\">review_manage_others</span>\"\n      <span class=sf-dump-index>56</span> => \"<span class=sf-dump-str title=\"15 characters\">system_log_view</span>\"\n      <span class=sf-dump-index>57</span> => \"<span class=sf-dump-str title=\"12 characters\">theme_manage</span>\"\n      <span class=sf-dump-index>58</span> => \"<span class=sf-dump-str title=\"19 characters\">social_manage_forum</span>\"\n      <span class=sf-dump-index>59</span> => \"<span class=sf-dump-str title=\"13 characters\">plugin_manage</span>\"\n      <span class=sf-dump-index>60</span> => \"<span class=sf-dump-str title=\"18 characters\">vendor_payout_view</span>\"\n      <span class=sf-dump-index>61</span> => \"<span class=sf-dump-str title=\"20 characters\">vendor_payout_manage</span>\"\n      <span class=sf-dump-index>62</span> => \"<span class=sf-dump-str title=\"10 characters\">popup_view</span>\"\n      <span class=sf-dump-index>63</span> => \"<span class=sf-dump-str title=\"12 characters\">popup_create</span>\"\n      <span class=sf-dump-index>64</span> => \"<span class=sf-dump-str title=\"12 characters\">popup_update</span>\"\n      <span class=sf-dump-index>65</span> => \"<span class=sf-dump-str title=\"12 characters\">popup_delete</span>\"\n      <span class=sf-dump-index>66</span> => \"<span class=sf-dump-str title=\"10 characters\">hotel_view</span>\"\n      <span class=sf-dump-index>67</span> => \"<span class=sf-dump-str title=\"12 characters\">hotel_create</span>\"\n      <span class=sf-dump-index>68</span> => \"<span class=sf-dump-str title=\"12 characters\">hotel_update</span>\"\n      <span class=sf-dump-index>69</span> => \"<span class=sf-dump-str title=\"12 characters\">hotel_delete</span>\"\n      <span class=sf-dump-index>70</span> => \"<span class=sf-dump-str title=\"19 characters\">hotel_manage_others</span>\"\n      <span class=sf-dump-index>71</span> => \"<span class=sf-dump-str title=\"23 characters\">hotel_manage_attributes</span>\"\n      <span class=sf-dump-index>72</span> => \"<span class=sf-dump-str title=\"10 characters\">space_view</span>\"\n      <span class=sf-dump-index>73</span> => \"<span class=sf-dump-str title=\"12 characters\">space_create</span>\"\n      <span class=sf-dump-index>74</span> => \"<span class=sf-dump-str title=\"12 characters\">space_update</span>\"\n      <span class=sf-dump-index>75</span> => \"<span class=sf-dump-str title=\"12 characters\">space_delete</span>\"\n      <span class=sf-dump-index>76</span> => \"<span class=sf-dump-str title=\"19 characters\">space_manage_others</span>\"\n      <span class=sf-dump-index>77</span> => \"<span class=sf-dump-str title=\"23 characters\">space_manage_attributes</span>\"\n      <span class=sf-dump-index>78</span> => \"<span class=sf-dump-str title=\"8 characters\">car_view</span>\"\n      <span class=sf-dump-index>79</span> => \"<span class=sf-dump-str title=\"10 characters\">car_create</span>\"\n      <span class=sf-dump-index>80</span> => \"<span class=sf-dump-str title=\"10 characters\">car_update</span>\"\n      <span class=sf-dump-index>81</span> => \"<span class=sf-dump-str title=\"10 characters\">car_delete</span>\"\n      <span class=sf-dump-index>82</span> => \"<span class=sf-dump-str title=\"17 characters\">car_manage_others</span>\"\n      <span class=sf-dump-index>83</span> => \"<span class=sf-dump-str title=\"21 characters\">car_manage_attributes</span>\"\n      <span class=sf-dump-index>84</span> => \"<span class=sf-dump-str title=\"10 characters\">event_view</span>\"\n      <span class=sf-dump-index>85</span> => \"<span class=sf-dump-str title=\"12 characters\">event_create</span>\"\n      <span class=sf-dump-index>86</span> => \"<span class=sf-dump-str title=\"12 characters\">event_update</span>\"\n      <span class=sf-dump-index>87</span> => \"<span class=sf-dump-str title=\"12 characters\">event_delete</span>\"\n      <span class=sf-dump-index>88</span> => \"<span class=sf-dump-str title=\"19 characters\">event_manage_others</span>\"\n      <span class=sf-dump-index>89</span> => \"<span class=sf-dump-str title=\"23 characters\">event_manage_attributes</span>\"\n      <span class=sf-dump-index>90</span> => \"<span class=sf-dump-str title=\"9 characters\">tour_view</span>\"\n      <span class=sf-dump-index>91</span> => \"<span class=sf-dump-str title=\"11 characters\">tour_create</span>\"\n      <span class=sf-dump-index>92</span> => \"<span class=sf-dump-str title=\"11 characters\">tour_update</span>\"\n      <span class=sf-dump-index>93</span> => \"<span class=sf-dump-str title=\"11 characters\">tour_delete</span>\"\n      <span class=sf-dump-index>94</span> => \"<span class=sf-dump-str title=\"18 characters\">tour_manage_others</span>\"\n      <span class=sf-dump-index>95</span> => \"<span class=sf-dump-str title=\"22 characters\">tour_manage_attributes</span>\"\n      <span class=sf-dump-index>96</span> => \"<span class=sf-dump-str title=\"11 characters\">flight_view</span>\"\n      <span class=sf-dump-index>97</span> => \"<span class=sf-dump-str title=\"13 characters\">flight_create</span>\"\n      <span class=sf-dump-index>98</span> => \"<span class=sf-dump-str title=\"13 characters\">flight_update</span>\"\n      <span class=sf-dump-index>99</span> => \"<span class=sf-dump-str title=\"13 characters\">flight_delete</span>\"\n      <span class=sf-dump-index>100</span> => \"<span class=sf-dump-str title=\"20 characters\">flight_manage_others</span>\"\n      <span class=sf-dump-index>101</span> => \"<span class=sf-dump-str title=\"24 characters\">flight_manage_attributes</span>\"\n      <span class=sf-dump-index>102</span> => \"<span class=sf-dump-str title=\"9 characters\">boat_view</span>\"\n      <span class=sf-dump-index>103</span> => \"<span class=sf-dump-str title=\"11 characters\">boat_create</span>\"\n      <span class=sf-dump-index>104</span> => \"<span class=sf-dump-str title=\"11 characters\">boat_update</span>\"\n      <span class=sf-dump-index>105</span> => \"<span class=sf-dump-str title=\"11 characters\">boat_delete</span>\"\n      <span class=sf-dump-index>106</span> => \"<span class=sf-dump-str title=\"18 characters\">boat_manage_others</span>\"\n      <span class=sf-dump-index>107</span> => \"<span class=sf-dump-str title=\"22 characters\">boat_manage_attributes</span>\"\n      <span class=sf-dump-index>108</span> => \"<span class=sf-dump-str title=\"11 characters\">coupon_view</span>\"\n      <span class=sf-dump-index>109</span> => \"<span class=sf-dump-str title=\"13 characters\">coupon_create</span>\"\n      <span class=sf-dump-index>110</span> => \"<span class=sf-dump-str title=\"13 characters\">coupon_update</span>\"\n      <span class=sf-dump-index>111</span> => \"<span class=sf-dump-str title=\"13 characters\">coupon_delete</span>\"\n      <span class=sf-dump-index>112</span> => \"<span class=sf-dump-str title=\"16 characters\">ai_text_generate</span>\"\n      <span class=sf-dump-index>113</span> => \"<span class=sf-dump-str title=\"18 characters\">support_topic_view</span>\"\n      <span class=sf-dump-index>114</span> => \"<span class=sf-dump-str title=\"20 characters\">support_topic_create</span>\"\n      <span class=sf-dump-index>115</span> => \"<span class=sf-dump-str title=\"20 characters\">support_topic_update</span>\"\n      <span class=sf-dump-index>116</span> => \"<span class=sf-dump-str title=\"20 characters\">support_topic_delete</span>\"\n      <span class=sf-dump-index>117</span> => \"<span class=sf-dump-str title=\"22 characters\">support_topic_category</span>\"\n      <span class=sf-dump-index>118</span> => \"<span class=sf-dump-str title=\"19 characters\">support_ticket_view</span>\"\n      <span class=sf-dump-index>119</span> => \"<span class=sf-dump-str title=\"21 characters\">support_ticket_create</span>\"\n      <span class=sf-dump-index>120</span> => \"<span class=sf-dump-str title=\"21 characters\">support_ticket_update</span>\"\n      <span class=sf-dump-index>121</span> => \"<span class=sf-dump-str title=\"21 characters\">support_ticket_delete</span>\"\n      <span class=sf-dump-index>122</span> => \"<span class=sf-dump-str title=\"20 characters\">support_ticket_reply</span>\"\n      <span class=sf-dump-index>123</span> => \"<span class=sf-dump-str title=\"23 characters\">support_ticket_category</span>\"\n      <span class=sf-dump-index>124</span> => \"<span class=sf-dump-str title=\"21 characters\">support_ticket_manage</span>\"\n      <span class=sf-dump-index>125</span> => \"<span class=sf-dump-str title=\"13 characters\">ratehawk_view</span>\"\n      <span class=sf-dump-index>126</span> => \"<span class=sf-dump-str title=\"15 characters\">ratehawk_manage</span>\"\n      <span class=sf-dump-index>127</span> => \"<span class=sf-dump-str title=\"17 characters\">ratehawk_settings</span>\"\n      <span class=sf-dump-index>128</span> => \"<span class=sf-dump-str title=\"22 characters\">ratehawk_bookings_view</span>\"\n      <span class=sf-dump-index>129</span> => \"<span class=sf-dump-str title=\"24 characters\">ratehawk_bookings_create</span>\"\n      <span class=sf-dump-index>130</span> => \"<span class=sf-dump-str title=\"24 characters\">ratehawk_bookings_cancel</span>\"\n    </samp>]\n    <span class=sf-dump-key>2</span> => <span class=sf-dump-note>array:42</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">enquiry_view</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">enquiry_update</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">news_view</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"11 characters\">news_create</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"11 characters\">news_update</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"11 characters\">news_delete</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"23 characters\">dashboard_vendor_access</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"12 characters\">media_upload</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"10 characters\">hotel_view</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"12 characters\">hotel_create</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"12 characters\">hotel_update</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"12 characters\">hotel_delete</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"10 characters\">space_view</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"12 characters\">space_create</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"12 characters\">space_update</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"12 characters\">space_delete</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"8 characters\">car_view</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"10 characters\">car_create</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"10 characters\">car_update</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"10 characters\">car_delete</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"10 characters\">event_view</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"12 characters\">event_create</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"12 characters\">event_update</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"12 characters\">event_delete</span>\"\n      <span class=sf-dump-index>24</span> => \"<span class=sf-dump-str title=\"9 characters\">tour_view</span>\"\n      <span class=sf-dump-index>25</span> => \"<span class=sf-dump-str title=\"11 characters\">tour_create</span>\"\n      <span class=sf-dump-index>26</span> => \"<span class=sf-dump-str title=\"11 characters\">tour_update</span>\"\n      <span class=sf-dump-index>27</span> => \"<span class=sf-dump-str title=\"11 characters\">tour_delete</span>\"\n      <span class=sf-dump-index>28</span> => \"<span class=sf-dump-str title=\"11 characters\">flight_view</span>\"\n      <span class=sf-dump-index>29</span> => \"<span class=sf-dump-str title=\"13 characters\">flight_create</span>\"\n      <span class=sf-dump-index>30</span> => \"<span class=sf-dump-str title=\"13 characters\">flight_update</span>\"\n      <span class=sf-dump-index>31</span> => \"<span class=sf-dump-str title=\"13 characters\">flight_delete</span>\"\n      <span class=sf-dump-index>32</span> => \"<span class=sf-dump-str title=\"9 characters\">boat_view</span>\"\n      <span class=sf-dump-index>33</span> => \"<span class=sf-dump-str title=\"11 characters\">boat_create</span>\"\n      <span class=sf-dump-index>34</span> => \"<span class=sf-dump-str title=\"11 characters\">boat_update</span>\"\n      <span class=sf-dump-index>35</span> => \"<span class=sf-dump-str title=\"11 characters\">boat_delete</span>\"\n      <span class=sf-dump-index>36</span> => \"<span class=sf-dump-str title=\"18 characters\">boat_manage_others</span>\"\n      <span class=sf-dump-index>37</span> => \"<span class=sf-dump-str title=\"22 characters\">boat_manage_attributes</span>\"\n      <span class=sf-dump-index>38</span> => \"<span class=sf-dump-str title=\"11 characters\">coupon_view</span>\"\n      <span class=sf-dump-index>39</span> => \"<span class=sf-dump-str title=\"13 characters\">coupon_create</span>\"\n      <span class=sf-dump-index>40</span> => \"<span class=sf-dump-str title=\"13 characters\">coupon_update</span>\"\n      <span class=sf-dump-index>41</span> => \"<span class=sf-dump-str title=\"13 characters\">coupon_delete</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1122103336\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1551961443 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6125</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/admin/module/user/role/permission_matrix</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2130 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkMvN08rRzh0ZTJtNUhKZVhFYTYreXc9PSIsInZhbHVlIjoidnJzMk9IdVBtMFl3MzhtOHBRajJkK254WXdqWWNQZlhVaEtTc0dFUGR0RTkvSWJTSUxGcExQbUVTQ2RWeVNrVkNDbjU4eG5kWVp2aXpJalBSV0tzQ0pzRHhteEE2VlhBMXUzSkJjeHdQUFA4S2UrVThNanBBMWxnN0o0cGlnSTEiLCJtYWMiOiJkYzAyZGNhNzFlMTNlZmY3NDE5OTQ3ZGM5ZjU3YWU1MjkyYzRjYTg3NGFjNDFiY2I2NjBjNWEzY2Y2MjVhOGNiIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IlROL3pDNStvTE82VTN0dUlZWWd6UFE9PSIsInZhbHVlIjoiZERvbExwV0Z4YlMzMjRZL1oyVmNONW5lTVlJakxMOU5PTXd1ZHFEMlhURmxsbTBDMTNnMlo4SEg5WWJxd0owVFRybEVRSXFSZDV6VGRUbHNuUm5RVWJKWmZIMy9Ob3NVa0xLK2JpZEJpUy9pdElIWmQ4eUFhN1pDTGpRSW9DaHgiLCJtYWMiOiIzMTE2ODY3ZjQ0OTYyMjJjYjk3MTRjMDQyMTNhM2NlYzMxY2U4ZGJkZWRhMDQyMjA0NDJjNjNjZGFiOTc3YzJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1551961443\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1139603877 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ncJc6JL7D4OUdNqnMyW2JaSVePbveurWfiadoF7Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1139603877\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1429295443 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 18:12:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/admin/module/user/role/permission_matrix</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkYvdE83K2xSU0twK09wWDRwdTJHZHc9PSIsInZhbHVlIjoiV3BHMnJDS2NteWZ6NXNDQVp3TGV0VjhCVGMrM0dLMXBSeHhlVjNHVjJ5ajZySG1GaXBzYU9abUtzOEZtT2RwaExXTWhjb3dwM3BXWFRmS0svTE41dW9Kd25kUUluVmJFNEI4RVNnem4xQzB0UjkxZkVKb1UwMlB6Z0RnTDBmdXYiLCJtYWMiOiIzNjY0NmU1OTM4MjEwNGYxYWQ4NWZiNmQ2YmE0YjIxNzBhMWNiOTEwNzEzYTNjMmQzMTlmOTk3ZWQ0MDVlZDBmIiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:12:06 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IndYZlV0bjQvdy9nbHp1c1dOOGNlK0E9PSIsInZhbHVlIjoib0k5dXJwUjhXZ1VDNVpJRk9zZTB2eE4ySXJEUHM5SHVxL1l2blE3cm1OZ2NvQ3c0MWN2dW1VakRBRDIwQkJBVEZzeHZFWG5YMW5QcU43bFNMTHlBNElJQ2ZFQm5jSkQzaDVybDNDakRmU3hpWFRNTFRBKzF1TSsvcU5UbEMvY3oiLCJtYWMiOiJjOWRiZDljZGYxYTRlMzFiOWEzMjliM2FiNDMxNGIwODM0NjM0YzE3NGRlNTA2NDBmMWU0MTJiM2ZkMDgyMWM0IiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:12:06 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkYvdE83K2xSU0twK09wWDRwdTJHZHc9PSIsInZhbHVlIjoiV3BHMnJDS2NteWZ6NXNDQVp3TGV0VjhCVGMrM0dLMXBSeHhlVjNHVjJ5ajZySG1GaXBzYU9abUtzOEZtT2RwaExXTWhjb3dwM3BXWFRmS0svTE41dW9Kd25kUUluVmJFNEI4RVNnem4xQzB0UjkxZkVKb1UwMlB6Z0RnTDBmdXYiLCJtYWMiOiIzNjY0NmU1OTM4MjEwNGYxYWQ4NWZiNmQ2YmE0YjIxNzBhMWNiOTEwNzEzYTNjMmQzMTlmOTk3ZWQ0MDVlZDBmIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:12:06 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IndYZlV0bjQvdy9nbHp1c1dOOGNlK0E9PSIsInZhbHVlIjoib0k5dXJwUjhXZ1VDNVpJRk9zZTB2eE4ySXJEUHM5SHVxL1l2blE3cm1OZ2NvQ3c0MWN2dW1VakRBRDIwQkJBVEZzeHZFWG5YMW5QcU43bFNMTHlBNElJQ2ZFQm5jSkQzaDVybDNDakRmU3hpWFRNTFRBKzF1TSsvcU5UbEMvY3oiLCJtYWMiOiJjOWRiZDljZGYxYTRlMzFiOWEzMjliM2FiNDMxNGIwODM0NjM0YzE3NGRlNTA2NDBmMWU0MTJiM2ZkMDgyMWM0IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:12:06 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429295443\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1684453113 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">http://127.0.0.1:8000/admin/module/user/role/permission_matrix</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Permission Matrix updated</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1684453113\", {\"maxDepth\":0})</script>\n"}}