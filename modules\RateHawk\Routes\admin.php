<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin routes for RateHawk module.
|
*/

Route::group(['middleware' => ['auth', 'verified']], function () {
    
    // Dashboard
    Route::get('/', 'RateHawkController@index')->name('ratehawk.admin.index');
    
    // Settings
    Route::get('/settings', 'RateHawkController@settings')->name('ratehawk.admin.settings');
    Route::post('/settings', 'RateHawkController@saveSettings')->name('ratehawk.admin.settings.save');
    
    // Bookings Management
    Route::group(['prefix' => 'bookings'], function () {
        Route::get('/', 'BookingController@index')->name('ratehawk.admin.bookings.index');
        Route::get('/create', 'BookingController@create')->name('ratehawk.admin.bookings.create');
        Route::post('/store', 'BookingController@store')->name('ratehawk.admin.bookings.store');
        Route::get('/{id}', 'BookingController@show')->name('ratehawk.admin.bookings.show');
        Route::get('/{id}/edit', 'BookingController@edit')->name('ratehawk.admin.bookings.edit');
        Route::put('/{id}', 'BookingController@update')->name('ratehawk.admin.bookings.update');
        Route::delete('/{id}', 'BookingController@destroy')->name('ratehawk.admin.bookings.destroy');
        Route::post('/{id}/cancel', 'BookingController@cancel')->name('ratehawk.admin.bookings.cancel');
    });
    
    // API Testing
    Route::group(['prefix' => 'api-test'], function () {
        Route::get('/', 'ApiTestController@index')->name('ratehawk.admin.api-test.index');
        Route::post('/test-connection', 'ApiTestController@testConnection')->name('ratehawk.admin.api-test.connection');
        Route::post('/search-hotels', 'ApiTestController@searchHotels')->name('ratehawk.admin.api-test.search');
        Route::post('/get-endpoints', 'ApiTestController@getEndpoints')->name('ratehawk.admin.api-test.endpoints');
    });
    
    // Logs
    Route::group(['prefix' => 'logs'], function () {
        Route::get('/', 'LogController@index')->name('ratehawk.admin.logs.index');
        Route::get('/{id}', 'LogController@show')->name('ratehawk.admin.logs.show');
        Route::delete('/{id}', 'LogController@destroy')->name('ratehawk.admin.logs.destroy');
        Route::post('/clear', 'LogController@clear')->name('ratehawk.admin.logs.clear');
    });
});
