<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Modules\RateHawk\Models\RateHawkSearchCache;
use Modules\RateHawk\Models\RateHawkHotelMapping;
use Carbon\Carbon;

class CacheService
{
    // Cache TTL constants (in minutes)
    const STATIC_DATA_TTL = 1440;      // 24 hours
    const SEMI_DYNAMIC_TTL = 360;      // 6 hours  
    const DYNAMIC_DATA_TTL = 15;       // 15 minutes
    const SEARCH_RESULTS_TTL = 30;     // 30 minutes
    const PRICING_DATA_TTL = 5;        // 5 minutes

    /**
     * Cache hotel static data
     */
    public function cacheHotelStatic(string $hotelId, array $data): bool
    {
        try {
            $key = $this->getHotelStaticKey($hotelId);
            
            // Cache in Redis
            Cache::put($key, $data, now()->addMinutes(self::STATIC_DATA_TTL));
            
            // Also store in database for persistence
            RateHawkHotelMapping::createOrUpdateMapping($hotelId, [
                'hotel_static_data' => $data,
                'last_synced' => now(),
                'data_expires_at' => now()->addMinutes(self::STATIC_DATA_TTL),
                'is_active' => true,
            ]);

            Log::info("Cached hotel static data for hotel: {$hotelId}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to cache hotel static data: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get hotel static data from cache
     */
    public function getHotelStatic(string $hotelId): ?array
    {
        try {
            $key = $this->getHotelStaticKey($hotelId);
            
            // Try Redis first
            $data = Cache::get($key);
            if ($data) {
                return $data;
            }

            // Fallback to database
            $mapping = RateHawkHotelMapping::findByRateHawkId($hotelId);
            if ($mapping && $mapping->isCacheValid()) {
                // Restore to Redis
                Cache::put($key, $mapping->hotel_static_data, $mapping->data_expires_at);
                return $mapping->hotel_static_data;
            }

            return null;
        } catch (\Exception $e) {
            Log::error("Failed to get hotel static data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Cache hotel pricing data
     */
    public function cacheHotelPricing(string $hotelId, array $searchParams, array $pricingData): bool
    {
        try {
            $key = $this->getHotelPricingKey($hotelId, $searchParams);
            
            Cache::put($key, $pricingData, now()->addMinutes(self::PRICING_DATA_TTL));
            
            Log::info("Cached hotel pricing data for hotel: {$hotelId}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to cache hotel pricing data: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get hotel pricing data from cache
     */
    public function getHotelPricing(string $hotelId, array $searchParams): ?array
    {
        try {
            $key = $this->getHotelPricingKey($hotelId, $searchParams);
            return Cache::get($key);
        } catch (\Exception $e) {
            Log::error("Failed to get hotel pricing data: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Cache search results
     */
    public function cacheSearchResults(array $searchParams, array $results): bool
    {
        try {
            // Cache in Redis for fast access
            $key = $this->getSearchResultsKey($searchParams);
            Cache::put($key, $results, now()->addMinutes(self::SEARCH_RESULTS_TTL));
            
            // Also store in database for analytics
            RateHawkSearchCache::cacheResults($searchParams, $results, self::SEARCH_RESULTS_TTL);
            
            Log::info("Cached search results for: " . json_encode($searchParams));
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to cache search results: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get search results from cache
     */
    public function getSearchResults(array $searchParams): ?array
    {
        try {
            // Try Redis first
            $key = $this->getSearchResultsKey($searchParams);
            $data = Cache::get($key);
            
            if ($data) {
                return $data;
            }

            // Fallback to database
            $cached = RateHawkSearchCache::findCachedResults($searchParams);
            if ($cached) {
                $cached->recordHit();
                
                // Restore to Redis
                Cache::put($key, $cached->results, $cached->expires_at);
                return $cached->results;
            }

            return null;
        } catch (\Exception $e) {
            Log::error("Failed to get search results: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Cache hotel amenities
     */
    public function cacheHotelAmenities(string $hotelId, array $amenities): bool
    {
        try {
            $key = $this->getHotelAmenitiesKey($hotelId);
            Cache::put($key, $amenities, now()->addMinutes(self::SEMI_DYNAMIC_TTL));
            
            // Update mapping
            $mapping = RateHawkHotelMapping::findByRateHawkId($hotelId);
            if ($mapping) {
                $mapping->update(['amenities_data' => $amenities]);
            }

            return true;
        } catch (\Exception $e) {
            Log::error("Failed to cache hotel amenities: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get hotel amenities from cache
     */
    public function getHotelAmenities(string $hotelId): ?array
    {
        try {
            $key = $this->getHotelAmenitiesKey($hotelId);
            
            $data = Cache::get($key);
            if ($data) {
                return $data;
            }

            // Fallback to database
            $mapping = RateHawkHotelMapping::findByRateHawkId($hotelId);
            if ($mapping && $mapping->amenities_data) {
                Cache::put($key, $mapping->amenities_data, now()->addMinutes(self::SEMI_DYNAMIC_TTL));
                return $mapping->amenities_data;
            }

            return null;
        } catch (\Exception $e) {
            Log::error("Failed to get hotel amenities: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Invalidate hotel cache
     */
    public function invalidateHotelCache(string $hotelId): bool
    {
        try {
            $keys = [
                $this->getHotelStaticKey($hotelId),
                $this->getHotelAmenitiesKey($hotelId),
            ];

            foreach ($keys as $key) {
                Cache::forget($key);
            }

            // Also clear pricing cache (pattern-based)
            $this->clearHotelPricingCache($hotelId);

            Log::info("Invalidated cache for hotel: {$hotelId}");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to invalidate hotel cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear all search cache
     */
    public function clearSearchCache(): bool
    {
        try {
            // Clear Redis cache with pattern
            $this->clearCacheByPattern('ratehawk:search:*');
            
            // Clear database cache
            RateHawkSearchCache::truncate();
            
            Log::info("Cleared all search cache");
            return true;
        } catch (\Exception $e) {
            Log::error("Failed to clear search cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get cache statistics
     */
    public function getCacheStats(): array
    {
        try {
            return [
                'search_cache' => RateHawkSearchCache::getCacheStats(),
                'hotel_mappings' => RateHawkHotelMapping::getCacheStats(),
                'redis_info' => $this->getRedisInfo(),
            ];
        } catch (\Exception $e) {
            Log::error("Failed to get cache stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Cleanup expired cache
     */
    public function cleanupExpiredCache(): array
    {
        try {
            $searchCleanup = RateHawkSearchCache::cleanupExpired();
            $hotelCleanup = RateHawkHotelMapping::cleanupExpiredCache();
            
            Log::info("Cleaned up expired cache: {$searchCleanup} search entries, {$hotelCleanup} hotel mappings");
            
            return [
                'search_entries_cleaned' => $searchCleanup,
                'hotel_mappings_cleaned' => $hotelCleanup,
            ];
        } catch (\Exception $e) {
            Log::error("Failed to cleanup expired cache: " . $e->getMessage());
            return [];
        }
    }

    // Private helper methods

    private function getHotelStaticKey(string $hotelId): string
    {
        return "ratehawk:hotel:static:{$hotelId}";
    }

    private function getHotelPricingKey(string $hotelId, array $searchParams): string
    {
        $hash = md5(json_encode($searchParams));
        return "ratehawk:hotel:pricing:{$hotelId}:{$hash}";
    }

    private function getSearchResultsKey(array $searchParams): string
    {
        $hash = RateHawkSearchCache::generateSearchHash($searchParams);
        return "ratehawk:search:{$hash}";
    }

    private function getHotelAmenitiesKey(string $hotelId): string
    {
        return "ratehawk:hotel:amenities:{$hotelId}";
    }

    private function clearHotelPricingCache(string $hotelId): void
    {
        $this->clearCacheByPattern("ratehawk:hotel:pricing:{$hotelId}:*");
    }

    private function clearCacheByPattern(string $pattern): void
    {
        // This would need to be implemented based on your Redis setup
        // For now, we'll just log it
        Log::info("Would clear cache pattern: {$pattern}");
    }

    private function getRedisInfo(): array
    {
        try {
            // Basic Redis info - implement based on your Redis setup
            return [
                'connected' => Cache::getStore() instanceof \Illuminate\Cache\RedisStore,
                'driver' => config('cache.default'),
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
