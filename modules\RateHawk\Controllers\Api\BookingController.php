<?php

namespace Modules\RateHawk\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Services\BookingService;
use Modules\RateHawk\Exceptions\RateHawkApiException;

class BookingController extends Controller
{
    protected $bookingService;

    public function __construct(BookingService $bookingService)
    {
        $this->bookingService = $bookingService;
    }

    /**
     * Create a new booking process
     */
    public function create(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $params['user_ip'] = $request->ip();

            $booking = $this->bookingService->createBookingProcess($params);

            return $this->successResponse($booking, 'Booking process created successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create booking: ' . $e->getMessage());
        }
    }

    /**
     * Start booking process
     */
    public function start(Request $request): JsonResponse
    {
        try {
            $params = $request->all();
            $result = $this->bookingService->startBookingProcess($params);

            return $this->successResponse($result, 'Booking process started successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to start booking: ' . $e->getMessage());
        }
    }

    /**
     * Check booking status
     */
    public function status(Request $request, string $orderId): JsonResponse
    {
        try {
            $status = $this->bookingService->checkBookingStatus($orderId);

            return $this->successResponse($status, 'Booking status retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get booking status: ' . $e->getMessage());
        }
    }

    /**
     * Cancel booking
     */
    public function cancel(Request $request, string $orderId): JsonResponse
    {
        try {
            $result = $this->bookingService->cancelBooking($orderId);

            return $this->successResponse($result, 'Booking cancelled successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to cancel booking: ' . $e->getMessage());
        }
    }

    /**
     * Get booking voucher
     */
    public function voucher(Request $request, string $orderId): JsonResponse
    {
        try {
            $voucher = $this->bookingService->getVoucher($orderId);

            return $this->successResponse($voucher, 'Voucher retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get voucher: ' . $e->getMessage());
        }
    }

    /**
     * Get booking invoice
     */
    public function invoice(Request $request, string $orderId): JsonResponse
    {
        try {
            $invoice = $this->bookingService->getInvoice($orderId);

            return $this->successResponse($invoice, 'Invoice retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get invoice: ' . $e->getMessage());
        }
    }

    /**
     * Get bookings list
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $params = $request->only(['from_date', 'to_date', 'status', 'limit', 'offset']);
            $bookings = $this->bookingService->getBookings($params);

            return $this->successResponse($bookings, 'Bookings retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get bookings: ' . $e->getMessage());
        }
    }

    /**
     * Get local booking details
     */
    public function show(Request $request, string $orderId): JsonResponse
    {
        try {
            $booking = $this->bookingService->getLocalBooking($orderId);

            if (!$booking) {
                return $this->errorResponse('Booking not found', 404);
            }

            return $this->successResponse($booking->toArray(), 'Booking details retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get booking details: ' . $e->getMessage());
        }
    }

    /**
     * Get local bookings with filters
     */
    public function localBookings(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['status', 'from_date', 'to_date', 'search', 'per_page']);
            $bookings = $this->bookingService->getLocalBookings($filters);

            return $this->successResponse([
                'bookings' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                ]
            ], 'Local bookings retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get local bookings: ' . $e->getMessage());
        }
    }

    /**
     * Create credit card token
     */
    public function createCreditCardToken(Request $request): JsonResponse
    {
        try {
            $params = $request->only([
                'cc_number', 'cc_holder_name', 'cc_expiry_month', 
                'cc_expiry_year', 'cc_cvc'
            ]);

            $token = $this->bookingService->createCreditCardToken($params);

            return $this->successResponse($token, 'Credit card token created successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create credit card token: ' . $e->getMessage());
        }
    }

    /**
     * Return success response
     */
    protected function successResponse(array $data, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Return error response
     */
    protected function errorResponse(
        string $message, 
        int $statusCode = 500, 
        array $errorDetails = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->toISOString()
        ];

        if (!empty($errorDetails)) {
            $response['error_details'] = $errorDetails;
        }

        return response()->json($response, $statusCode);
    }
}
