<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratehawk_settings', function (Blueprint $table) {
            $table->id();
            $table->string('key')->unique()->index();
            $table->text('value')->nullable();
            $table->string('type', 50)->default('string'); // string, boolean, integer, json, etc.
            $table->text('description')->nullable();
            $table->boolean('is_public')->default(false); // Whether setting can be accessed publicly
            $table->boolean('is_encrypted')->default(false); // Whether value should be encrypted
            $table->timestamps();

            // Index for faster lookups
            $table->index(['key', 'is_public']);
        });

        // Insert default settings
        $defaultSettings = [
            [
                'key' => 'ratehawk_enable',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable RateHawk API integration',
                'is_public' => true,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_environment',
                'value' => 'test',
                'type' => 'string',
                'description' => 'RateHawk API environment (test or production)',
                'is_public' => false,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_key_id',
                'value' => '',
                'type' => 'string',
                'description' => 'RateHawk API Key ID',
                'is_public' => false,
                'is_encrypted' => true,
            ],
            [
                'key' => 'ratehawk_api_key',
                'value' => '',
                'type' => 'string',
                'description' => 'RateHawk API Key',
                'is_public' => false,
                'is_encrypted' => true,
            ],
            [
                'key' => 'ratehawk_default_currency',
                'value' => 'USD',
                'type' => 'string',
                'description' => 'Default currency for RateHawk searches',
                'is_public' => true,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_default_language',
                'value' => 'en',
                'type' => 'string',
                'description' => 'Default language for RateHawk searches',
                'is_public' => true,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_default_residency',
                'value' => 'us',
                'type' => 'string',
                'description' => 'Default residency for RateHawk searches',
                'is_public' => true,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_cache_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable caching for RateHawk API responses',
                'is_public' => false,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_logging_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable logging for RateHawk API requests',
                'is_public' => false,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_webhooks_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable RateHawk webhooks',
                'is_public' => false,
                'is_encrypted' => false,
            ],
            [
                'key' => 'ratehawk_webhook_secret',
                'value' => '',
                'type' => 'string',
                'description' => 'RateHawk webhook secret for signature verification',
                'is_public' => false,
                'is_encrypted' => true,
            ],
        ];

        foreach ($defaultSettings as $setting) {
            DB::table('ratehawk_settings')->insert(array_merge($setting, [
                'created_at' => now(),
                'updated_at' => now(),
            ]));
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratehawk_settings');
    }
};
