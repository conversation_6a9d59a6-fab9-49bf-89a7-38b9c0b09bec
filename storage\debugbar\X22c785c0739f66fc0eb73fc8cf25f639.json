{"__meta": {"id": "X22c785c0739f66fc0eb73fc8cf25f639", "datetime": "2025-07-09 06:49:44", "utime": 1752043784.178103, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:49:36] LOG.warning: strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\wamp64\\www\\mazar\\app\\BaseModel.php on line 206", "message_html": null, "is_string": false, "label": "warning", "time": **********.638396, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752043775.669941, "end": 1752043784.178161, "duration": 8.508219957351685, "duration_str": "8.51s", "measures": [{"label": "Booting", "start": 1752043775.669941, "relative_start": 0, "end": **********.319671, "relative_end": **********.319671, "duration": 0.6497299671173096, "duration_str": "650ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.319694, "relative_start": 0.6497530937194824, "end": 1752043784.178165, "relative_end": 4.0531158447265625e-06, "duration": 7.858470916748047, "duration_str": "7.86s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 10077560, "peak_usage_str": "10MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 89, "templates": [{"name": "1x Page::frontend.detail", "param_count": null, "params": [], "start": **********.641338, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.phpPage::frontend.detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FPage%2FViews%2Ffrontend%2Fdetail.blade.php&line=1", "ajax": false, "filename": "detail.blade.php", "line": "?"}, "render_count": 1, "name_original": "Page::frontend.detail"}, {"name": "1x Template::frontend.blocks.form-search-all-service.index", "param_count": null, "params": [], "start": **********.806372, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/form-search-all-service/index.blade.phpTemplate::frontend.blocks.form-search-all-service.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fform-search-all-service%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.form-search-all-service.index"}, {"name": "1x Template::frontend.blocks.form-search-all-service.style-normal", "param_count": null, "params": [], "start": **********.888953, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/form-search-all-service/style-normal.blade.phpTemplate::frontend.blocks.form-search-all-service.style-normal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fform-search-all-service%2Fstyle-normal.blade.php&line=1", "ajax": false, "filename": "style-normal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.form-search-all-service.style-normal"}, {"name": "1x Template::frontend.blocks.form-search-all-service.form-search", "param_count": null, "params": [], "start": **********.980483, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/form-search-all-service/form-search.blade.phpTemplate::frontend.blocks.form-search-all-service.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fform-search-all-service%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.form-search-all-service.form-search"}, {"name": "1x Hotel::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": 1752043777.194349, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/form-search.blade.phpHotel::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.form-search"}, {"name": "1x Hotel::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": 1752043777.344158, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/fields/location.blade.phpHotel::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.fields.location"}, {"name": "1x Hotel::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": 1752043777.505484, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/fields/date.blade.phpHotel::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.fields.date"}, {"name": "1x Hotel::frontend.layouts.search.fields.guests", "param_count": null, "params": [], "start": 1752043777.575527, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/fields/guests.blade.phpHotel::frontend.layouts.search.fields.guests", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fguests.blade.php&line=1", "ajax": false, "filename": "guests.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.layouts.search.fields.guests"}, {"name": "1x Space::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": 1752043777.694075, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/form-search.blade.phpSpace::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.form-search"}, {"name": "1x Space::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": 1752043777.851385, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/fields/location.blade.phpSpace::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.fields.location"}, {"name": "1x Space::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": 1752043777.993886, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/fields/date.blade.phpSpace::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.fields.date"}, {"name": "1x Space::frontend.layouts.search.fields.guests", "param_count": null, "params": [], "start": 1752043778.056516, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/fields/guests.blade.phpSpace::frontend.layouts.search.fields.guests", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fguests.blade.php&line=1", "ajax": false, "filename": "guests.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.layouts.search.fields.guests"}, {"name": "1x Tour::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": 1752043778.137818, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/search/form-search.blade.phpTour::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.layouts.search.form-search"}, {"name": "1x Tour::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": 1752043778.257169, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/search/fields/location.blade.phpTour::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.layouts.search.fields.location"}, {"name": "1x Tour::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": 1752043778.402916, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/search/fields/date.blade.phpTour::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.layouts.search.fields.date"}, {"name": "1x Car::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": 1752043778.466254, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/form-search.blade.phpCar::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.search.form-search"}, {"name": "1x Car::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": 1752043778.588656, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/fields/location.blade.phpCar::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.search.fields.location"}, {"name": "1x Car::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": 1752043778.724824, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/fields/date.blade.phpCar::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.search.fields.date"}, {"name": "1x Event::frontend.layouts.search.form-search", "param_count": null, "params": [], "start": 1752043778.797808, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/form-search.blade.phpEvent::frontend.layouts.search.form-search", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Fform-search.blade.php&line=1", "ajax": false, "filename": "form-search.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.layouts.search.form-search"}, {"name": "1x Event::frontend.layouts.search.fields.location", "param_count": null, "params": [], "start": 1752043778.958274, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/fields/location.blade.phpEvent::frontend.layouts.search.fields.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.layouts.search.fields.location"}, {"name": "1x Event::frontend.layouts.search.fields.date", "param_count": null, "params": [], "start": 1752043779.10366, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/fields/date.blade.phpEvent::frontend.layouts.search.fields.date", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Ffields%2Fdate.blade.php&line=1", "ajax": false, "filename": "date.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.layouts.search.fields.date"}, {"name": "1x Template::frontend.blocks.offer-block.index", "param_count": null, "params": [], "start": 1752043779.17557, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/offer-block/index.blade.phpTemplate::frontend.blocks.offer-block.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Foffer-block%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.offer-block.index"}, {"name": "1x Hotel::frontend.blocks.list-hotel.index", "param_count": null, "params": [], "start": 1752043779.348113, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/blocks/list-hotel/index.blade.phpHotel::frontend.blocks.list-hotel.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Fblocks%2Flist-hotel%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::frontend.blocks.list-hotel.index"}, {"name": "4x Hotel::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1752043779.418144, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/loop-grid.blade.phpHotel::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FHotel%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "Hotel::frontend.layouts.search.loop-grid"}, {"name": "1x Location::frontend.blocks.list-locations.index", "param_count": null, "params": [], "start": 1752043779.884363, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/index.blade.phpLocation::frontend.blocks.list-locations.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLocation%2FViews%2Ffrontend%2Fblocks%2Flist-locations%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Location::frontend.blocks.list-locations.index"}, {"name": "6x Location::frontend.blocks.list-locations.loop", "param_count": null, "params": [], "start": 1752043779.961898, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.phpLocation::frontend.blocks.list-locations.loop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLocation%2FViews%2Ffrontend%2Fblocks%2Flist-locations%2Floop.blade.php&line=1", "ajax": false, "filename": "loop.blade.php", "line": "?"}, "render_count": 6, "name_original": "Location::frontend.blocks.list-locations.loop"}, {"name": "1x Tour::frontend.blocks.list-tour.index", "param_count": null, "params": [], "start": 1752043780.600961, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/blocks/list-tour/index.blade.phpTour::frontend.blocks.list-tour.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Fblocks%2Flist-tour%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.blocks.list-tour.index"}, {"name": "1x Tour::frontend.blocks.list-tour.style-normal", "param_count": null, "params": [], "start": 1752043780.674422, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/blocks/list-tour/style-normal.blade.phpTour::frontend.blocks.list-tour.style-normal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Fblocks%2Flist-tour%2Fstyle-normal.blade.php&line=1", "ajax": false, "filename": "style-normal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.blocks.list-tour.style-normal"}, {"name": "6x Tour::frontend.blocks.list-tour.loop-box-shadow", "param_count": null, "params": [], "start": 1752043780.770557, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/blocks/list-tour/loop-box-shadow.blade.phpTour::frontend.blocks.list-tour.loop-box-shadow", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Fblocks%2Flist-tour%2Floop-box-shadow.blade.php&line=1", "ajax": false, "filename": "loop-box-shadow.blade.php", "line": "?"}, "render_count": 6, "name_original": "Tour::frontend.blocks.list-tour.loop-box-shadow"}, {"name": "1x Space::frontend.blocks.list-space.index", "param_count": null, "params": [], "start": 1752043781.271032, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/blocks/list-space/index.blade.phpSpace::frontend.blocks.list-space.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Fblocks%2Flist-space%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Space::frontend.blocks.list-space.index"}, {"name": "4x Space::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1752043781.335455, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/loop-grid.blade.phpSpace::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FSpace%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "Space::frontend.layouts.search.loop-grid"}, {"name": "1x Car::frontend.blocks.list-car.index", "param_count": null, "params": [], "start": 1752043781.838977, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/blocks/list-car/index.blade.phpCar::frontend.blocks.list-car.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Fblocks%2Flist-car%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.blocks.list-car.index"}, {"name": "8x Car::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1752043781.918503, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/search/loop-grid.blade.phpCar::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 8, "name_original": "Car::frontend.layouts.search.loop-grid"}, {"name": "1x Event::frontend.blocks.list-event.index", "param_count": null, "params": [], "start": 1752043782.436767, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/blocks/list-event/index.blade.phpEvent::frontend.blocks.list-event.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Fblocks%2Flist-event%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Event::frontend.blocks.list-event.index"}, {"name": "4x Event::frontend.layouts.search.loop-grid", "param_count": null, "params": [], "start": 1752043782.517392, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/loop-grid.blade.phpEvent::frontend.layouts.search.loop-grid", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FEvent%2FViews%2Ffrontend%2Flayouts%2Fsearch%2Floop-grid.blade.php&line=1", "ajax": false, "filename": "loop-grid.blade.php", "line": "?"}, "render_count": 4, "name_original": "Event::frontend.layouts.search.loop-grid"}, {"name": "1x News::frontend.blocks.list-news.index", "param_count": null, "params": [], "start": 1752043783.012039, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/index.blade.phpNews::frontend.blocks.list-news.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FNews%2FViews%2Ffrontend%2Fblocks%2Flist-news%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "News::frontend.blocks.list-news.index"}, {"name": "6x News::frontend.blocks.list-news.loop", "param_count": null, "params": [], "start": 1752043783.065616, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.phpNews::frontend.blocks.list-news.loop", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FNews%2FViews%2Ffrontend%2Fblocks%2Flist-news%2Floop.blade.php&line=1", "ajax": false, "filename": "loop.blade.php", "line": "?"}, "render_count": 6, "name_original": "News::frontend.blocks.list-news.loop"}, {"name": "1x Template::frontend.blocks.call-to-action.index", "param_count": null, "params": [], "start": 1752043783.276957, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/call-to-action/index.blade.phpTemplate::frontend.blocks.call-to-action.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fcall-to-action%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.call-to-action.index"}, {"name": "1x Template::frontend.blocks.call-to-action.style-normal", "param_count": null, "params": [], "start": 1752043783.333054, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/call-to-action/style-normal.blade.phpTemplate::frontend.blocks.call-to-action.style-normal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Fcall-to-action%2Fstyle-normal.blade.php&line=1", "ajax": false, "filename": "style-normal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.call-to-action.style-normal"}, {"name": "1x Template::frontend.blocks.testimonial.index", "param_count": null, "params": [], "start": 1752043783.365291, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Template/Views/frontend/blocks/testimonial/index.blade.phpTemplate::frontend.blocks.testimonial.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FTemplate%2FViews%2Ffrontend%2Fblocks%2Ftestimonial%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "Template::frontend.blocks.testimonial.index"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": 1752043783.479886, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x Layout::app", "param_count": null, "params": [], "start": 1752043783.481363, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/app.blade.phpLayout::app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::app"}, {"name": "1x Layout::parts.favicon", "param_count": null, "params": [], "start": 1752043783.483126, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/favicon.blade.phpLayout::parts.favicon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffavicon.blade.php&line=1", "ajax": false, "filename": "favicon.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.favicon"}, {"name": "1x Layout::parts.seo-meta", "param_count": null, "params": [], "start": 1752043783.502603, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/seo-meta.blade.phpLayout::parts.seo-meta", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fseo-meta.blade.php&line=1", "ajax": false, "filename": "seo-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.seo-meta"}, {"name": "1x Layout::parts.global-script", "param_count": null, "params": [], "start": 1752043783.51669, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/global-script.blade.phpLayout::parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.global-script"}, {"name": "1x Layout::parts.topbar", "param_count": null, "params": [], "start": 1752043783.658883, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/topbar.blade.phpLayout::parts.topbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Ftopbar.blade.php&line=1", "ajax": false, "filename": "topbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.topbar"}, {"name": "2x Core::frontend.currency-switcher", "param_count": null, "params": [], "start": 1752043783.723781, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Core/Views/frontend/currency-switcher.blade.phpCore::frontend.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCore%2FViews%2Ffrontend%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Core::frontend.currency-switcher"}, {"name": "2x Language::frontend.switcher", "param_count": null, "params": [], "start": 1752043783.731592, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Language/Views/frontend/switcher.blade.phpLanguage::frontend.switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLanguage%2FViews%2Ffrontend%2Fswitcher.blade.php&line=1", "ajax": false, "filename": "switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Language::frontend.switcher"}, {"name": "1x Layout::parts.notification", "param_count": null, "params": [], "start": 1752043783.734279, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/notification.blade.phpLayout::parts.notification", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.notification"}, {"name": "1x Layout::parts.header", "param_count": null, "params": [], "start": 1752043783.800666, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/header.blade.phpLayout::parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.header"}, {"name": "1x Layout::parts.footer", "param_count": null, "params": [], "start": 1752043783.932229, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/footer.blade.phpLayout::parts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.footer"}, {"name": "1x Layout::parts.login-register-modal", "param_count": null, "params": [], "start": 1752043784.001132, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/login-register-modal.blade.phpLayout::parts.login-register-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Flogin-register-modal.blade.php&line=1", "ajax": false, "filename": "login-register-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.login-register-modal"}, {"name": "1x Layout::auth.login-form", "param_count": null, "params": [], "start": 1752043784.002718, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/login-form.blade.phpLayout::auth.login-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Flogin-form.blade.php&line=1", "ajax": false, "filename": "login-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.login-form"}, {"name": "1x Layout::auth.register-form", "param_count": null, "params": [], "start": 1752043784.067594, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/register-form.blade.phpLayout::auth.register-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Fregister-form.blade.php&line=1", "ajax": false, "filename": "register-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.register-form"}, {"name": "1x Popup::frontend.popup", "param_count": null, "params": [], "start": 1752043784.094194, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Popup/Views/frontend/popup.blade.phpPopup::frontend.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FPopup%2FViews%2Ffrontend%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "Popup::frontend.popup"}, {"name": "1x demo_script", "param_count": null, "params": [], "start": 1752043784.165362, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/demo_script.blade.phpdemo_script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fdemo_script.blade.php&line=1", "ajax": false, "filename": "demo_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "demo_script"}]}, "route": {"uri": "GET /", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeController@index", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=31\" onclick=\"\">app/Http/Controllers/HomeController.php:31-61</a>"}, "queries": {"nb_statements": 187, "nb_failed_statements": 0, "accumulated_duration": 0.21186000000000002, "accumulated_duration_str": "212ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = '7' and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 75}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 196}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 167}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 21, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}], "start": **********.481507, "duration": 0.02557, "duration_str": "25.57ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:75", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:75", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=75", "ajax": false, "filename": "EloquentUserProvider.php", "line": "75"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.555663, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'home_page_id' limit 1", "type": "query", "params": [], "bindings": ["home_page_id"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.586542, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_pages` where `id` = '1' and `status` = 'publish' and `core_pages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1", "publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeController.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.6031399, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "HomeController.php:34", "source": "app/Http/Controllers/HomeController.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeController.php&line=34", "ajax": false, "filename": "HomeController.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select * from `core_page_translations` where `core_page_translations`.`origin_id` = 1 and `core_page_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeController.php", "line": 37}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.6207469, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_seo` where `object_id` = 1 and `object_model` = 'page' limit 1", "type": "query", "params": [], "bindings": ["1", "page"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 212}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/HomeController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeController.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6291611, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:199", "source": "app/BaseModel.php:199", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=199", "ajax": false, "filename": "BaseModel.php", "line": "199"}, "connection": "mazar_travel"}, {"sql": "select * from `core_templates` where `core_templates`.`id` = 1 and `core_templates`.`id` is not null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 79}, {"index": 22, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.708869, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Page.php:79", "source": "modules/Page/Models/Page.php:79", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FPage%2FModels%2FPage.php&line=79", "ajax": false, "filename": "Page.php", "line": "79"}, "connection": "mazar_travel"}, {"sql": "select * from `core_template_translations` where `core_template_translations`.`origin_id` = 1 and `core_template_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 81}, {"index": 23, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.718748, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 16 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["16"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": **********.754507, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `name` asc limit 1000", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 129}, {"index": 18, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 21, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 22, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.7715018, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:129", "source": "modules/Template/Blocks/FormSearchAllService.php:129", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=129", "ajax": false, "filename": "FormSearchAllService.php", "line": "129"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 129}, {"index": 23, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 26, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 27, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.779796, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:129", "source": "modules/Template/Blocks/FormSearchAllService.php:129", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=129", "ajax": false, "filename": "FormSearchAllService.php", "line": "129"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_category` where `status` = 'publish' and `bravo_tour_category`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 130}, {"index": 18, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 21, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 22, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.78988, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:130", "source": "modules/Template/Blocks/FormSearchAllService.php:130", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=130", "ajax": false, "filename": "FormSearchAllService.php", "line": "130"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_category_translations` where `locale` = 'en' and `bravo_tour_category_translations`.`origin_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Template/Blocks/FormSearchAllService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\FormSearchAllService.php", "line": 130}, {"index": 23, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 26, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 27, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.800244, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "FormSearchAllService.php:130", "source": "modules/Template/Blocks/FormSearchAllService.php:130", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FBlocks%2FFormSearchAllService.php&line=130", "ajax": false, "filename": "FormSearchAllService.php", "line": "130"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 47 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["47"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": **********.971812, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'hotel_search_fields' limit 1", "type": "query", "params": [], "bindings": ["hotel_search_fields"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043777.336707, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'hotel_location_search_style' limit 1", "type": "query", "params": [], "bindings": ["hotel_location_search_style"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043777.482986, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'space_search_fields' limit 1", "type": "query", "params": [], "bindings": ["space_search_fields"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043777.842962, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'space_location_search_style' limit 1", "type": "query", "params": [], "bindings": ["space_location_search_style"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043777.9767392, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'tour_search_fields' limit 1", "type": "query", "params": [], "bindings": ["tour_search_fields"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043778.24911, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'tour_location_search_style' limit 1", "type": "query", "params": [], "bindings": ["tour_location_search_style"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043778.38292, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_search_fields' limit 1", "type": "query", "params": [], "bindings": ["car_search_fields"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043778.581561, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_location_search_style' limit 1", "type": "query", "params": [], "bindings": ["car_location_search_style"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043778.707375, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'event_search_fields' limit 1", "type": "query", "params": [], "bindings": ["event_search_fields"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043778.948872, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'event_location_search_style' limit 1", "type": "query", "params": [], "bindings": ["event_location_search_style"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.084939, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 17 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["17"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043779.251741, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 18 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["18"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043779.264914, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 19 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["19"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043779.277436, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_hotels`.* from `bravo_hotels` where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null group by `bravo_hotels`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 17, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.287031, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select `bravo_hotels`.* from `bravo_hotels` where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null group by `bravo_hotels`.`id` order by `bravo_hotels`.`id` asc limit 4 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 17, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.293425, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 22, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.300113, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 27, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.304017, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'hotel' and `user_id` = 7 and `user_wishlist`.`object_id` in (1, 2, 3, 4)", "type": "query", "params": [], "bindings": ["hotel", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 22, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.307839, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_hotel_translations` where `locale` = 'en' and `bravo_hotel_translations`.`origin_id` in (1, 2, 3, 4) and `bravo_hotel_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 22, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.314265, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'hotel_attribute_show_in_listing_page' limit 1", "type": "query", "params": [], "bindings": ["hotel_attribute_show_in_listing_page"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.322195, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select `bravo_terms`.*, `bravo_hotel_term`.`target_id` as `laravel_through_key` from `bravo_terms` inner join `bravo_hotel_term` on `bravo_hotel_term`.`term_id` = `bravo_terms`.`id` where `bravo_terms`.`attr_id` = '6' and `bravo_hotel_term`.`target_id` in (1, 2, 3, 4) and `bravo_terms`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["6"], "hints": [], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 21, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.331806, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_terms_translations` where `locale` = 'en' and `bravo_terms_translations`.`origin_id` in (42, 43, 44, 45, 46, 47, 48)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 161}, {"index": 26, "namespace": null, "name": "modules/Hotel/Blocks/ListHotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Blocks\\ListHotel.php", "line": 140}, {"index": 29, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 32, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 33, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.341897, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "ListHotel.php:161", "source": "modules/Hotel/Blocks/ListHotel.php:161", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FBlocks%2FListHotel.php&line=161", "ajax": false, "filename": "ListHotel.php", "line": "161"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 67 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["67"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043779.565696, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'hotel_enable_review' limit 1", "type": "query", "params": [], "bindings": ["hotel_enable_review"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.5856721, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 1 and `object_model` = 'hotel' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1", "hotel", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 704}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 703}, {"index": 21, "namespace": "view", "name": "Hotel::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/loop-grid.blade.php", "line": 58}], "start": 1752043779.597398, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "Hotel.php:704", "source": "modules/Hotel/Models/Hotel.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=704", "ajax": false, "filename": "Hotel.php", "line": "704"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_main' limit 1", "type": "query", "params": [], "bindings": ["currency_main"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.6113942, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_format' limit 1", "type": "query", "params": [], "bindings": ["currency_format"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.630033, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_thousand' limit 1", "type": "query", "params": [], "bindings": ["currency_thousand"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.6398568, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_no_decimal' limit 1", "type": "query", "params": [], "bindings": ["currency_no_decimal"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.64999, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'currency_decimal' limit 1", "type": "query", "params": [], "bindings": ["currency_decimal"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.659783, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'extra_currency' limit 1", "type": "query", "params": [], "bindings": ["extra_currency"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043779.671248, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 68 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["68"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043779.706275, "duration": 0.0017800000000000001, "duration_str": "1.78ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 2 and `object_model` = 'hotel' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2", "hotel", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 704}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 703}, {"index": 21, "namespace": "view", "name": "Hotel::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/loop-grid.blade.php", "line": 58}], "start": 1752043779.7327979, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:704", "source": "modules/Hotel/Models/Hotel.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=704", "ajax": false, "filename": "Hotel.php", "line": "704"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 69 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["69"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043779.7680538, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 3 and `object_model` = 'hotel' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3", "hotel", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 704}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 703}, {"index": 21, "namespace": "view", "name": "Hotel::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/loop-grid.blade.php", "line": 58}], "start": 1752043779.789933, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:704", "source": "modules/Hotel/Models/Hotel.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=704", "ajax": false, "filename": "Hotel.php", "line": "704"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 70 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["70"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043779.8229768, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 4 and `object_model` = 'hotel' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4", "hotel", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 704}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 703}, {"index": 21, "namespace": "view", "name": "Hotel::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Hotel/Views/frontend/layouts/search/loop-grid.blade.php", "line": 58}], "start": 1752043779.8472528, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:704", "source": "modules/Hotel/Models/Hotel.php:704", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=704", "ajax": false, "filename": "Hotel.php", "line": "704"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null order by `id` asc limit 6", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 168}, {"index": 16, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 22, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 23, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.87467, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ListLocations.php:168", "source": "modules/Location/Blocks/ListLocations.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FBlocks%2FListLocations.php&line=168", "ajax": false, "filename": "ListLocations.php", "line": "168"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 168}, {"index": 21, "namespace": null, "name": "modules/Location/Blocks/ListLocations.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Blocks\\ListLocations.php", "line": 132}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043779.88016, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ListLocations.php:168", "source": "modules/Location/Blocks/ListLocations.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FBlocks%2FListLocations.php&line=168", "ajax": false, "filename": "ListLocations.php", "line": "168"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 111 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["111"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.0793998, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.097559, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.120073, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 1 and `bravo_locations`.`_rgt` <= 2 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "2", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.142606, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 107 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["107"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.164836, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.180266, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.202772, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 3 and `bravo_locations`.`_rgt` <= 4 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["3", "4", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.224096, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 108 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["108"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.242581, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.277697, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.303115, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 5 and `bravo_locations`.`_rgt` <= 6 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["5", "6", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.326517, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 109 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["109"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.3471432, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.365366, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.386818, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 7 and `bravo_locations`.`_rgt` <= 8 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7", "8", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.400955, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 110 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["110"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.424712, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.4534178, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.4668438, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 9 and `bravo_locations`.`_rgt` <= 10 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.488452, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 106 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["106"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.5108778, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_spaces`.`id`) as aggregate from `bravo_spaces` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_spaces`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 720}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.528027, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "Space.php:720", "source": "modules/Space/Models/Space.php:720", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=720", "ajax": false, "filename": "Space.php", "line": "720"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_hotels`.`id`) as aggregate from `bravo_hotels` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_hotels`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_hotels`.`status` = 'publish' and `bravo_hotels`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Hotel/Models/Hotel.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Models\\Hotel.php", "line": 730}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.541489, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Hotel.php:730", "source": "modules/Hotel/Models/Hotel.php:730", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=730", "ajax": false, "filename": "Hotel.php", "line": "730"}, "connection": "mazar_travel"}, {"sql": "select count(`bravo_tours`.`id`) as aggregate from `bravo_tours` inner join `bravo_locations` on `bravo_locations`.`id` = `bravo_tours`.`location_id` and `bravo_locations`.`_lft` >= 11 and `bravo_locations`.`_rgt` <= 12 where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "12", "publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 813}, {"index": 17, "namespace": null, "name": "modules/Location/Models/Location.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Models\\Location.php", "line": 78}, {"index": 18, "namespace": "view", "name": "Location::frontend.blocks.list-locations.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Location/Views/frontend/blocks/list-locations/loop.blade.php", "line": 29}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752043780.554329, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Tour.php:813", "source": "modules/Tour/Models/Tour.php:813", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=813", "ajax": false, "filename": "Tour.php", "line": "813"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_category` where (0 = 1 or 0 = 1 and `status` = 'publish') and `bravo_tour_category`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Tour/Models/Tour.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Models\\Tour.php", "line": 1003}, {"index": 16, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 176}, {"index": 17, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}], "start": 1752043780.569411, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Tour.php:1003", "source": "modules/Tour/Models/Tour.php:1003", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=1003", "ajax": false, "filename": "Tour.php", "line": "1003"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_tours`.* from `bravo_tours` where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null group by `bravo_tours`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 17, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043780.573825, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select `bravo_tours`.* from `bravo_tours` where `bravo_tours`.`status` = 'publish' and `bravo_tours`.`deleted_at` is null group by `bravo_tours`.`id` order by `bravo_tours`.`id` asc limit 6 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 17, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043780.578554, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1, 2) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 22, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043780.583048, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 2)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 27, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043780.586728, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'tour' and `user_id` = 7 and `user_wishlist`.`object_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["tour", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 22, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043780.589957, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_tour_translations` where `locale` = 'en' and `bravo_tour_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 178}, {"index": 22, "namespace": null, "name": "modules/Tour/Blocks/ListTours.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Tour\\Blocks\\ListTours.php", "line": 157}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043780.59531, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "ListTours.php:178", "source": "modules/Tour/Blocks/ListTours.php:178", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FBlocks%2FListTours.php&line=178", "ajax": false, "filename": "ListTours.php", "line": "178"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 21 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["21"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.870585, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 22 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["22"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.936322, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 23 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["23"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043780.997434, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 24 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["24"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043781.0565488, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 25 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["25"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043781.1177502, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 26 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["26"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043781.1859598, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_spaces`.* from `bravo_spaces` where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null group by `bravo_spaces`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 17, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.240958, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select `bravo_spaces`.* from `bravo_spaces` where `bravo_spaces`.`status` = 'publish' and `bravo_spaces`.`deleted_at` is null group by `bravo_spaces`.`id` order by `bravo_spaces`.`id` desc limit 4 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 17, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.2460039, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 22, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.250308, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 27, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.253487, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'space' and `user_id` = 7 and `user_wishlist`.`object_id` in (8, 9, 10, 11)", "type": "query", "params": [], "bindings": ["space", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 22, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.256539, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_space_translations` where `locale` = 'en' and `bravo_space_translations`.`origin_id` in (8, 9, 10, 11) and `bravo_space_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 156}, {"index": 22, "namespace": null, "name": "modules/Space/Blocks/ListSpace.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Blocks\\ListSpace.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.263502, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "ListSpace.php:156", "source": "modules/Space/Blocks/ListSpace.php:156", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FBlocks%2FListSpace.php&line=156", "ajax": false, "filename": "ListSpace.php", "line": "156"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 73 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["73"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043781.501164, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'space_booking_type' limit 1", "type": "query", "params": [], "bindings": ["space_booking_type"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043781.5669951, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'space_enable_review' limit 1", "type": "query", "params": [], "bindings": ["space_enable_review"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043781.5812578, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 11 and `object_model` = 'space' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11", "space", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 694}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 693}, {"index": 21, "namespace": "view", "name": "Space::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/loop-grid.blade.php", "line": 61}], "start": 1752043781.591859, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "Space.php:694", "source": "modules/Space/Models/Space.php:694", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=694", "ajax": false, "filename": "Space.php", "line": "694"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'size_unit' limit 1", "type": "query", "params": [], "bindings": ["size_unit"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043781.606065, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 72 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["72"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043781.6203032, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 10 and `object_model` = 'space' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["10", "space", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 694}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 693}, {"index": 21, "namespace": "view", "name": "Space::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/loop-grid.blade.php", "line": 61}], "start": 1752043781.6665778, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Space.php:694", "source": "modules/Space/Models/Space.php:694", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=694", "ajax": false, "filename": "Space.php", "line": "694"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 71 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["71"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043781.683343, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 9 and `object_model` = 'space' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9", "space", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 694}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 693}, {"index": 21, "namespace": "view", "name": "Space::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/loop-grid.blade.php", "line": 61}], "start": 1752043781.7480938, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Space.php:694", "source": "modules/Space/Models/Space.php:694", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=694", "ajax": false, "filename": "Space.php", "line": "694"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 8 and `object_model` = 'space' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["8", "space", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 694}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Space/Models/Space.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Space\\Models\\Space.php", "line": 693}, {"index": 21, "namespace": "view", "name": "Space::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Space/Views/frontend/layouts/search/loop-grid.blade.php", "line": 61}], "start": 1752043781.793577, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "Space.php:694", "source": "modules/Space/Models/Space.php:694", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=694", "ajax": false, "filename": "Space.php", "line": "694"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_cars`.* from `bravo_cars` where `bravo_cars`.`status` = 'publish' and `bravo_cars`.`deleted_at` is null group by `bravo_cars`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 17, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.80384, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select `bravo_cars`.* from `bravo_cars` where `bravo_cars`.`status` = 'publish' and `bravo_cars`.`deleted_at` is null group by `bravo_cars`.`id` order by `bravo_cars`.`id` desc limit 8 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 17, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.809964, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (1, 3, 6, 7, 8) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 22, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.81579, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (1, 3, 6, 7, 8)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 27, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.820025, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'car' and `user_id` = 7 and `user_wishlist`.`object_id` in (6, 7, 8, 9, 10, 11, 12, 13)", "type": "query", "params": [], "bindings": ["car", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 22, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.823586, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_car_translations` where `locale` = 'en' and `bravo_car_translations`.`origin_id` in (6, 7, 8, 9, 10, 11, 12, 13) and `bravo_car_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 153}, {"index": 22, "namespace": null, "name": "modules/Car/Blocks/ListCar.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Blocks\\ListCar.php", "line": 132}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043781.830862, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "ListCar.php:153", "source": "modules/Car/Blocks/ListCar.php:153", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FBlocks%2FListCar.php&line=153", "ajax": false, "filename": "ListCar.php", "line": "153"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 154 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["154"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.02183, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 153 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["153"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.071475, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 152 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["152"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.121517, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 151 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["151"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.167956, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 150 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["150"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.2155979, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 149 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["149"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.264195, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 148 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["148"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.314102, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 147 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["147"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.363172, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from (select `bravo_events`.* from `bravo_events` where `bravo_events`.`status` = 'publish' and `bravo_events`.`deleted_at` is null group by `bravo_events`.`id`) as `aggregate_table`", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 17, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043782.405164, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select `bravo_events`.* from `bravo_events` where `bravo_events`.`status` = 'publish' and `bravo_events`.`deleted_at` is null group by `bravo_events`.`id` order by `bravo_events`.`is_featured` desc, `bravo_events`.`id` desc limit 4 offset 0", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 17, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 20, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 23, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 24, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043782.410902, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (2, 4, 6, 7) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 22, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043782.416295, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (2, 4, 6, 7)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 27, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 30, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 33, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 34, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043782.420095, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'event' and `user_id` = 7 and `user_wishlist`.`object_id` in (6, 9, 11, 12)", "type": "query", "params": [], "bindings": ["event", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 22, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043782.4235282, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_event_translations` where `locale` = 'en' and `bravo_event_translations`.`origin_id` in (6, 9, 11, 12) and `bravo_event_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 154}, {"index": 22, "namespace": null, "name": "modules/Event/Blocks/ListEvent.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Blocks\\ListEvent.php", "line": 133}, {"index": 25, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 28, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 29, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043782.4309, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "ListEvent.php:154", "source": "modules/Event/Blocks/ListEvent.php:154", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FBlocks%2FListEvent.php&line=154", "ajax": false, "filename": "ListEvent.php", "line": "154"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 174 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["174"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.7070749, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 12 and `object_model` = 'event' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["12", "event", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 724}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 723}, {"index": 21, "namespace": "view", "name": "Event::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/loop-grid.blade.php", "line": 47}], "start": 1752043782.731159, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Event.php:724", "source": "modules/Event/Models/Event.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FModels%2FEvent.php&line=724", "ajax": false, "filename": "Event.php", "line": "724"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 173 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["173"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.788259, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 11 and `object_model` = 'event' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11", "event", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 724}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 723}, {"index": 21, "namespace": "view", "name": "Event::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/loop-grid.blade.php", "line": 47}], "start": 1752043782.813023, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Event.php:724", "source": "modules/Event/Models/Event.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FModels%2FEvent.php&line=724", "ajax": false, "filename": "Event.php", "line": "724"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 171 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["171"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.850151, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 9 and `object_model` = 'event' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["9", "event", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 724}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 723}, {"index": 21, "namespace": "view", "name": "Event::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/loop-grid.blade.php", "line": 47}], "start": 1752043782.8756862, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Event.php:724", "source": "modules/Event/Models/Event.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FModels%2FEvent.php&line=724", "ajax": false, "filename": "Event.php", "line": "724"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 168 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["168"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043782.93588, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 6 and `object_model` = 'event' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["6", "event", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 724}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Event/Models/Event.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Event\\Models\\Event.php", "line": 723}, {"index": 21, "namespace": "view", "name": "Event::frontend.layouts.search.loop-grid", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Event/Views/frontend/layouts/search/loop-grid.blade.php", "line": 47}], "start": 1752043782.9634671, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "Event.php:724", "source": "modules/Event/Models/Event.php:724", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FModels%2FEvent.php&line=724", "ajax": false, "filename": "Event.php", "line": "724"}, "connection": "mazar_travel"}, {"sql": "select `core_news`.* from `core_news` where `core_news`.`status` = 'publish' and `core_news`.`deleted_at` is null group by `core_news`.`id` order by `core_news`.`id` asc limit 6", "type": "query", "params": [], "bindings": ["publish"], "hints": [], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 134}, {"index": 16, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 89}, {"index": 19, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 22, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 23, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043782.9943721, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "ListNews.php:134", "source": "modules/News/Blocks/ListNews.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FBlocks%2FListNews.php&line=134", "ajax": false, "filename": "ListNews.php", "line": "134"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_translations` where `locale` = 'en' and `core_news_translations`.`origin_id` in (1, 2, 3, 4, 5, 6)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 134}, {"index": 21, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 89}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043783.000809, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "ListNews.php:134", "source": "modules/News/Blocks/ListNews.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FBlocks%2FListNews.php&line=134", "ajax": false, "filename": "ListNews.php", "line": "134"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category` where `core_news_category`.`id` in (1, 2, 3) and `core_news_category`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 134}, {"index": 21, "namespace": null, "name": "modules/News/Blocks/ListNews.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\Blocks\\ListNews.php", "line": 89}, {"index": 24, "namespace": null, "name": "modules/Template/Blocks/RootBlock.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Template\\Blocks\\RootBlock.php", "line": 10}, {"index": 27, "namespace": null, "name": "modules/Page/Models/Page.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Page\\Models\\Page.php", "line": 82}, {"index": 28, "namespace": "view", "name": "Page::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/Base/Page/Views/frontend/detail.blade.php", "line": 5}], "start": 1752043783.006896, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "ListNews.php:134", "source": "modules/News/Blocks/ListNews.php:134", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FBlocks%2FListNews.php&line=134", "ajax": false, "filename": "ListNews.php", "line": "134"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 115 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["115"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.154484, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category_translations` where `core_news_category_translations`.`origin_id` = 3 and `core_news_category_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["3", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "News::frontend.blocks.list-news.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752043783.16661, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 116 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["116"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.1830869, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category_translations` where `core_news_category_translations`.`origin_id` = 2 and `core_news_category_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["2", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "News::frontend.blocks.list-news.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752043783.190756, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 117 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["117"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.204539, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 118 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["118"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.221661, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 119 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["119"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.239325, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_news_category_translations` where `core_news_category_translations`.`origin_id` = 1 and `core_news_category_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "News::frontend.blocks.list-news.loop", "file": "C:\\wamp64\\www\\mazar\\themes/BC/News/Views/frontend/blocks/list-news/loop.blade.php", "line": 22}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752043783.248573, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 120 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["120"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.263015, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 1 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.445599, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 2 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.4553049, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 3 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.469328, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'cookie_agreement_type' limit 1", "type": "query", "params": [], "bindings": ["cookie_agreement_type"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.5088968, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_lat_default' limit 1", "type": "query", "params": [], "bindings": ["map_lat_default"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.555886, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_lng_default' limit 1", "type": "query", "params": [], "bindings": ["map_lng_default"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.56651, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_clustering' limit 1", "type": "query", "params": [], "bindings": ["map_clustering"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.5767071, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_fit_bounds' limit 1", "type": "query", "params": [], "bindings": ["map_fit_bounds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.5863612, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'enable_rtl' limit 1", "type": "query", "params": [], "bindings": ["enable_rtl"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.6020298, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'head_scripts' limit 1", "type": "query", "params": [], "bindings": ["head_scripts"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.637318, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'body_scripts' limit 1", "type": "query", "params": [], "bindings": ["body_scripts"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.650458, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'topbar_left_text' limit 1", "type": "query", "params": [], "bindings": ["topbar_left_text"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.66497, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1752043783.736178, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1752043783.741, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'wallet_module_disable' limit 1", "type": "query", "params": [], "bindings": ["wallet_module_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.749717, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'dashboard_vendor_access')", "type": "query", "params": [], "bindings": ["1", "dashboard_vendor_access"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": 1752043783.7619, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'inbox_enable' limit 1", "type": "query", "params": [], "bindings": ["inbox_enable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.778877, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'user_plans_enable' limit 1", "type": "query", "params": [], "bindings": ["user_plans_enable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.789282, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'enable_header_sticky' limit 1", "type": "query", "params": [], "bindings": ["enable_header_sticky"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.804339, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'logo_id' limit 1", "type": "query", "params": [], "bindings": ["logo_id"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.817045, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '229' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["229"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752043783.8298109, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'menu_locations' limit 1", "type": "query", "params": [], "bindings": ["menu_locations"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.8457148, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menus` where `core_menus`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 110}], "start": 1752043783.859699, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752043783.868295, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752043783.907707, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'list_widget_footer' limit 1", "type": "query", "params": [], "bindings": ["list_widget_footer"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.938148, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'footer_text_left' limit 1", "type": "query", "params": [], "bindings": ["footer_text_left"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.964092, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'footer_text_right' limit 1", "type": "query", "params": [], "bindings": ["footer_text_right"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043783.984385, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'user_enable_login_recaptcha' limit 1", "type": "query", "params": [], "bindings": ["user_enable_login_recaptcha"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.009134, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'facebook_enable' limit 1", "type": "query", "params": [], "bindings": ["facebook_enable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.023241, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'google_enable' limit 1", "type": "query", "params": [], "bindings": ["google_enable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.038439, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'twitter_enable' limit 1", "type": "query", "params": [], "bindings": ["twitter_enable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.048931, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'user_disable_register' limit 1", "type": "query", "params": [], "bindings": ["user_disable_register"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.060244, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'booking_term_conditions' limit 1", "type": "query", "params": [], "bindings": ["booking_term_conditions"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.072917, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'user_enable_register_recaptcha' limit 1", "type": "query", "params": [], "bindings": ["user_enable_register_recaptcha"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.084379, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_popups` where `status` = 'publish' order by `id` desc", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Popup/Models/Popup.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Popup\\Models\\Popup.php", "line": 56}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 19, "namespace": null, "name": "modules/Popup/Models/Popup.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Popup\\Models\\Popup.php", "line": 55}, {"index": 20, "namespace": null, "name": "modules/Popup/Models/Popup.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Popup\\Models\\Popup.php", "line": 68}], "start": 1752043784.0992172, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "Popup.php:56", "source": "modules/Popup/Models/Popup.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FPopup%2FModels%2FPopup.php&line=56", "ajax": false, "filename": "Popup.php", "line": "56"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'recaptcha_api_key' limit 1", "type": "query", "params": [], "bindings": ["recaptcha_api_key"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.1153889, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'recaptcha_api_secret' limit 1", "type": "query", "params": [], "bindings": ["recaptcha_api_secret"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.1260638, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'recaptcha_enable' limit 1", "type": "query", "params": [], "bindings": ["recaptcha_enable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.1357021, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'recaptcha_version' limit 1", "type": "query", "params": [], "bindings": ["recaptcha_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.1459348, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'footer_scripts' limit 1", "type": "query", "params": [], "bindings": ["footer_scripts"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752043784.156493, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Media\\Models\\MediaFile": {"value": 46, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FModels%2FMediaFile.php&line=1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Modules\\Location\\Models\\Location": {"value": 29, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "Modules\\Core\\Models\\Settings": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Modules\\Core\\Models\\Terms": {"value": 27, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FTerms.php&line=1", "ajax": false, "filename": "Terms.php", "line": "?"}}, "Modules\\Review\\Models\\Review": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FReview%2FModels%2FReview.php&line=1", "ajax": false, "filename": "Review.php", "line": "?"}}, "Modules\\Car\\Models\\Car": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=1", "ajax": false, "filename": "Car.php", "line": "?"}}, "Modules\\Tour\\Models\\Tour": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTour.php&line=1", "ajax": false, "filename": "Tour.php", "line": "?"}}, "Modules\\News\\Models\\News": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModels%2FNews.php&line=1", "ajax": false, "filename": "News.php", "line": "?"}}, "Modules\\Tour\\Models\\TourCategory": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTour%2FModels%2FTourCategory.php&line=1", "ajax": false, "filename": "TourCategory.php", "line": "?"}}, "Modules\\Hotel\\Models\\Hotel": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=1", "ajax": false, "filename": "Hotel.php", "line": "?"}}, "Modules\\Space\\Models\\Space": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FSpace%2FModels%2FSpace.php&line=1", "ajax": false, "filename": "Space.php", "line": "?"}}, "Modules\\Event\\Models\\Event": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FEvent%2FModels%2FEvent.php&line=1", "ajax": false, "filename": "Event.php", "line": "?"}}, "Modules\\News\\Models\\NewsCategory": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModels%2FNewsCategory.php&line=1", "ajax": false, "filename": "NewsCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Modules\\Page\\Models\\Page": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FPage%2FModels%2FPage.php&line=1", "ajax": false, "filename": "Page.php", "line": "?"}}, "Modules\\Template\\Models\\Template": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FModels%2FTemplate.php&line=1", "ajax": false, "filename": "Template.php", "line": "?"}}, "Modules\\Template\\Models\\TemplateTranslation": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FTemplate%2FModels%2FTemplateTranslation.php&line=1", "ajax": false, "filename": "TemplateTranslation.php", "line": "?"}}, "Modules\\Core\\Models\\Menu": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}}, "count": 186, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "GrHQppIptkSvatnxv2wjCUWbne7yexhS52j7Iggb", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1822652613 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1822652613\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1154421691 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1154421691\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-160018542 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-160018542\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2111533712 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">http://127.0.0.1:8000/admin/module/hotel</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1410 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111533712\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1091643645 data-indent-pad=\"  \"><span class=sf-dump-note>array:10</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1091643645\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1345503366 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 06:49:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImFGYllMcXphcEFMMGFOL1RhRUZaOHc9PSIsInZhbHVlIjoiNUNBZDBzcDRsQ09BY00yMVo4UE9YMHJhZlJ1YVRMc3pHWUJNbGUwcXZCbDFSZ2hYUktNVVB1M2Rsa1ZFVjlYTnFTbisvbW9EWW5uRGRiMDQyWm5CeU1wcEpkYkFWV1BnYm90RVNPb2drQlhJY3lRQlBvbHRxanExaW55M2pOaHEiLCJtYWMiOiJmMGFmMjk4ODI5MzIzM2Y2M2ZmNzE0NzkwZDAzNmY0MGVjY2UyNTQyZGIyYjg3NmRiNTUwMDJjYjFiMGVmZTRmIiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 08:49:44 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6Inh1NUU0cVpOT2xsTTlkZFRoOTlIREE9PSIsInZhbHVlIjoiYkxtZjB5MGRDaTZRK0MyUjlIUHF1MDhnL2huR1hhcE5YdVpGRzU3bFBQUVZ0cjZyTDFlVVJpZm9DOWF2Q3ZaWmxOR2R5R1QvR3JGQVpnY2hMMzVRbWY1aVVDLzZVVmhsalQzTzFLOEo1cHFXSDJMS0VRRXovdWRNZld5djlzSlkiLCJtYWMiOiIzNTY5ZDdjODkyZWEwYzYyMjc1NWNhZjYwMmE0MTViMTI2YTY5NzVmNDU3Y2RiZGQ4ZTI4OTg2MDEyZTcxOTE1IiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 08:49:44 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImFGYllMcXphcEFMMGFOL1RhRUZaOHc9PSIsInZhbHVlIjoiNUNBZDBzcDRsQ09BY00yMVo4UE9YMHJhZlJ1YVRMc3pHWUJNbGUwcXZCbDFSZ2hYUktNVVB1M2Rsa1ZFVjlYTnFTbisvbW9EWW5uRGRiMDQyWm5CeU1wcEpkYkFWV1BnYm90RVNPb2drQlhJY3lRQlBvbHRxanExaW55M2pOaHEiLCJtYWMiOiJmMGFmMjk4ODI5MzIzM2Y2M2ZmNzE0NzkwZDAzNmY0MGVjY2UyNTQyZGIyYjg3NmRiNTUwMDJjYjFiMGVmZTRmIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 08:49:44 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6Inh1NUU0cVpOT2xsTTlkZFRoOTlIREE9PSIsInZhbHVlIjoiYkxtZjB5MGRDaTZRK0MyUjlIUHF1MDhnL2huR1hhcE5YdVpGRzU3bFBQUVZ0cjZyTDFlVVJpZm9DOWF2Q3ZaWmxOR2R5R1QvR3JGQVpnY2hMMzVRbWY1aVVDLzZVVmhsalQzTzFLOEo1cHFXSDJMS0VRRXovdWRNZld5djlzSlkiLCJtYWMiOiIzNTY5ZDdjODkyZWEwYzYyMjc1NWNhZjYwMmE0MTViMTI2YTY5NzVmNDU3Y2RiZGQ4ZTI4OTg2MDEyZTcxOTE1IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 08:49:44 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1345503366\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1504266931 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">GrHQppIptkSvatnxv2wjCUWbne7yexhS52j7Iggb</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504266931\", {\"maxDepth\":0})</script>\n"}}