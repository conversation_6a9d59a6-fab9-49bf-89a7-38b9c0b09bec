<?php

namespace Modules\RateHawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RateHawkApiLog extends Model
{
    use HasFactory;

    protected $table = 'ratehawk_api_logs';

    protected $fillable = [
        'request_id',
        'method',
        'endpoint',
        'request_data',
        'response_data',
        'status_code',
        'duration',
        'status',
        'error_type',
        'error_message',
        'user_id',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'duration' => 'float',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that made the request
     */
    public function user()
    {
        return $this->belongsTo(\App\User::class);
    }

    /**
     * Scope for successful requests
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed requests
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'error');
    }

    /**
     * Scope for pending requests
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for specific endpoint
     */
    public function scopeEndpoint($query, string $endpoint)
    {
        return $query->where('endpoint', 'like', '%' . $endpoint . '%');
    }

    /**
     * Scope for specific method
     */
    public function scopeMethod($query, string $method)
    {
        return $query->where('method', $method);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get formatted duration
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration) {
            return 'N/A';
        }

        if ($this->duration < 1) {
            return round($this->duration * 1000) . 'ms';
        }

        return round($this->duration, 2) . 's';
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        switch ($this->status) {
            case 'completed':
                return 'badge-success';
            case 'error':
                return 'badge-danger';
            case 'pending':
                return 'badge-warning';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Get method badge class
     */
    public function getMethodBadgeClassAttribute(): string
    {
        switch ($this->method) {
            case 'GET':
                return 'badge-info';
            case 'POST':
                return 'badge-success';
            case 'PUT':
                return 'badge-warning';
            case 'DELETE':
                return 'badge-danger';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Check if request was successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === 'completed' && $this->status_code >= 200 && $this->status_code < 300;
    }

    /**
     * Check if request failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'error' || $this->status_code >= 400;
    }

    /**
     * Get error summary
     */
    public function getErrorSummary(): ?string
    {
        if (!$this->isFailed()) {
            return null;
        }

        if ($this->error_message) {
            return $this->error_message;
        }

        if ($this->response_data && isset($this->response_data['error'])) {
            return $this->response_data['error'];
        }

        return 'Unknown error';
    }
}
