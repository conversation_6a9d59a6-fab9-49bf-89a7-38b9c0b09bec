<?php
namespace Modules\RateHawk;

use <PERSON><PERSON><PERSON>\ModuleServiceProvider;
use <PERSON><PERSON><PERSON>\User\Helpers\PermissionHelper;

class ModuleProvider extends ModuleServiceProvider
{
    public static $name = 'RateHawk API';
    public static $desc = 'RateHawk B2B API integration for hotel booking management';
    public static $version = '1.0';
    public static $author = 'Mazar Team';
    public static $isPro = false;

    public function boot()
    {
        $this->mergeConfigFrom(__DIR__ . '/Configs/ratehawk.php', 'ratehawk');
        $this->loadMigrationsFrom(__DIR__ . '/Migrations');

        PermissionHelper::add([
            // RateHawk API permissions
            'ratehawk_view',
            'ratehawk_manage',
            'ratehawk_settings',
            'ratehawk_bookings_view',
            'ratehawk_bookings_create',
            'ratehawk_bookings_cancel',
        ]);
    }

    /**
     * Register bindings in the container.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouterServiceProvider::class);
    }

    public static function getAdminMenu()
    {
        return [
            'ratehawk' => [
                "position" => 60,
                'url' => route('ratehawk.admin.index'),
                'title' => __("RateHawk API"),
                'icon' => 'icon ion-ios-business',
                'permission' => 'ratehawk_view',
                'group' => 'booking'
            ],
        ];
    }

    public static function getAdminSubmenu()
    {
        return [
            'ratehawk' => [
                [
                    'url' => route('ratehawk.admin.index'),
                    'title' => __('Dashboard'),
                    'permission' => 'ratehawk_view',
                ],
                [
                    'url' => route('ratehawk.admin.bookings.index'),
                    'title' => __('Bookings'),
                    'permission' => 'ratehawk_bookings_view',
                ],
                [
                    'url' => route('ratehawk.admin.settings'),
                    'title' => __('Settings'),
                    'permission' => 'ratehawk_settings',
                ],
            ]
        ];
    }
}
