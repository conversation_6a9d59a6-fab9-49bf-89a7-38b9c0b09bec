<?php
namespace Modules\RateHawk;

use <PERSON><PERSON><PERSON>\ModuleServiceProvider;
use <PERSON><PERSON><PERSON>\User\Helpers\PermissionHelper;

class ModuleProvider extends ModuleServiceProvider
{
    public static $name = 'RateHawk API';
    public static $desc = 'RateHawk B2B API integration for hotel booking management';
    public static $version = '1.0';
    public static $author = 'Mazar Team';
    public static $isPro = false;

    public function boot()
    {
        $this->mergeConfigFrom(__DIR__ . '/Configs/ratehawk.php', 'ratehawk');
        $this->loadMigrationsFrom(__DIR__ . '/Migrations');

        PermissionHelper::add([
            // RateHawk API permissions
            'ratehawk_view',
            'ratehawk_manage',
            'ratehawk_settings',
            'ratehawk_bookings_view',
            'ratehawk_bookings_create',
            'ratehawk_bookings_cancel',
        ]);
    }

    /**
     * Register bindings in the container.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouterServiceProvider::class);
    }

    public static function getAdminMenu()
    {
        // Check if module is enabled
        if (setting_item('ratehawk_enable') != '1') {
            return [];
        }

        return [
            'ratehawk' => [
                'position' => 35,
                'url' => route('ratehawk.admin.index'),
                'title' => __('RateHawk'),
                'icon' => 'fa fa-hotel',
                'permission' => 'ratehawk_view',
                'children' => [
                    'dashboard' => [
                        'url' => route('ratehawk.admin.index'),
                        'title' => __('Dashboard'),
                        'permission' => 'ratehawk_view',
                    ],
                    'bookings' => [
                        'url' => route('ratehawk.admin.bookings.index'),
                        'title' => __('Bookings'),
                        'permission' => 'ratehawk_view',
                    ],
                    'logs' => [
                        'url' => route('ratehawk.admin.logs.index'),
                        'title' => __('API Logs'),
                        'permission' => 'ratehawk_view',
                    ],
                    'settings' => [
                        'url' => route('core.admin.settings.index', ['group' => 'ratehawk']),
                        'title' => __('Settings'),
                        'permission' => 'ratehawk_settings',
                    ],
                ]
            ]
        ];
    }


}
