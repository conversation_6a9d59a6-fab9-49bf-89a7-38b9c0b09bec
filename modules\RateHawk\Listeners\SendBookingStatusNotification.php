<?php

namespace Modules\RateHawk\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Modules\RateHawk\Events\BookingStatusUpdated;
use Modules\Email\Emails\BookingStatusEmail;

class SendBookingStatusNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(BookingStatusUpdated $event)
    {
        try {
            $booking = $event->booking;
            $newStatus = $event->newStatus;
            $bookingData = $event->bookingData;

            Log::info('Processing booking status notification', [
                'order_id' => $booking->order_id,
                'status' => $newStatus
            ]);

            // Send email notification if guest email is available
            $guestEmail = $this->getGuestEmail($booking);
            
            if ($guestEmail && $this->shouldSendNotification($newStatus)) {
                $this->sendEmailNotification($booking, $newStatus, $guestEmail);
            }

            // Send admin notification for important status changes
            if ($this->shouldNotifyAdmin($newStatus)) {
                $this->sendAdminNotification($booking, $newStatus);
            }

            // Log the notification
            Log::info('Booking status notification processed', [
                'order_id' => $booking->order_id,
                'status' => $newStatus,
                'guest_email' => $guestEmail ? 'sent' : 'no_email'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send booking status notification', [
                'order_id' => $event->booking->order_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Get guest email from booking data
     */
    protected function getGuestEmail($booking): ?string
    {
        if ($booking->guest_data && is_array($booking->guest_data)) {
            foreach ($booking->guest_data as $guest) {
                if (isset($guest['email']) && !empty($guest['email'])) {
                    return $guest['email'];
                }
            }
        }

        return null;
    }

    /**
     * Check if notification should be sent for this status
     */
    protected function shouldSendNotification(string $status): bool
    {
        $notifiableStatuses = [
            'confirmed',
            'cancelled',
            'completed',
            'failed'
        ];

        return in_array($status, $notifiableStatuses);
    }

    /**
     * Check if admin should be notified for this status
     */
    protected function shouldNotifyAdmin(string $status): bool
    {
        $adminNotifiableStatuses = [
            'failed',
            'cancelled'
        ];

        return in_array($status, $adminNotifiableStatuses);
    }

    /**
     * Send email notification to guest
     */
    protected function sendEmailNotification($booking, string $status, string $email): void
    {
        try {
            // Create email data
            $emailData = [
                'booking' => $booking,
                'status' => $status,
                'guest_email' => $email,
                'hotel_name' => $booking->hotel_name,
                'order_id' => $booking->order_id,
                'checkin_date' => $booking->checkin_date?->format('Y-m-d'),
                'checkout_date' => $booking->checkout_date?->format('Y-m-d'),
            ];

            // Send appropriate email based on status
            switch ($status) {
                case 'confirmed':
                    $this->sendConfirmationEmail($emailData);
                    break;
                case 'cancelled':
                    $this->sendCancellationEmail($emailData);
                    break;
                case 'completed':
                    $this->sendCompletionEmail($emailData);
                    break;
                case 'failed':
                    $this->sendFailureEmail($emailData);
                    break;
            }

        } catch (\Exception $e) {
            Log::error('Failed to send guest email notification', [
                'order_id' => $booking->order_id,
                'email' => $email,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send admin notification
     */
    protected function sendAdminNotification($booking, string $status): void
    {
        try {
            $adminEmail = config('mail.admin_email');
            
            if (!$adminEmail) {
                return;
            }

            $emailData = [
                'booking' => $booking,
                'status' => $status,
                'admin_email' => $adminEmail,
            ];

            // Send admin notification email
            // Implementation depends on your email system
            Log::info('Admin notification sent for booking status change', [
                'order_id' => $booking->order_id,
                'status' => $status,
                'admin_email' => $adminEmail
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send admin notification', [
                'order_id' => $booking->order_id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Send booking confirmation email
     */
    protected function sendConfirmationEmail(array $data): void
    {
        // Implementation depends on your email system
        Log::info('Booking confirmation email sent', [
            'order_id' => $data['order_id'],
            'email' => $data['guest_email']
        ]);
    }

    /**
     * Send booking cancellation email
     */
    protected function sendCancellationEmail(array $data): void
    {
        // Implementation depends on your email system
        Log::info('Booking cancellation email sent', [
            'order_id' => $data['order_id'],
            'email' => $data['guest_email']
        ]);
    }

    /**
     * Send booking completion email
     */
    protected function sendCompletionEmail(array $data): void
    {
        // Implementation depends on your email system
        Log::info('Booking completion email sent', [
            'order_id' => $data['order_id'],
            'email' => $data['guest_email']
        ]);
    }

    /**
     * Send booking failure email
     */
    protected function sendFailureEmail(array $data): void
    {
        // Implementation depends on your email system
        Log::info('Booking failure email sent', [
            'order_id' => $data['order_id'],
            'email' => $data['guest_email']
        ]);
    }
}
