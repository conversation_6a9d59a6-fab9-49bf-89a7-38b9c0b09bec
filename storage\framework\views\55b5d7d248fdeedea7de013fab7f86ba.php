<?php $__env->startPush('css'); ?>
<style>
.booking-history {
    padding: 60px 0;
    background: #f8f9fa;
}

.booking-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s ease;
}

.booking-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.booking-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
}

.booking-body {
    padding: 20px;
}

.status-badge {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-confirmed {
    background: #d4edda;
    color: #155724;
}

.status-pending {
    background: #fff3cd;
    color: #856404;
}

.status-cancelled {
    background: #f8d7da;
    color: #721c24;
}

.search-form {
    background: white;
    border-radius: 12px;
    padding: 30px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    margin-bottom: 40px;
}

.btn-cancel {
    background: #dc3545;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: #c82333;
    color: white;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="booking-history">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <h1 class="text-center mb-5"><?php echo e(__('My Bookings')); ?></h1>
                
                <!-- Search Form -->
                <div class="search-form">
                    <h4 class="mb-3"><?php echo e(__('Find Your Bookings')); ?></h4>
                    <form id="booking-search-form">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="search_email"><?php echo e(__('Email Address')); ?></label>
                                    <input type="email" class="form-control" id="search_email" name="email" 
                                           placeholder="<?php echo e(__('Enter your email address')); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fa fa-search"></i> <?php echo e(__('Search Bookings')); ?>

                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Bookings List -->
                <div id="bookings-container">
                    <div class="text-center py-5" id="no-bookings">
                        <i class="fa fa-calendar-o fa-3x text-muted mb-3"></i>
                        <h5><?php echo e(__('No bookings found')); ?></h5>
                        <p class="text-muted"><?php echo e(__('Enter your email address to view your booking history')); ?></p>
                        <a href="<?php echo e(url('/home2')); ?>" class="btn btn-primary">
                            <i class="fa fa-search"></i> <?php echo e(__('Search Hotels')); ?>

                        </a>
                    </div>
                </div>

                <!-- Loading State -->
                <div id="loading-bookings" class="text-center py-5" style="display: none;">
                    <i class="fa fa-spinner fa-spin fa-3x text-muted mb-3"></i>
                    <h5><?php echo e(__('Loading bookings...')); ?></h5>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Booking Details Modal -->
<div class="modal fade" id="bookingDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__('Booking Details')); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="booking-details-content">
                    <!-- Booking details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__('Close')); ?></button>
                <button type="button" class="btn btn-danger" id="cancelBookingBtn" style="display: none;">
                    <?php echo e(__('Cancel Booking')); ?>

                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cancel Booking Modal -->
<div class="modal fade" id="cancelBookingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e(__('Cancel Booking')); ?></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p><?php echo e(__('Are you sure you want to cancel this booking?')); ?></p>
                <div class="form-group">
                    <label for="cancel_reason"><?php echo e(__('Reason for cancellation (optional)')); ?></label>
                    <textarea class="form-control" id="cancel_reason" name="reason" rows="3" 
                              placeholder="<?php echo e(__('Please provide a reason for cancellation...')); ?>"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal"><?php echo e(__('Keep Booking')); ?></button>
                <button type="button" class="btn btn-danger" id="confirmCancelBtn"><?php echo e(__('Confirm Cancellation')); ?></button>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    let currentBookingReference = null;
    
    // Search bookings form submission
    $('#booking-search-form').on('submit', function(e) {
        e.preventDefault();
        searchBookings();
    });
    
    // Cancel booking button click
    $('#cancelBookingBtn').on('click', function() {
        $('#bookingDetailsModal').modal('hide');
        $('#cancelBookingModal').modal('show');
    });
    
    // Confirm cancellation
    $('#confirmCancelBtn').on('click', function() {
        if (currentBookingReference) {
            cancelBooking(currentBookingReference);
        }
    });
});

function searchBookings() {
    const email = $('#search_email').val();
    
    if (!email) {
        showAlert('warning', '<?php echo e(__("Please enter your email address")); ?>');
        return;
    }
    
    // Show loading
    $('#loading-bookings').show();
    $('#no-bookings').hide();
    $('#bookings-container').empty();
    
    $.get('<?php echo e(route("api.booking.history")); ?>', { email: email })
        .done(function(response) {
            $('#loading-bookings').hide();
            
            if (response.success && response.data.length > 0) {
                displayBookings(response.data);
            } else {
                $('#no-bookings').show();
            }
        })
        .fail(function(xhr) {
            $('#loading-bookings').hide();
            $('#no-bookings').show();
            
            const response = xhr.responseJSON;
            showAlert('danger', response?.message || '<?php echo e(__("Failed to load bookings")); ?>');
        });
}

function displayBookings(bookings) {
    let html = '';
    
    bookings.forEach(function(booking) {
        const statusClass = 'status-' + booking.status;
        const statusText = booking.status.charAt(0).toUpperCase() + booking.status.slice(1);
        
        html += `
            <div class="booking-card">
                <div class="booking-header">
                    <div class="row">
                        <div class="col-md-8">
                            <h5 class="mb-1">${booking.hotel_name}</h5>
                            <p class="mb-0"><i class="fa fa-map-marker"></i> ${booking.location}</p>
                        </div>
                        <div class="col-md-4 text-right">
                            <span class="status-badge ${statusClass}">${statusText}</span>
                            <h6 class="mt-2 mb-0">${booking.currency} ${booking.total_price}</h6>
                        </div>
                    </div>
                </div>
                <div class="booking-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong><?php echo e(__('Reference')); ?>:</strong> ${booking.reference}</p>
                            <p><strong><?php echo e(__('Check-in')); ?>:</strong> ${booking.checkin}</p>
                            <p><strong><?php echo e(__('Check-out')); ?>:</strong> ${booking.checkout}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong><?php echo e(__('Booked on')); ?>:</strong> ${new Date(booking.created_at).toLocaleDateString()}</p>
                            <div class="text-right">
                                <button class="btn btn-outline-primary btn-sm" onclick="viewBookingDetails('${booking.reference}')">
                                    <?php echo e(__('View Details')); ?>

                                </button>
                                ${booking.status === 'confirmed' ? `
                                <button class="btn btn-cancel btn-sm ml-2" onclick="showCancelBooking('${booking.reference}')">
                                    <?php echo e(__('Cancel')); ?>

                                </button>
                                ` : ''}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#bookings-container').html(html);
}

function viewBookingDetails(reference) {
    $.get(`/api/booking/${reference}`)
        .done(function(response) {
            if (response.success) {
                displayBookingDetails(response.data);
                $('#bookingDetailsModal').modal('show');
            } else {
                showAlert('danger', '<?php echo e(__("Failed to load booking details")); ?>');
            }
        })
        .fail(function() {
            showAlert('danger', '<?php echo e(__("Failed to load booking details")); ?>');
        });
}

function displayBookingDetails(booking) {
    const detailsHtml = `
        <div class="row">
            <div class="col-12">
                <h4>${booking.hotel.name}</h4>
                <p class="text-muted">${booking.hotel.location}</p>
                <hr>
                <div class="row">
                    <div class="col-md-6">
                        <h6><?php echo e(__('Booking Information')); ?></h6>
                        <p><strong><?php echo e(__('Reference')); ?>:</strong> ${booking.reference}</p>
                        <p><strong><?php echo e(__('Status')); ?>:</strong> <span class="status-badge status-${booking.status}">${booking.status}</span></p>
                        <p><strong><?php echo e(__('Guest')); ?>:</strong> ${booking.guest_info.name}</p>
                        <p><strong><?php echo e(__('Email')); ?>:</strong> ${booking.guest_info.email}</p>
                    </div>
                    <div class="col-md-6">
                        <h6><?php echo e(__('Stay Details')); ?></h6>
                        <p><strong><?php echo e(__('Check-in')); ?>:</strong> ${booking.checkin || 'N/A'}</p>
                        <p><strong><?php echo e(__('Check-out')); ?>:</strong> ${booking.checkout || 'N/A'}</p>
                        <p><strong><?php echo e(__('Booked on')); ?>:</strong> ${new Date(booking.created_at).toLocaleDateString()}</p>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    $('#booking-details-content').html(detailsHtml);
    
    // Show cancel button only for confirmed bookings
    if (booking.status === 'confirmed') {
        $('#cancelBookingBtn').show();
        currentBookingReference = booking.reference;
    } else {
        $('#cancelBookingBtn').hide();
    }
}

function showCancelBooking(reference) {
    currentBookingReference = reference;
    $('#cancelBookingModal').modal('show');
}

function cancelBooking(reference) {
    const reason = $('#cancel_reason').val();
    
    const $btn = $('#confirmCancelBtn');
    const originalText = $btn.text();
    $btn.text('<?php echo e(__("Cancelling...")); ?>').prop('disabled', true);
    
    $.post(`/api/booking/${reference}/cancel`, {
        reason: reason,
        _token: $('meta[name="csrf-token"]').attr('content')
    })
    .done(function(response) {
        $btn.text(originalText).prop('disabled', false);
        
        if (response.success) {
            $('#cancelBookingModal').modal('hide');
            showAlert('success', response.message);
            
            // Refresh bookings list
            searchBookings();
        } else {
            showAlert('danger', response.message || '<?php echo e(__("Failed to cancel booking")); ?>');
        }
    })
    .fail(function() {
        $btn.text(originalText).prop('disabled', false);
        showAlert('danger', '<?php echo e(__("Failed to cancel booking")); ?>');
    });
}

function showAlert(type, message) {
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="close" data-dismiss="alert">
                <span>&times;</span>
            </button>
        </div>
    `;
    $('.container').prepend(alertHtml);
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('Layout::app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\wamp64\www\mazar\resources\views/frontend/booking-history.blade.php ENDPATH**/ ?>