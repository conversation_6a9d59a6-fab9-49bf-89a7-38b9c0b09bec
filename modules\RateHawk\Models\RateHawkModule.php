<?php

namespace Modules\RateHawk\Models;

use App\BaseModel;

class RateHawkModule extends BaseModel
{
    protected $table = 'ratehawk_settings'; // This can be a dummy table or use existing settings

    public static function isEnable()
    {
        return setting_item('ratehawk_enable') == '1';
    }

    public static function getModelName()
    {
        return __("RateHawk API");
    }

    public static function getServiceIconFeatured()
    {
        return "fa fa-hotel";
    }

    public static function isInstalled()
    {
        return true; // Module is always considered installed if files exist
    }

    public static function getVersion()
    {
        return '1.0.0';
    }

    public static function getDescription()
    {
        return __("RateHawk B2B API integration for hotel bookings and management");
    }

    public static function getAuthor()
    {
        return "Your Company";
    }

    public static function getRequirements()
    {
        return [
            'php' => '>=8.0',
            'laravel' => '>=8.0',
            'extensions' => ['curl', 'json', 'openssl']
        ];
    }

    public static function getConfigurationStatus()
    {
        $status = [
            'enabled' => static::isEnable(),
            'configured' => false,
            'api_connected' => false,
            'errors' => []
        ];

        // Check if API credentials are configured
        $keyId = setting_item('ratehawk_key_id');
        $apiKey = setting_item('ratehawk_api_key');
        
        if (!empty($keyId) && !empty($apiKey)) {
            $status['configured'] = true;
        } else {
            $status['errors'][] = __('API credentials not configured');
        }

        return $status;
    }

    public static function getStatistics()
    {
        $stats = [
            'total_bookings' => 0,
            'active_bookings' => 0,
            'total_searches' => 0,
            'api_calls_today' => 0,
        ];

        try {
            // Get booking statistics
            if (class_exists('\Modules\RateHawk\Models\RateHawkBooking')) {
                $stats['total_bookings'] = \Modules\RateHawk\Models\RateHawkBooking::count();
                $stats['active_bookings'] = \Modules\RateHawk\Models\RateHawkBooking::whereIn('status', ['confirmed', 'pending'])->count();
            }

            // Get API call statistics
            if (class_exists('\Modules\RateHawk\Models\RateHawkApiLog')) {
                $today = now()->startOfDay();
                $stats['api_calls_today'] = \Modules\RateHawk\Models\RateHawkApiLog::where('created_at', '>=', $today)->count();
                $stats['total_searches'] = \Modules\RateHawk\Models\RateHawkApiLog::where('endpoint', 'like', '%search%')->count();
            }
        } catch (\Exception $e) {
            // Ignore errors if tables don't exist yet
        }

        return $stats;
    }

    public static function getHealthStatus()
    {
        $health = [
            'status' => 'healthy',
            'checks' => []
        ];

        // Check if module is enabled
        $health['checks']['enabled'] = [
            'status' => static::isEnable() ? 'pass' : 'warning',
            'message' => static::isEnable() ? __('Module is enabled') : __('Module is disabled')
        ];

        // Check API configuration
        $configStatus = static::getConfigurationStatus();
        $health['checks']['configuration'] = [
            'status' => $configStatus['configured'] ? 'pass' : 'fail',
            'message' => $configStatus['configured'] ? __('API credentials configured') : __('API credentials missing')
        ];

        // Check database tables
        try {
            \DB::table('ratehawk_bookings')->limit(1)->get();
            $health['checks']['database'] = [
                'status' => 'pass',
                'message' => __('Database tables accessible')
            ];
        } catch (\Exception $e) {
            $health['checks']['database'] = [
                'status' => 'fail',
                'message' => __('Database tables not accessible: ') . $e->getMessage()
            ];
            $health['status'] = 'unhealthy';
        }

        // Check recent API errors
        try {
            if (class_exists('\Modules\RateHawk\Models\RateHawkApiLog')) {
                $recentErrors = \Modules\RateHawk\Models\RateHawkApiLog::where('status', 'error')
                    ->where('created_at', '>=', now()->subHour())
                    ->count();
                
                if ($recentErrors > 10) {
                    $health['checks']['api_errors'] = [
                        'status' => 'warning',
                        'message' => __('High error rate detected: ') . $recentErrors . __(' errors in last hour')
                    ];
                    $health['status'] = 'degraded';
                } else {
                    $health['checks']['api_errors'] = [
                        'status' => 'pass',
                        'message' => __('API error rate normal')
                    ];
                }
            }
        } catch (\Exception $e) {
            // Ignore if table doesn't exist
        }

        return $health;
    }

    public static function getMenuItems()
    {
        if (!static::isEnable()) {
            return [];
        }

        return [
            [
                'title' => __('RateHawk'),
                'icon' => 'fa fa-hotel',
                'url' => url('/admin/ratehawk'),
                'permission' => 'ratehawk_view',
                'children' => [
                    [
                        'title' => __('Dashboard'),
                        'url' => url('/admin/ratehawk'),
                        'permission' => 'ratehawk_view'
                    ],
                    [
                        'title' => __('Bookings'),
                        'url' => url('/admin/ratehawk/bookings'),
                        'permission' => 'ratehawk_view'
                    ],
                    [
                        'title' => __('API Logs'),
                        'url' => url('/admin/ratehawk/logs'),
                        'permission' => 'ratehawk_view'
                    ],
                    [
                        'title' => __('Settings'),
                        'url' => url('/admin/settings?group=ratehawk'),
                        'permission' => 'ratehawk_settings'
                    ]
                ]
            ]
        ];
    }
}
