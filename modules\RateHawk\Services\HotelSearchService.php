<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Log;
use Modules\RateHawk\Services\RateHawkApiClient;
use Modules\RateHawk\Services\CacheService;
use Modules\RateHawk\Models\RateHawkSearchCache;
use Modules\RateHawk\Models\RateHawkHotelMapping;
use Carbon\Carbon;

class HotelSearchService
{
    protected $apiClient;
    protected $cacheService;

    public function __construct(RateHawkApiClient $apiClient, CacheService $cacheService)
    {
        $this->apiClient = $apiClient;
        $this->cacheService = $cacheService;
    }

    /**
     * Search hotels with intelligent caching
     */
    public function searchHotels(array $searchParams): array
    {
        try {
            // Validate search parameters
            $validatedParams = $this->validateSearchParams($searchParams);

            // Record user search for analytics
            $this->recordUserSearch($validatedParams);

            // Try to get from cache first
            $cachedResults = $this->cacheService->getSearchResults($validatedParams);
            if ($cachedResults) {
                Log::info('Hotel search served from cache', ['params' => $validatedParams]);
                return $this->formatSearchResponse($cachedResults, true);
            }

            // Cache miss - call API
            Log::info('Hotel search cache miss, calling API', ['params' => $validatedParams]);
            $apiResults = $this->callSearchAPI($validatedParams);

            if ($apiResults['success']) {
                // Cache the results
                $this->cacheService->cacheSearchResults($validatedParams, $apiResults['data']);

                // Process and cache individual hotel data
                $this->processHotelResults($apiResults['data']);

                return $this->formatSearchResponse($apiResults['data'], false);
            }

            return $this->handleSearchError($apiResults['error'] ?? 'Unknown API error');

        } catch (\Exception $e) {
            Log::error('Hotel search failed', ['error' => $e->getMessage(), 'params' => $searchParams]);
            return $this->handleSearchError($e->getMessage());
        }
    }

    /**
     * Get hotel details with caching
     */
    public function getHotelDetails(string $hotelId, array $searchParams = []): array
    {
        try {
            // Try cache first
            $cachedData = $this->cacheService->getHotelStatic($hotelId);
            if ($cachedData) {
                // Get pricing if search params provided
                $pricingData = null;
                if (!empty($searchParams)) {
                    $pricingData = $this->getHotelPricing($hotelId, $searchParams);
                }

                return [
                    'success' => true,
                    'data' => array_merge($cachedData, [
                        'pricing' => $pricingData,
                        'from_cache' => true
                    ])
                ];
            }

            // Cache miss - call API
            $apiResult = $this->apiClient->getHotelStatic($hotelId);
            if ($apiResult['success']) {
                // Cache the static data
                $this->cacheService->cacheHotelStatic($hotelId, $apiResult['data']);

                // Get pricing if needed
                $pricingData = null;
                if (!empty($searchParams)) {
                    $pricingData = $this->getHotelPricing($hotelId, $searchParams);
                }

                return [
                    'success' => true,
                    'data' => array_merge($apiResult['data'], [
                        'pricing' => $pricingData,
                        'from_cache' => false
                    ])
                ];
            }

            return $apiResult;

        } catch (\Exception $e) {
            Log::error('Get hotel details failed', ['error' => $e->getMessage(), 'hotel_id' => $hotelId]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get hotel pricing with short-term caching
     */
    public function getHotelPricing(string $hotelId, array $searchParams): ?array
    {
        try {
            // Try cache first (short TTL for pricing)
            $cachedPricing = $this->cacheService->getHotelPricing($hotelId, $searchParams);
            if ($cachedPricing) {
                return $cachedPricing;
            }

            // Call pricing API
            $pricingResult = $this->apiClient->getHotelPricing($hotelId, $searchParams);
            if ($pricingResult['success']) {
                // Cache with short TTL
                $this->cacheService->cacheHotelPricing($hotelId, $searchParams, $pricingResult['data']);
                return $pricingResult['data'];
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Get hotel pricing failed', ['error' => $e->getMessage(), 'hotel_id' => $hotelId]);
            return null;
        }
    }

    /**
     * Search destinations for autocomplete
     */
    public function searchDestinations(string $query, int $limit = 10): array
    {
        try {
            // Try cache first
            $cacheKey = "destinations:" . md5(strtolower($query));
            $cached = $this->cacheService->getSearchResults(['type' => 'destinations', 'query' => $query]);

            if ($cached) {
                return ['success' => true, 'data' => array_slice($cached, 0, $limit)];
            }

            // Call API
            $apiResult = $this->apiClient->searchDestinations($query, $limit);
            if ($apiResult['success']) {
                // Cache for longer period (destinations don't change often)
                $this->cacheService->cacheSearchResults(
                    ['type' => 'destinations', 'query' => $query],
                    $apiResult['data']
                );
            }

            return $apiResult;

        } catch (\Exception $e) {
            Log::error('Destination search failed', ['error' => $e->getMessage(), 'query' => $query]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get popular destinations
     */
    public function getPopularDestinations(int $limit = 20): array
    {
        try {
            // Try cache first
            $cached = $this->cacheService->getSearchResults(['type' => 'popular_destinations']);
            if ($cached) {
                return ['success' => true, 'data' => array_slice($cached, 0, $limit)];
            }

            // Get from analytics + API
            $popularFromSearch = RateHawkSearchCache::getPopularDestinations($limit);

            // Supplement with API if needed
            if (count($popularFromSearch) < $limit) {
                $apiResult = $this->apiClient->getPopularDestinations($limit);
                if ($apiResult['success']) {
                    $popularFromSearch = array_merge($popularFromSearch, $apiResult['data']);
                }
            }

            // Cache the results
            $this->cacheService->cacheSearchResults(
                ['type' => 'popular_destinations'],
                $popularFromSearch
            );

            return ['success' => true, 'data' => array_slice($popularFromSearch, 0, $limit)];

        } catch (\Exception $e) {
            Log::error('Get popular destinations failed', ['error' => $e->getMessage()]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Get search suggestions based on user input
     */
    public function getSearchSuggestions(string $input): array
    {
        try {
            $suggestions = [];

            // Get destination suggestions
            $destinations = $this->searchDestinations($input, 5);
            if ($destinations['success']) {
                foreach ($destinations['data'] as $dest) {
                    $suggestions[] = [
                        'type' => 'destination',
                        'text' => $dest['name'],
                        'subtitle' => $dest['country'] ?? '',
                        'data' => $dest
                    ];
                }
            }

            // Get hotel suggestions if input is specific enough
            if (strlen($input) >= 3) {
                $hotels = $this->searchHotelsByName($input, 3);
                foreach ($hotels as $hotel) {
                    $suggestions[] = [
                        'type' => 'hotel',
                        'text' => $hotel['name'],
                        'subtitle' => $hotel['location'] ?? '',
                        'data' => $hotel
                    ];
                }
            }

            return ['success' => true, 'data' => $suggestions];

        } catch (\Exception $e) {
            Log::error('Get search suggestions failed', ['error' => $e->getMessage(), 'input' => $input]);
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // Private helper methods

    private function validateSearchParams(array $params): array
    {
        $validated = [];

        // Required parameters
        $validated['destination'] = $params['destination'] ?? '';
        $validated['checkin'] = $params['checkin'] ?? date('Y-m-d', strtotime('+1 day'));
        $validated['checkout'] = $params['checkout'] ?? date('Y-m-d', strtotime('+2 days'));
        $validated['adults'] = (int)($params['adults'] ?? 1);

        // Optional parameters
        $validated['children'] = (int)($params['children'] ?? 0);
        $validated['rooms'] = (int)($params['rooms'] ?? 1);
        $validated['currency'] = $params['currency'] ?? 'USD';

        // Filters
        $validated['min_price'] = isset($params['min_price']) ? (float)$params['min_price'] : null;
        $validated['max_price'] = isset($params['max_price']) ? (float)$params['max_price'] : null;
        $validated['star_rating'] = isset($params['star_rating']) ? (array)$params['star_rating'] : [];
        $validated['amenities'] = isset($params['amenities']) ? (array)$params['amenities'] : [];

        // Sorting
        $validated['sort_by'] = $params['sort_by'] ?? 'price';
        $validated['sort_order'] = $params['sort_order'] ?? 'asc';

        // Pagination
        $validated['page'] = (int)($params['page'] ?? 1);
        $validated['per_page'] = min((int)($params['per_page'] ?? 20), 100); // Max 100 per page

        return $validated;
    }

    private function callSearchAPI(array $params): array
    {
        return $this->apiClient->searchHotels($params);
    }

    private function processHotelResults(array $hotels): void
    {
        foreach ($hotels as $hotel) {
            if (isset($hotel['id'])) {
                // Cache individual hotel static data
                $this->cacheService->cacheHotelStatic($hotel['id'], $hotel);
            }
        }
    }

    private function formatSearchResponse(array $data, bool $fromCache): array
    {
        return [
            'success' => true,
            'data' => [
                'hotels' => $data['hotels'] ?? $data,
                'total' => $data['total'] ?? count($data),
                'page' => $data['page'] ?? 1,
                'per_page' => $data['per_page'] ?? 20,
                'from_cache' => $fromCache,
                'search_id' => $data['search_id'] ?? uniqid('search_'),
            ]
        ];
    }

    private function handleSearchError(string $error): array
    {
        return [
            'success' => false,
            'error' => $error,
            'data' => [
                'hotels' => [],
                'total' => 0,
                'from_cache' => false,
            ]
        ];
    }

    private function recordUserSearch(array $params): void
    {
        try {
            // This would be implemented to track user searches for analytics
            // For now, we'll just log it
            Log::info('User search recorded', ['params' => $params]);
        } catch (\Exception $e) {
            // Don't fail the search if analytics recording fails
            Log::warning('Failed to record user search', ['error' => $e->getMessage()]);
        }
    }

    private function searchHotelsByName(string $name, int $limit): array
    {
        try {
            // Search in cached hotel mappings first
            $hotels = RateHawkHotelMapping::active()
                ->whereJsonContains('hotel_static_data->name', $name)
                ->limit($limit)
                ->get()
                ->map(function ($mapping) {
                    return [
                        'id' => $mapping->ratehawk_hotel_id,
                        'name' => $mapping->hotel_name,
                        'location' => $mapping->hotel_location,
                    ];
                })
                ->toArray();

            return $hotels;
        } catch (\Exception $e) {
            Log::error('Search hotels by name failed', ['error' => $e->getMessage(), 'name' => $name]);
            return [];
        }
    }
}
