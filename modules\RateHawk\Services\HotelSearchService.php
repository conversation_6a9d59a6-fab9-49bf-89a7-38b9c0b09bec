<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Validator;
use Modules\RateHawk\Exceptions\RateHawkApiException;
use Modules\RateHawk\Helpers\ConfigHelper;

class HotelSearchService
{
    protected $apiClient;
    protected $cacheEnabled;
    protected $cacheTtl;

    public function __construct(RateHawkApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
        $cacheConfig = ConfigHelper::getCacheConfig();
        $this->cacheEnabled = $cacheConfig['enabled'];
        $this->cacheTtl = $cacheConfig['ttl']['search_results'];
    }

    /**
     * Search hotels by region
     */
    public function searchByRegion(array $params): array
    {
        $this->validateSearchParams($params);

        $cacheKey = $this->getCacheKey('region', $params);

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/search/serp/region/';
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl);
        }

        return $response;
    }

    /**
     * Search hotels by hotel IDs
     */
    public function searchByHotels(array $params): array
    {
        $this->validateHotelSearchParams($params);

        $cacheKey = $this->getCacheKey('hotels', $params);

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/search/serp/hotels/';
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl);
        }

        return $response;
    }

    /**
     * Search hotels by geo coordinates
     */
    public function searchByCoordinates(array $params): array
    {
        $this->validateCoordinateSearchParams($params);

        $cacheKey = $this->getCacheKey('coordinates', $params);

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/search/serp/coordinates/';
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl);
        }

        return $response;
    }

    /**
     * Get hotel suggestions
     */
    public function suggest(array $params): array
    {
        $validator = Validator::make($params, [
            'query' => 'required|string|min:2',
            'language' => 'nullable|string|size:2',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid suggestion parameters: ' . implode(', ', $validator->errors()->all()));
        }

        $cacheKey = $this->getCacheKey('suggest', $params);

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/search/suggest/';
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl);
        }

        return $response;
    }

    /**
     * Get hotel page details
     */
    public function getHotelPage(array $params): array
    {
        $this->validateHotelPageParams($params);

        $cacheKey = $this->getCacheKey('hotelpage', $params);

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/search/hp/';
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl);
        }

        return $response;
    }

    /**
     * Prebook a rate
     */
    public function prebook(array $params): array
    {
        $this->validatePrebookParams($params);

        $endpoint = '/api/b2b/v3/search/prebook/';
        return $this->apiClient->post($endpoint, $params);
    }

    /**
     * Validate basic search parameters
     */
    protected function validateSearchParams(array $params): void
    {
        $validator = Validator::make($params, [
            'checkin' => 'required|date|after_or_equal:today',
            'checkout' => 'required|date|after:checkin',
            'residency' => 'required|string|size:2',
            'language' => 'nullable|string|size:2',
            'currency' => 'nullable|string|size:3',
            'guests' => 'required|array|min:1|max:9',
            'guests.*.adults' => 'required|integer|min:1|max:6',
            'guests.*.children' => 'nullable|array|max:4',
            'guests.*.children.*' => 'integer|min:0|max:17',
            'region_id' => 'required|integer|min:1',
            'hotels_limit' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid search parameters: ' . implode(', ', $validator->errors()->all()));
        }

        // Additional validation
        $checkin = \Carbon\Carbon::parse($params['checkin']);
        $checkout = \Carbon\Carbon::parse($params['checkout']);

        if ($checkin->diffInDays($checkout) > 30) {
            throw new RateHawkApiException('Stay duration cannot exceed 30 days');
        }

        if ($checkin->diffInDays(now()) > 730) {
            throw new RateHawkApiException('Check-in date cannot be more than 730 days in the future');
        }
    }

    /**
     * Validate hotel search parameters
     */
    protected function validateHotelSearchParams(array $params): void
    {
        $this->validateSearchParams($params);

        $validator = Validator::make($params, [
            'ids' => 'required|array|min:1',
            'ids.*' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid hotel search parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Validate coordinate search parameters
     */
    protected function validateCoordinateSearchParams(array $params): void
    {
        $this->validateSearchParams($params);

        $validator = Validator::make($params, [
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:0.1|max:50',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid coordinate search parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Validate hotel page parameters
     */
    protected function validateHotelPageParams(array $params): void
    {
        $this->validateSearchParams($params);

        $validator = Validator::make($params, [
            'id' => 'required|string',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid hotel page parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Validate prebook parameters
     */
    protected function validatePrebookParams(array $params): void
    {
        $validator = Validator::make($params, [
            'search_hash' => 'required|string',
            'match_hash' => 'required|string',
            'language' => 'nullable|string|size:2',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid prebook parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Generate cache key for search results
     */
    protected function getCacheKey(string $type, array $params): string
    {
        return 'ratehawk_search_' . $type . '_' . md5(json_encode($params));
    }

    /**
     * Clear search cache
     */
    public function clearCache(): void
    {
        if ($this->cacheEnabled) {
            Cache::flush(); // This is a simple approach, in production you might want to be more selective
        }
    }
}
