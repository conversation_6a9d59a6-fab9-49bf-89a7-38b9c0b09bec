@extends('admin.layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between">
            <h1 class="h3 mb-3 text-gray-800">{{$page_title}}</h1>
        </div>
        @include('admin.message')
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    {{__('Total Requests')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['total'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    {{__('Successful')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['completed'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    {{__('Errors')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['error'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    {{__('Pending')}}
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800">{{$stats['pending'] ?? 0}}</div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">{{__('Filters')}}</h6>
            </div>
            <div class="card-body">
                <form method="GET" action="{{route('ratehawk.admin.logs.index')}}">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>{{__('Status')}}</label>
                                <select name="status" class="form-control">
                                    <option value="">{{__('All Statuses')}}</option>
                                    @foreach($statuses as $key => $label)
                                        <option value="{{$key}}" {{($filters['status'] ?? '') == $key ? 'selected' : ''}}>
                                            {{$label}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>{{__('Method')}}</label>
                                <select name="method" class="form-control">
                                    <option value="">{{__('All Methods')}}</option>
                                    @foreach($methods as $method)
                                        <option value="{{$method}}" {{($filters['method'] ?? '') == $method ? 'selected' : ''}}>
                                            {{$method}}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>{{__('From Date')}}</label>
                                <input type="date" name="from_date" class="form-control" value="{{$filters['from_date'] ?? ''}}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>{{__('To Date')}}</label>
                                <input type="date" name="to_date" class="form-control" value="{{$filters['to_date'] ?? ''}}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>{{__('Search')}}</label>
                                <input type="text" name="search" class="form-control" placeholder="{{__('Request ID, Endpoint, Error...')}}" value="{{$filters['search'] ?? ''}}">
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">{{__('Filter')}}</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">{{__('API Logs')}}</h6>
                <div>
                    <a href="{{route('ratehawk.admin.logs.export')}}" class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> {{__('Export')}}
                    </a>
                    <button type="button" class="btn btn-danger btn-sm" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> {{__('Clear All')}}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>{{__('Request ID')}}</th>
                                <th>{{__('Method')}}</th>
                                <th>{{__('Endpoint')}}</th>
                                <th>{{__('Status')}}</th>
                                <th>{{__('Response Time')}}</th>
                                <th>{{__('User')}}</th>
                                <th>{{__('Created At')}}</th>
                                <th>{{__('Actions')}}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($logs as $log)
                                <tr>
                                    <td>
                                        <code>{{$log->request_id}}</code>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary">{{$log->method}}</span>
                                    </td>
                                    <td>
                                        <small>{{$log->endpoint}}</small>
                                    </td>
                                    <td>
                                        @if($log->status == 'completed')
                                            <span class="badge badge-success">{{__('Completed')}}</span>
                                        @elseif($log->status == 'error')
                                            <span class="badge badge-danger">{{__('Error')}}</span>
                                        @else
                                            <span class="badge badge-warning">{{__('Pending')}}</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($log->response_time)
                                            {{$log->response_time}}ms
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>
                                        @if($log->user)
                                            {{$log->user->name}}
                                        @else
                                            {{__('System')}}
                                        @endif
                                    </td>
                                    <td>
                                        {{$log->created_at->format('Y-m-d H:i:s')}}
                                    </td>
                                    <td>
                                        <a href="{{route('ratehawk.admin.logs.show', $log->id)}}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteLog({{$log->id}})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center">{{__('No logs found')}}</td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{$logs->appends(request()->query())->links()}}
                </div>
            </div>
        </div>
    </div>

    <script>
        function clearLogs() {
            if (confirm('{{__("Are you sure you want to clear all logs? This action cannot be undone.")}}')) {
                $.post('{{route("ratehawk.admin.logs.clear")}}', {
                    _token: '{{csrf_token()}}'
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || '{{__("Error occurred")}}');
                    }
                });
            }
        }

        function deleteLog(id) {
            if (confirm('{{__("Are you sure you want to delete this log?")}}')) {
                $.ajax({
                    url: '{{route("ratehawk.admin.logs.destroy", ":id")}}'.replace(':id', id),
                    method: 'DELETE',
                    data: {
                        _token: '{{csrf_token()}}'
                    }
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || '{{__("Error occurred")}}');
                    }
                });
            }
        }
    </script>
@endsection
