<?php

namespace Modules\RateHawk\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Modules\Admin\Controllers\Controller;
use Modules\RateHawk\Services\RateHawkApiClient;
use Modules\RateHawk\Services\BookingService;
use Modules\RateHawk\Services\StaticContentService;
use Modules\RateHawk\Models\RateHawkSetting;
use Modules\RateHawk\Models\RateHawkBooking;
use Modules\RateHawk\Models\RateHawkApiLog;

class RateHawkController extends Controller
{
    protected $apiClient;
    protected $bookingService;
    protected $staticContentService;

    public function __construct(
        RateHawkApiClient $apiClient,
        BookingService $bookingService,
        StaticContentService $staticContentService
    ) {
        parent::__construct();
        $this->apiClient = $apiClient;
        $this->bookingService = $bookingService;
        $this->staticContentService = $staticContentService;
    }

    /**
     * Display the main dashboard
     */
    public function index(Request $request): View
    {
        $this->checkPermission('ratehawk_view');

        // Get dashboard statistics
        $stats = $this->getDashboardStats();

        // Get recent bookings
        $recentBookings = RateHawkBooking::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        // Get recent API logs
        $recentLogs = RateHawkApiLog::orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        $data = [
            'page_title' => __('RateHawk Dashboard'),
            'stats' => $stats,
            'recent_bookings' => $recentBookings,
            'recent_logs' => $recentLogs,
            'breadcrumbs' => [
                ['name' => __('Dashboard'), 'url' => route('admin.index')],
                ['name' => __('RateHawk'), 'class' => 'active'],
            ],
        ];

        return view('RateHawk::admin.index', $data);
    }

    /**
     * Display settings page
     */
    public function settings(Request $request): View
    {
        $this->checkPermission('ratehawk_settings');

        $settings = RateHawkSetting::all()->keyBy('key');
        
        $data = [
            'page_title' => __('RateHawk Settings'),
            'settings' => $settings,
            'supported_currencies' => config('ratehawk.supported_currencies'),
            'supported_languages' => config('ratehawk.supported_languages'),
            'breadcrumbs' => [
                ['name' => __('Dashboard'), 'url' => route('admin.index')],
                ['name' => __('RateHawk'), 'url' => route('ratehawk.admin.index')],
                ['name' => __('Settings'), 'class' => 'active'],
            ],
        ];

        return view('RateHawk::admin.settings', $data);
    }

    /**
     * Save settings
     */
    public function saveSettings(Request $request): RedirectResponse
    {
        $this->checkPermission('ratehawk_settings');

        $request->validate([
            'ratehawk_enable' => 'nullable|boolean',
            'ratehawk_key_id' => 'nullable|string|max:255',
            'ratehawk_api_key' => 'nullable|string|max:255',
            'ratehawk_environment' => 'nullable|in:test,production',
            'ratehawk_default_currency' => 'nullable|string|size:3',
            'ratehawk_default_language' => 'nullable|string|size:2',
            'ratehawk_default_residency' => 'nullable|string|size:2',
            'ratehawk_cache_enabled' => 'nullable|boolean',
            'ratehawk_logging_enabled' => 'nullable|boolean',
            'ratehawk_webhooks_enabled' => 'nullable|boolean',
            'ratehawk_webhook_secret' => 'nullable|string|max:255',
        ]);

        try {
            $settings = $request->only([
                'ratehawk_enable',
                'ratehawk_key_id',
                'ratehawk_api_key',
                'ratehawk_environment',
                'ratehawk_default_currency',
                'ratehawk_default_language',
                'ratehawk_default_residency',
                'ratehawk_cache_enabled',
                'ratehawk_logging_enabled',
                'ratehawk_webhooks_enabled',
                'ratehawk_webhook_secret',
            ]);

            foreach ($settings as $key => $value) {
                if ($value !== null) {
                    $setting = RateHawkSetting::where('key', $key)->first();
                    if ($setting) {
                        $setting->value = $value;
                        $setting->save();
                    } else {
                        RateHawkSetting::create([
                            'key' => $key,
                            'value' => $value,
                            'type' => in_array($key, ['ratehawk_enable', 'ratehawk_cache_enabled', 'ratehawk_logging_enabled', 'ratehawk_webhooks_enabled']) ? 'boolean' : 'string',
                            'is_encrypted' => in_array($key, ['ratehawk_key_id', 'ratehawk_api_key', 'ratehawk_webhook_secret']),
                        ]);
                    }
                }
            }

            // Test connection if API credentials are provided
            if ($request->filled(['ratehawk_key_id', 'ratehawk_api_key'])) {
                $testResult = $this->testApiConnection();
                if (!$testResult['success']) {
                    return redirect()->back()
                        ->with('warning', __('Settings saved but API connection test failed: ') . $testResult['message']);
                }
            }

            return redirect()->back()->with('success', __('Settings saved successfully'));

        } catch (\Exception $e) {
            \Log::error('Failed to save RateHawk settings: ' . $e->getMessage());
            return redirect()->back()
                ->with('error', __('Failed to save settings: ') . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request)
    {
        $this->checkPermission('ratehawk_settings');

        try {
            $result = $this->testApiConnection();
            
            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'message' => __('API connection successful'),
                    'data' => $result['data']
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Connection test failed: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get dashboard statistics
     */
    protected function getDashboardStats(): array
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();

        return [
            'total_bookings' => RateHawkBooking::count(),
            'bookings_today' => RateHawkBooking::where('created_at', '>=', $today)->count(),
            'bookings_this_month' => RateHawkBooking::where('created_at', '>=', $thisMonth)->count(),
            'confirmed_bookings' => RateHawkBooking::where('status', 'confirmed')->count(),
            'cancelled_bookings' => RateHawkBooking::where('status', 'cancelled')->count(),
            'failed_bookings' => RateHawkBooking::where('status', 'failed')->count(),
            'total_revenue' => RateHawkBooking::where('status', 'confirmed')->sum('total_amount'),
            'api_requests_today' => RateHawkApiLog::where('created_at', '>=', $today)->count(),
            'api_errors_today' => RateHawkApiLog::where('created_at', '>=', $today)->where('status', 'error')->count(),
            'avg_response_time' => RateHawkApiLog::where('created_at', '>=', $today)->avg('duration'),
        ];
    }

    /**
     * Test API connection
     */
    protected function testApiConnection(): array
    {
        try {
            return $this->apiClient->testConnection();
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }
}
