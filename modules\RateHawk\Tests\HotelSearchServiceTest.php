<?php

namespace Modules\RateHawk\Tests;

use Tests\TestCase;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Services\RateHawkApiClient;
use Modules\RateHawk\Services\CacheService;

class HotelSearchServiceTest extends TestCase
{
    protected $searchService;

    public function setUp(): void
    {
        parent::setUp();
        
        // Mock the dependencies
        $apiClient = $this->createMock(RateHawkApiClient::class);
        $cacheService = $this->createMock(CacheService::class);
        
        $this->searchService = new HotelSearchService($apiClient, $cacheService);
    }

    public function test_search_hotels_validates_parameters()
    {
        $searchParams = [
            'destination' => 'Paris',
            'checkin' => '2025-01-15',
            'checkout' => '2025-01-17',
            'adults' => 2,
        ];

        // This should not throw an exception
        $validatedParams = $this->invokeMethod($this->searchService, 'validateSearchParams', [$searchParams]);
        
        $this->assertIsArray($validatedParams);
        $this->assertEquals('Paris', $validatedParams['destination']);
        $this->assertEquals(2, $validatedParams['adults']);
    }

    public function test_search_suggestions_returns_array()
    {
        $input = 'Par';
        
        // Mock the cache service to return null (cache miss)
        $cacheService = $this->createMock(CacheService::class);
        $cacheService->method('getSearchResults')->willReturn(null);
        
        // Mock the API client
        $apiClient = $this->createMock(RateHawkApiClient::class);
        $apiClient->method('searchDestinations')->willReturn([
            'success' => true,
            'data' => [
                ['name' => 'Paris', 'country' => 'France'],
                ['name' => 'Parma', 'country' => 'Italy'],
            ]
        ]);
        
        $searchService = new HotelSearchService($apiClient, $cacheService);
        $result = $searchService->getSearchSuggestions($input);
        
        $this->assertTrue($result['success']);
        $this->assertIsArray($result['data']);
    }

    /**
     * Call protected/private method of a class.
     */
    protected function invokeMethod(&$object, $methodName, array $parameters = [])
    {
        $reflection = new \ReflectionClass(get_class($object));
        $method = $reflection->getMethod($methodName);
        $method->setAccessible(true);

        return $method->invokeArgs($object, $parameters);
    }
}
