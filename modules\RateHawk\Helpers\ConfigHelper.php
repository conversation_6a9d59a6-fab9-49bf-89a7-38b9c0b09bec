<?php

namespace Modules\RateHawk\Helpers;

use Modules\RateHawk\Models\RateHawkSetting;
use Modules\Core\Models\Settings;
use Illuminate\Support\Facades\Cache;

class ConfigHelper
{
    protected static $cacheKey = 'ratehawk_config_cache';
    protected static $cacheTtl = 3600; // 1 hour

    /**
     * Get configuration value with fallback to default
     */
    public static function get(string $key, $default = null)
    {
        // Try to get from core settings first
        $value = setting_item($key);

        if ($value !== null && $value !== '') {
            return $value;
        }

        // Try RateHawk specific settings
        $value = RateHawkSetting::get($key);

        if ($value !== null) {
            return $value;
        }

        // Fallback to config file
        $configKey = 'ratehawk.' . str_replace('ratehawk_', '', $key);
        return config($configKey, $default);
    }

    /**
     * Set configuration value
     */
    public static function set(string $key, $value, string $type = 'string'): void
    {
        // Update in core settings if it exists there
        if (Settings::where('name', $key)->exists()) {
            Settings::where('name', $key)->update(['val' => $value]);
        } else {
            // Otherwise use RateHawk specific settings
            RateHawkSetting::set($key, $value, $type);
        }
        static::clearCache();
    }

    /**
     * Get all RateHawk configuration
     */
    public static function getAll(): array
    {
        return Cache::remember(static::$cacheKey, static::$cacheTtl, function () {
            $settings = RateHawkSetting::getAllSettings();
            
            // Merge with config file defaults
            $configDefaults = config('ratehawk', []);
            
            return array_merge($configDefaults, $settings);
        });
    }

    /**
     * Check if RateHawk is enabled
     */
    public static function isEnabled(): bool
    {
        return (bool) static::get('ratehawk_enable', false);
    }

    /**
     * Get API credentials
     */
    public static function getApiCredentials(): array
    {
        return [
            'key_id' => static::get('ratehawk_key_id', ''),
            'api_key' => static::get('ratehawk_api_key', ''),
            'environment' => static::get('ratehawk_environment', 'test'),
        ];
    }

    /**
     * Check if API credentials are configured
     */
    public static function hasApiCredentials(): bool
    {
        $credentials = static::getApiCredentials();
        return !empty($credentials['key_id']) && !empty($credentials['api_key']);
    }

    /**
     * Get default search parameters
     */
    public static function getDefaultSearchParams(): array
    {
        return [
            'currency' => static::get('ratehawk_default_currency', 'USD'),
            'language' => static::get('ratehawk_default_language', 'en'),
            'residency' => static::get('ratehawk_default_residency', 'us'),
            'hotels_limit' => static::get('ratehawk_default_hotels_limit', 50),
        ];
    }

    /**
     * Get cache configuration
     */
    public static function getCacheConfig(): array
    {
        return [
            'enabled' => (bool) static::get('ratehawk_cache_enabled', true),
            'ttl' => [
                'search_results' => (int) static::get('ratehawk_cache_search_ttl', 300),
                'hotel_data' => (int) static::get('ratehawk_cache_hotel_ttl', 3600),
                'regions' => (int) static::get('ratehawk_cache_regions_ttl', 86400),
            ],
        ];
    }

    /**
     * Get logging configuration
     */
    public static function getLoggingConfig(): array
    {
        return [
            'enabled' => (bool) static::get('ratehawk_logging_enabled', true),
            'level' => static::get('ratehawk_log_level', 'info'),
            'channel' => static::get('ratehawk_log_channel', 'daily'),
        ];
    }

    /**
     * Get webhook configuration
     */
    public static function getWebhookConfig(): array
    {
        return [
            'enabled' => (bool) static::get('ratehawk_webhooks_enabled', true),
            'secret' => static::get('ratehawk_webhook_secret', ''),
            'endpoints' => [
                'booking_status' => '/api/ratehawk/webhook/booking-status',
            ],
        ];
    }

    /**
     * Get rate limiting configuration
     */
    public static function getRateLimitConfig(): array
    {
        return [
            'search' => [
                'requests' => (int) static::get('ratehawk_rate_limit_search_requests', 10),
                'seconds' => (int) static::get('ratehawk_rate_limit_search_seconds', 60),
            ],
            'booking' => [
                'requests' => (int) static::get('ratehawk_rate_limit_booking_requests', 5),
                'seconds' => (int) static::get('ratehawk_rate_limit_booking_seconds', 60),
            ],
            'general' => [
                'requests' => (int) static::get('ratehawk_rate_limit_general_requests', 100),
                'seconds' => (int) static::get('ratehawk_rate_limit_general_seconds', 3600),
            ],
        ];
    }

    /**
     * Get API configuration
     */
    public static function getApiConfig(): array
    {
        return [
            'base_url' => static::get('ratehawk_api_base_url', 'https://api.worldota.net'),
            'version' => static::get('ratehawk_api_version', 'v3'),
            'timeout' => (int) static::get('ratehawk_api_timeout', 30),
        ];
    }

    /**
     * Get supported currencies
     */
    public static function getSupportedCurrencies(): array
    {
        return config('ratehawk.supported_currencies', [
            'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'SEK', 'NZD'
        ]);
    }

    /**
     * Get supported languages
     */
    public static function getSupportedLanguages(): array
    {
        return config('ratehawk.supported_languages', [
            'en' => 'English',
            'es' => 'Spanish',
            'fr' => 'French',
            'de' => 'German',
            'it' => 'Italian',
            'pt' => 'Portuguese',
            'ru' => 'Russian',
            'zh_CN' => 'Simplified Chinese',
        ]);
    }

    /**
     * Validate configuration
     */
    public static function validate(): array
    {
        $errors = [];

        // Check if module is enabled
        if (!static::isEnabled()) {
            $errors[] = 'RateHawk module is disabled';
            return $errors; // Return early if disabled
        }

        // Check API credentials
        if (!static::hasApiCredentials()) {
            $errors[] = 'API credentials are not configured';
        }

        // Validate currency
        $currency = static::get('ratehawk_default_currency');
        if ($currency && !in_array($currency, static::getSupportedCurrencies())) {
            $errors[] = "Unsupported default currency: {$currency}";
        }

        // Validate language
        $language = static::get('ratehawk_default_language');
        $supportedLanguages = array_keys(static::getSupportedLanguages());
        if ($language && !in_array($language, $supportedLanguages)) {
            $errors[] = "Unsupported default language: {$language}";
        }

        // Validate environment
        $environment = static::get('ratehawk_environment');
        if ($environment && !in_array($environment, ['test', 'production'])) {
            $errors[] = "Invalid environment: {$environment}";
        }

        return $errors;
    }

    /**
     * Get configuration status
     */
    public static function getStatus(): array
    {
        $errors = static::validate();
        
        return [
            'is_enabled' => static::isEnabled(),
            'has_credentials' => static::hasApiCredentials(),
            'is_valid' => empty($errors),
            'errors' => $errors,
            'environment' => static::get('ratehawk_environment', 'test'),
            'cache_enabled' => (bool) static::get('ratehawk_cache_enabled', true),
            'logging_enabled' => (bool) static::get('ratehawk_logging_enabled', true),
            'webhooks_enabled' => (bool) static::get('ratehawk_webhooks_enabled', true),
        ];
    }

    /**
     * Clear configuration cache
     */
    public static function clearCache(): void
    {
        Cache::forget(static::$cacheKey);
    }

    /**
     * Refresh configuration cache
     */
    public static function refreshCache(): array
    {
        static::clearCache();
        return static::getAll();
    }

    /**
     * Export configuration
     */
    public static function export(bool $includeSecrets = false): array
    {
        $config = static::getAll();
        
        if (!$includeSecrets) {
            // Remove sensitive data
            $sensitiveKeys = [
                'ratehawk_api_key',
                'ratehawk_key_id',
                'ratehawk_webhook_secret',
            ];
            
            foreach ($sensitiveKeys as $key) {
                if (isset($config[$key])) {
                    $config[$key] = '***HIDDEN***';
                }
            }
        }
        
        return $config;
    }

    /**
     * Import configuration
     */
    public static function import(array $config): array
    {
        $imported = [];
        $errors = [];
        
        foreach ($config as $key => $value) {
            try {
                // Skip hidden values
                if ($value === '***HIDDEN***') {
                    continue;
                }
                
                // Determine type
                $type = 'string';
                if (is_bool($value)) {
                    $type = 'boolean';
                } elseif (is_int($value)) {
                    $type = 'integer';
                } elseif (is_float($value)) {
                    $type = 'float';
                } elseif (is_array($value)) {
                    $type = 'json';
                }
                
                static::set($key, $value, $type);
                $imported[] = $key;
                
            } catch (\Exception $e) {
                $errors[] = "Failed to import {$key}: " . $e->getMessage();
            }
        }
        
        return [
            'imported' => $imported,
            'errors' => $errors,
            'count' => count($imported),
        ];
    }
}
