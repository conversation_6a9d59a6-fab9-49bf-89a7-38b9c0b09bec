<?php

namespace Modules\RateHawk\Admin\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Models\RateHawkBooking;
use Modules\RateHawk\Models\RateHawkApiLog;

class DashboardController extends Controller
{
    /**
     * Get dashboard statistics
     */
    public function stats(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', '30'); // days
            $startDate = now()->subDays($period)->startOfDay();

            $stats = [
                'bookings' => $this->getBookingStats($startDate),
                'api' => $this->getApiStats($startDate),
                'revenue' => $this->getRevenueStats($startDate),
                'trends' => $this->getTrendData($startDate),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'period' => $period,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get dashboard stats: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get recent bookings
     */
    public function recentBookings(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 10);
            
            $bookings = RateHawkBooking::with('user')
                ->orderBy('created_at', 'desc')
                ->limit($limit)
                ->get()
                ->map(function ($booking) {
                    return [
                        'id' => $booking->id,
                        'order_id' => $booking->order_id,
                        'hotel_name' => $booking->hotel_name,
                        'status' => $booking->status,
                        'formatted_status' => $booking->formatted_status,
                        'status_badge_class' => $booking->status_badge_class,
                        'total_amount' => $booking->total_amount,
                        'currency' => $booking->currency,
                        'formatted_total_amount' => $booking->formatted_total_amount,
                        'guest_name' => $booking->primary_guest_name,
                        'checkin_date' => $booking->checkin_date?->format('Y-m-d'),
                        'checkout_date' => $booking->checkout_date?->format('Y-m-d'),
                        'created_at' => $booking->created_at->toISOString(),
                        'user' => $booking->user ? [
                            'id' => $booking->user->id,
                            'name' => $booking->user->name,
                            'email' => $booking->user->email,
                        ] : null,
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => $bookings,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get recent bookings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking statistics
     */
    protected function getBookingStats(\Carbon\Carbon $startDate): array
    {
        $total = RateHawkBooking::where('created_at', '>=', $startDate)->count();
        $confirmed = RateHawkBooking::where('created_at', '>=', $startDate)
            ->where('status', 'confirmed')->count();
        $cancelled = RateHawkBooking::where('created_at', '>=', $startDate)
            ->where('status', 'cancelled')->count();
        $failed = RateHawkBooking::where('created_at', '>=', $startDate)
            ->where('status', 'failed')->count();

        return [
            'total' => $total,
            'confirmed' => $confirmed,
            'cancelled' => $cancelled,
            'failed' => $failed,
            'pending' => $total - $confirmed - $cancelled - $failed,
            'success_rate' => $total > 0 ? round(($confirmed / $total) * 100, 2) : 0,
            'cancellation_rate' => $total > 0 ? round(($cancelled / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Get API statistics
     */
    protected function getApiStats(\Carbon\Carbon $startDate): array
    {
        $total = RateHawkApiLog::where('created_at', '>=', $startDate)->count();
        $successful = RateHawkApiLog::where('created_at', '>=', $startDate)
            ->where('status', 'completed')->count();
        $errors = RateHawkApiLog::where('created_at', '>=', $startDate)
            ->where('status', 'error')->count();

        $avgDuration = RateHawkApiLog::where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->avg('duration');

        return [
            'total_requests' => $total,
            'successful_requests' => $successful,
            'failed_requests' => $errors,
            'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
            'error_rate' => $total > 0 ? round(($errors / $total) * 100, 2) : 0,
            'avg_response_time' => $avgDuration ? round($avgDuration, 3) : 0,
        ];
    }

    /**
     * Get revenue statistics
     */
    protected function getRevenueStats(\Carbon\Carbon $startDate): array
    {
        $totalRevenue = RateHawkBooking::where('created_at', '>=', $startDate)
            ->where('status', 'confirmed')
            ->sum('total_amount');

        $totalCommission = RateHawkBooking::where('created_at', '>=', $startDate)
            ->where('status', 'confirmed')
            ->sum('commission_amount');

        $avgBookingValue = RateHawkBooking::where('created_at', '>=', $startDate)
            ->where('status', 'confirmed')
            ->avg('total_amount');

        return [
            'total_revenue' => round($totalRevenue, 2),
            'total_commission' => round($totalCommission, 2),
            'avg_booking_value' => round($avgBookingValue, 2),
            'currency' => 'USD', // This could be made dynamic
        ];
    }

    /**
     * Get trend data for charts
     */
    protected function getTrendData(\Carbon\Carbon $startDate): array
    {
        $days = [];
        $bookingCounts = [];
        $revenueCounts = [];

        for ($date = $startDate->copy(); $date->lte(now()); $date->addDay()) {
            $dayStart = $date->copy()->startOfDay();
            $dayEnd = $date->copy()->endOfDay();

            $bookingCount = RateHawkBooking::whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $revenue = RateHawkBooking::whereBetween('created_at', [$dayStart, $dayEnd])
                ->where('status', 'confirmed')
                ->sum('total_amount');

            $days[] = $date->format('Y-m-d');
            $bookingCounts[] = $bookingCount;
            $revenueCounts[] = round($revenue, 2);
        }

        return [
            'dates' => $days,
            'bookings' => $bookingCounts,
            'revenue' => $revenueCounts,
        ];
    }
}
