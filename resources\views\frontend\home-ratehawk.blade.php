@extends('Layout::app')

@push('css')
<style>
.ratehawk-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 80px 0;
    color: white;
    position: relative;
    overflow: hidden;
}

.ratehawk-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.search-form-container {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    position: relative;
    z-index: 2;
    margin-top: 40px;
}

.search-form-title {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
    font-size: 28px;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-control {
    height: 50px;
    border-radius: 8px;
    border: 2px solid #e1e5e9;
    padding: 0 15px;
    font-size: 16px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.btn-search {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    height: 50px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    transition: all 0.3s ease;
    width: 100%;
}

.btn-search:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    color: white;
}

.popular-destinations {
    padding: 80px 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    font-size: 32px;
    font-weight: 600;
    color: #333;
}

.destination-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 30px;
    cursor: pointer;
}

.destination-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.destination-card .card-body {
    padding: 20px;
    text-align: center;
}

.destination-card h5 {
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.destination-card .text-muted {
    font-size: 14px;
}

.featured-hotels {
    padding: 80px 0;
}

.hotel-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin-bottom: 30px;
}

.hotel-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.hotel-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.hotel-card .card-body {
    padding: 20px;
}

.hotel-card h5 {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.hotel-rating {
    color: #ffc107;
    margin-bottom: 10px;
}

.hotel-price {
    font-size: 18px;
    font-weight: 600;
    color: #667eea;
}

.autocomplete-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e1e5e9;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.autocomplete-suggestion {
    padding: 12px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f1f1f1;
    transition: background-color 0.2s ease;
}

.autocomplete-suggestion:hover,
.autocomplete-suggestion.active {
    background-color: #f8f9fa;
}

.autocomplete-suggestion:last-child {
    border-bottom: none;
}

.search-loading {
    text-align: center;
    padding: 20px;
    display: none;
}

.error-message {
    color: #dc3545;
    font-size: 14px;
    margin-top: 5px;
    display: none;
}

@media (max-width: 768px) {
    .ratehawk-hero {
        padding: 60px 0;
    }
    
    .search-form-container {
        padding: 20px;
        margin-top: 20px;
    }
    
    .search-form-title {
        font-size: 24px;
    }
}
</style>
@endpush

@section('content')
<!-- Hero Section with Search Form -->
<div class="ratehawk-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="text-center">
                    <h1 class="display-4 font-weight-bold mb-3">{{__('Find Your Perfect Hotel')}}</h1>
                    <p class="lead mb-4">{{__('Search millions of hotels worldwide with our advanced booking system')}}</p>
                    <div class="badge badge-light badge-pill px-3 py-2">
                        <i class="fa fa-bolt text-warning"></i> {{__('Powered by RateHawk API')}}
                        @if(isset($api_status))
                            @if($api_status['connected'])
                                <span class="badge badge-success ml-2">{{__('Connected')}}</span>
                            @else
                                <span class="badge badge-warning ml-2">{{__('Local Mode')}}</span>
                            @endif
                        @endif
                    </div>
                    <div class="mt-3">
                        <a href="{{route('bookings.history')}}" class="btn btn-outline-light btn-sm">
                            <i class="fa fa-calendar"></i> {{__('My Bookings')}}
                        </a>
                    </div>
                </div>
                
                <!-- Search Form -->
                <div class="search-form-container">
                    <h3 class="search-form-title">{{__('Search Hotels')}}</h3>
                    
                    <form id="hotel-search-form">
                        @csrf
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group position-relative">
                                    <label for="destination">{{__('Destination')}}</label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="destination" 
                                           name="destination" 
                                           placeholder="{{__('Where are you going?')}}"
                                           autocomplete="off"
                                           required>
                                    <div class="autocomplete-suggestions" id="destination-suggestions"></div>
                                    <div class="error-message" id="destination-error"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="checkin">{{__('Check-in')}}</label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="checkin" 
                                           name="checkin" 
                                           min="{{date('Y-m-d')}}"
                                           value="{{date('Y-m-d', strtotime('+1 day'))}}"
                                           required>
                                    <div class="error-message" id="checkin-error"></div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="checkout">{{__('Check-out')}}</label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="checkout" 
                                           name="checkout" 
                                           min="{{date('Y-m-d', strtotime('+2 days'))}}"
                                           value="{{date('Y-m-d', strtotime('+2 days'))}}"
                                           required>
                                    <div class="error-message" id="checkout-error"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="adults">{{__('Adults')}}</label>
                                    <select class="form-control" id="adults" name="adults" required>
                                        @for($i = 1; $i <= 10; $i++)
                                            <option value="{{$i}}" {{$i == 2 ? 'selected' : ''}}>{{$i}} {{$i == 1 ? __('Adult') : __('Adults')}}</option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="children">{{__('Children')}}</label>
                                    <select class="form-control" id="children" name="children">
                                        @for($i = 0; $i <= 10; $i++)
                                            <option value="{{$i}}">{{$i}} {{$i == 1 ? __('Child') : __('Children')}}</option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label for="rooms">{{__('Rooms')}}</label>
                                    <select class="form-control" id="rooms" name="rooms">
                                        @for($i = 1; $i <= 9; $i++)
                                            <option value="{{$i}}">{{$i}} {{$i == 1 ? __('Room') : __('Rooms')}}</option>
                                        @endfor
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-search">
                                        <i class="fa fa-search"></i> {{__('Search Hotels')}}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <div class="search-loading">
                        <i class="fa fa-spinner fa-spin"></i> {{__('Searching hotels...')}}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Popular Destinations -->
@if(!empty($popular_destinations))
<div class="popular-destinations">
    <div class="container">
        <h2 class="section-title">{{__('Popular Destinations')}}</h2>
        <div class="row">
            @foreach($popular_destinations as $destination)
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="destination-card" onclick="selectDestination('{{$destination['name']}}')">
                    <div class="card-body">
                        <h5>{{$destination['name']}}</h5>
                        <p class="text-muted">
                            @if(isset($destination['country']))
                                {{$destination['country']}}
                            @endif
                            @if(isset($destination['hotels_count']))
                                <br><small>{{$destination['hotels_count']}} {{__('hotels')}}</small>
                            @endif
                        </p>
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endif

<!-- Featured Hotels -->
@if(!empty($featured_hotels))
<div class="featured-hotels">
    <div class="container">
        <h2 class="section-title">{{__('Featured Hotels')}}</h2>
        <div class="row">
            @foreach($featured_hotels as $hotel)
            <div class="col-lg-3 col-md-6">
                <div class="hotel-card">
                    @if(!empty($hotel['image']))
                        <img src="{{$hotel['image']}}" alt="{{$hotel['name']}}" class="card-img-top">
                    @else
                        <div style="height: 200px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); display: flex; align-items: center; justify-content: center;">
                            <i class="fa fa-hotel fa-3x text-muted"></i>
                        </div>
                    @endif
                    <div class="card-body">
                        <h5>{{$hotel['name']}}</h5>
                        @if(!empty($hotel['location']))
                            <p class="text-muted"><i class="fa fa-map-marker"></i> {{$hotel['location']}}</p>
                        @endif
                        @if(!empty($hotel['rating']))
                            <div class="hotel-rating">
                                @for($i = 1; $i <= 5; $i++)
                                    <i class="fa fa-star{{$i <= $hotel['rating'] ? '' : '-o'}}"></i>
                                @endfor
                            </div>
                        @endif
                        @if(!empty($hotel['price']))
                            <div class="hotel-price">
                                {{$hotel['currency'] ?? 'USD'}} {{number_format($hotel['price'], 2)}}
                                <small class="text-muted">/{{__('night')}}</small>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
    </div>
</div>
@endif

<!-- Search Results Modal -->
<div class="modal fade" id="searchResultsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-xl" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{__('Search Results')}}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="search-results-content">
                    <!-- Results will be loaded here -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hotel Details Modal -->
<div class="modal fade" id="hotelDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="hotelDetailsTitle">{{__('Hotel Details')}}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="hotel-details-content">
                    <!-- Hotel details will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{__('Close')}}</button>
                <button type="button" class="btn btn-primary" id="bookNowBtn">{{__('Book Now')}}</button>
            </div>
        </div>
    </div>
</div>

<!-- Booking Modal -->
<div class="modal fade" id="bookingModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{__('Complete Your Booking')}}</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="booking-form">
                    @csrf
                    <div class="row">
                        <div class="col-md-6">
                            <h6>{{__('Guest Information')}}</h6>
                            <div class="form-group">
                                <label for="guest_name">{{__('Full Name')}}</label>
                                <input type="text" class="form-control" id="guest_name" name="guest_name" required>
                            </div>
                            <div class="form-group">
                                <label for="guest_email">{{__('Email')}}</label>
                                <input type="email" class="form-control" id="guest_email" name="guest_email" required>
                            </div>
                            <div class="form-group">
                                <label for="guest_phone">{{__('Phone')}}</label>
                                <input type="tel" class="form-control" id="guest_phone" name="guest_phone" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>{{__('Booking Summary')}}</h6>
                            <div id="booking-summary">
                                <!-- Booking summary will be populated here -->
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>{{__('Special Requests')}}</h6>
                            <div class="form-group">
                                <textarea class="form-control" id="special_requests" name="special_requests" rows="3" placeholder="{{__('Any special requests or notes...')}}"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">{{__('Cancel')}}</button>
                <button type="button" class="btn btn-success" id="confirmBookingBtn">
                    <i class="fa fa-credit-card"></i> {{__('Confirm Booking')}}
                </button>
            </div>
        </div>
    </div>
</div>

@if(isset($error_message))
<div class="container mt-4">
    <div class="alert alert-warning">
        <i class="fa fa-exclamation-triangle"></i> {{$error_message}}
    </div>
</div>
@endif
@endsection

@push('js')
<script>
$(document).ready(function() {
    let searchTimeout;
    let currentSuggestionIndex = -1;
    
    // Destination autocomplete
    $('#destination').on('input', function() {
        const query = $(this).val();
        
        if (query.length < 2) {
            $('#destination-suggestions').hide();
            return;
        }
        
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(function() {
            searchDestinations(query);
        }, 300);
    });
    
    // Handle keyboard navigation for suggestions
    $('#destination').on('keydown', function(e) {
        const suggestions = $('#destination-suggestions .autocomplete-suggestion');
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            currentSuggestionIndex = Math.min(currentSuggestionIndex + 1, suggestions.length - 1);
            updateSuggestionSelection();
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            currentSuggestionIndex = Math.max(currentSuggestionIndex - 1, -1);
            updateSuggestionSelection();
        } else if (e.key === 'Enter' && currentSuggestionIndex >= 0) {
            e.preventDefault();
            suggestions.eq(currentSuggestionIndex).click();
        } else if (e.key === 'Escape') {
            $('#destination-suggestions').hide();
            currentSuggestionIndex = -1;
        }
    });
    
    // Hide suggestions when clicking outside
    $(document).on('click', function(e) {
        if (!$(e.target).closest('#destination, #destination-suggestions').length) {
            $('#destination-suggestions').hide();
        }
    });
    
    // Date validation
    $('#checkin').on('change', function() {
        const checkinDate = new Date($(this).val());
        const checkoutDate = new Date($('#checkout').val());
        
        if (checkoutDate <= checkinDate) {
            const newCheckout = new Date(checkinDate);
            newCheckout.setDate(newCheckout.getDate() + 1);
            $('#checkout').val(newCheckout.toISOString().split('T')[0]);
        }
        
        $('#checkout').attr('min', $(this).val());
    });
    
    // Hotel search form submission
    $('#hotel-search-form').on('submit', function(e) {
        e.preventDefault();
        
        if (validateForm()) {
            searchHotels();
        }
    });
});

function searchDestinations(query) {
    $.get('{{route("api.search.destinations")}}', { query: query })
        .done(function(response) {
            if (response.success && response.data.length > 0) {
                showDestinationSuggestions(response.data);
            } else {
                $('#destination-suggestions').hide();
            }
        })
        .fail(function() {
            $('#destination-suggestions').hide();
        });
}

function showDestinationSuggestions(destinations) {
    let html = '';
    destinations.forEach(function(dest, index) {
        html += `
            <div class="autocomplete-suggestion" data-value="${dest.name}" data-index="${index}">
                <strong>${dest.name}</strong>
                ${dest.country ? `<br><small class="text-muted">${dest.country}</small>` : ''}
            </div>
        `;
    });
    
    $('#destination-suggestions').html(html).show();
    currentSuggestionIndex = -1;
    
    // Handle suggestion clicks
    $('.autocomplete-suggestion').on('click', function() {
        const value = $(this).data('value');
        $('#destination').val(value);
        $('#destination-suggestions').hide();
        currentSuggestionIndex = -1;
    });
}

function updateSuggestionSelection() {
    $('.autocomplete-suggestion').removeClass('active');
    if (currentSuggestionIndex >= 0) {
        $('.autocomplete-suggestion').eq(currentSuggestionIndex).addClass('active');
    }
}

function selectDestination(destination) {
    $('#destination').val(destination);
    $('html, body').animate({
        scrollTop: $('.search-form-container').offset().top - 100
    }, 500);
}

function validateForm() {
    let isValid = true;
    
    // Clear previous errors
    $('.error-message').hide();
    
    // Validate destination
    if ($('#destination').val().trim().length < 2) {
        $('#destination-error').text('{{__("Please enter a destination")}}').show();
        isValid = false;
    }
    
    // Validate dates
    const checkin = new Date($('#checkin').val());
    const checkout = new Date($('#checkout').val());
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (checkin < today) {
        $('#checkin-error').text('{{__("Check-in date cannot be in the past")}}').show();
        isValid = false;
    }
    
    if (checkout <= checkin) {
        $('#checkout-error').text('{{__("Check-out date must be after check-in date")}}').show();
        isValid = false;
    }
    
    return isValid;
}

function searchHotels() {
    const formData = {
        destination: $('#destination').val(),
        checkin: $('#checkin').val(),
        checkout: $('#checkout').val(),
        adults: $('#adults').val(),
        children: $('#children').val(),
        rooms: $('#rooms').val(),
        _token: $('meta[name="csrf-token"]').attr('content')
    };
    
    // Show loading
    $('.search-loading').show();
    $('#hotel-search-form').hide();
    
    $.post('{{route("api.search.hotels")}}', formData)
        .done(function(response) {
            if (response.success) {
                showSearchResults(response.data, formData);
            } else {
                showError(response.message || '{{__("Search failed")}}');
            }
        })
        .fail(function(xhr) {
            const response = xhr.responseJSON;
            showError(response?.message || '{{__("Search failed")}}');
        })
        .always(function() {
            $('.search-loading').hide();
            $('#hotel-search-form').show();
        });
}

function showSearchResults(data, searchParams) {
    let html = `
        <div class="search-summary mb-4">
            <h5>{{__('Search Results for')}} "${searchParams.destination}"</h5>
            <p class="text-muted">
                ${searchParams.checkin} - ${searchParams.checkout} •
                ${searchParams.adults} {{__('adults')}} •
                ${searchParams.rooms} {{__('room(s)')}}
                ${data.from_cache ? ' • <span class="badge badge-info">{{__("Cached")}}</span>' : ''}
                ${data.source ? ' • <span class="badge badge-secondary">' + data.source.toUpperCase() + '</span>' : ''}
            </p>
            <p><strong>${data.total || 0} {{__('hotels found')}}</strong></p>
        </div>
    `;

    if (data.hotels && data.hotels.length > 0) {
        html += '<div class="row">';
        data.hotels.forEach(function(hotel, index) {
            html += `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="hotel-card" style="cursor: pointer;" onclick="showHotelDetails(${index}, '${JSON.stringify(hotel).replace(/'/g, "\\'")}', '${JSON.stringify(searchParams).replace(/'/g, "\\'")}')">
                        ${hotel.image ? `<img src="${hotel.image}" alt="${hotel.name}" class="card-img-top">` :
                          '<div style="height: 200px; background: #f0f0f0; display: flex; align-items: center; justify-content: center;"><i class="fa fa-hotel fa-3x text-muted"></i></div>'}
                        <div class="card-body">
                            <h6>${hotel.name}</h6>
                            ${hotel.location ? `<p class="text-muted small"><i class="fa fa-map-marker"></i> ${hotel.location}</p>` : ''}
                            ${hotel.rating ? `<div class="hotel-rating">${'★'.repeat(Math.floor(hotel.rating))}${'☆'.repeat(5-Math.floor(hotel.rating))}</div>` : ''}
                            ${hotel.price ? `<div class="hotel-price">${hotel.currency || 'USD'} ${hotel.price} <small>/{{__('night')}}</small></div>` : ''}
                            ${hotel.description ? `<p class="text-muted small mt-2">${hotel.description.substring(0, 100)}...</p>` : ''}
                            <button class="btn btn-primary btn-sm mt-2" onclick="event.stopPropagation(); showHotelDetails(${index}, '${JSON.stringify(hotel).replace(/'/g, "\\'")}', '${JSON.stringify(searchParams).replace(/'/g, "\\'")}')">
                                {{__('View Details')}}
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });
        html += '</div>';
    } else {
        html += `
            <div class="text-center py-5">
                <i class="fa fa-search fa-3x text-muted mb-3"></i>
                <h5>{{__('No hotels found')}}</h5>
                <p class="text-muted">{{__('Try adjusting your search criteria')}}</p>
            </div>
        `;
    }

    $('#search-results-content').html(html);
    $('#searchResultsModal').modal('show');

    // Store search data globally for booking
    window.currentSearchData = data;
    window.currentSearchParams = searchParams;
}

function showHotelDetails(index, hotelData, searchParams) {
    try {
        const hotel = typeof hotelData === 'string' ? JSON.parse(hotelData) : hotelData;
        const params = typeof searchParams === 'string' ? JSON.parse(searchParams) : searchParams;

        $('#hotelDetailsTitle').text(hotel.name);

        let detailsHtml = `
            <div class="row">
                <div class="col-md-6">
                    ${hotel.image ? `<img src="${hotel.image}" alt="${hotel.name}" class="img-fluid rounded">` :
                      '<div style="height: 300px; background: #f0f0f0; display: flex; align-items: center; justify-content: center; border-radius: 8px;"><i class="fa fa-hotel fa-5x text-muted"></i></div>'}
                </div>
                <div class="col-md-6">
                    <h4>${hotel.name}</h4>
                    ${hotel.location ? `<p class="text-muted"><i class="fa fa-map-marker"></i> ${hotel.location}</p>` : ''}
                    ${hotel.rating ? `<div class="hotel-rating mb-3">${'★'.repeat(Math.floor(hotel.rating))}${'☆'.repeat(5-Math.floor(hotel.rating))} (${hotel.rating}/5)</div>` : ''}
                    ${hotel.description ? `<p>${hotel.description}</p>` : ''}

                    <div class="booking-info mt-4">
                        <h5>{{__('Booking Details')}}</h5>
                        <p><strong>{{__('Check-in')}}:</strong> ${params.checkin}</p>
                        <p><strong>{{__('Check-out')}}:</strong> ${params.checkout}</p>
                        <p><strong>{{__('Guests')}}:</strong> ${params.adults} {{__('adults')}}${params.children > 0 ? ', ' + params.children + ' {{__("children")}}' : ''}</p>
                        <p><strong>{{__('Rooms')}}:</strong> ${params.rooms}</p>
                        ${hotel.price ? `<h4 class="text-primary">${hotel.currency || 'USD'} ${hotel.price} <small>/{{__('night')}}</small></h4>` : ''}
                    </div>
                </div>
            </div>
        `;

        $('#hotel-details-content').html(detailsHtml);
        $('#searchResultsModal').modal('hide');
        $('#hotelDetailsModal').modal('show');

        // Store current hotel for booking
        window.currentHotel = hotel;

    } catch (e) {
        console.error('Error showing hotel details:', e);
        showAlert('danger', '{{__("Error loading hotel details")}}');
    }
}

function showError(message) {
    alert('{{__("Error")}}: ' + message);
}

// Booking functionality
$(document).ready(function() {
    // Book Now button click
    $('#bookNowBtn').on('click', function() {
        if (window.currentHotel && window.currentSearchParams) {
            showBookingForm();
        } else {
            showAlert('danger', '{{__("Please select a hotel first")}}');
        }
    });

    // Confirm booking button click
    $('#confirmBookingBtn').on('click', function() {
        if (validateBookingForm()) {
            processBooking();
        }
    });
});

function showBookingForm() {
    const hotel = window.currentHotel;
    const params = window.currentSearchParams;

    // Calculate total nights and price
    const checkinDate = new Date(params.checkin);
    const checkoutDate = new Date(params.checkout);
    const nights = Math.ceil((checkoutDate - checkinDate) / (1000 * 60 * 60 * 24));
    const totalPrice = hotel.price ? (hotel.price * nights * params.rooms) : 0;

    // Populate booking summary
    let summaryHtml = `
        <div class="card">
            <div class="card-body">
                <h6 class="card-title">${hotel.name}</h6>
                <p class="card-text small text-muted">${hotel.location || ''}</p>
                <hr>
                <div class="row">
                    <div class="col-6"><small>{{__('Check-in')}}:</small></div>
                    <div class="col-6"><small>${params.checkin}</small></div>
                </div>
                <div class="row">
                    <div class="col-6"><small>{{__('Check-out')}}:</small></div>
                    <div class="col-6"><small>${params.checkout}</small></div>
                </div>
                <div class="row">
                    <div class="col-6"><small>{{__('Nights')}}:</small></div>
                    <div class="col-6"><small>${nights}</small></div>
                </div>
                <div class="row">
                    <div class="col-6"><small>{{__('Guests')}}:</small></div>
                    <div class="col-6"><small>${params.adults} {{__('adults')}}${params.children > 0 ? ', ' + params.children + ' {{__("children")}}' : ''}</small></div>
                </div>
                <div class="row">
                    <div class="col-6"><small>{{__('Rooms')}}:</small></div>
                    <div class="col-6"><small>${params.rooms}</small></div>
                </div>
                <hr>
                ${hotel.price ? `
                <div class="row">
                    <div class="col-6"><small>{{__('Price per night')}}:</small></div>
                    <div class="col-6"><small>${hotel.currency || 'USD'} ${hotel.price}</small></div>
                </div>
                <div class="row font-weight-bold">
                    <div class="col-6">{{__('Total Price')}}:</div>
                    <div class="col-6">${hotel.currency || 'USD'} ${totalPrice.toFixed(2)}</div>
                </div>
                ` : '<p class="text-muted">{{__("Price on request")}}</p>'}
            </div>
        </div>
    `;

    $('#booking-summary').html(summaryHtml);
    $('#hotelDetailsModal').modal('hide');
    $('#bookingModal').modal('show');
}

function validateBookingForm() {
    let isValid = true;

    // Clear previous errors
    $('.is-invalid').removeClass('is-invalid');

    // Validate required fields
    const requiredFields = ['guest_name', 'guest_email', 'guest_phone'];
    requiredFields.forEach(function(field) {
        const value = $('#' + field).val().trim();
        if (!value) {
            $('#' + field).addClass('is-invalid');
            isValid = false;
        }
    });

    // Validate email format
    const email = $('#guest_email').val().trim();
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (email && !emailRegex.test(email)) {
        $('#guest_email').addClass('is-invalid');
        isValid = false;
    }

    if (!isValid) {
        showAlert('danger', '{{__("Please fill in all required fields correctly")}}');
    }

    return isValid;
}

function processBooking() {
    const bookingData = {
        hotel: window.currentHotel,
        search_params: window.currentSearchParams,
        guest_info: {
            name: $('#guest_name').val(),
            email: $('#guest_email').val(),
            phone: $('#guest_phone').val(),
            special_requests: $('#special_requests').val()
        },
        _token: $('meta[name="csrf-token"]').attr('content')
    };

    // Show loading state
    const $btn = $('#confirmBookingBtn');
    const originalText = $btn.html();
    $btn.html('<i class="fa fa-spinner fa-spin"></i> {{__("Processing...")}}').prop('disabled', true);

    // Make actual API call
    $.post('{{route("api.booking.process")}}', bookingData)
        .done(function(response) {
            $btn.html(originalText).prop('disabled', false);

            if (response.success) {
                $('#bookingModal').modal('hide');

                // Show success message with booking details
                const successMessage = `
                    {{__("Booking confirmed!")}} <br>
                    <strong>{{__("Reference")}}:</strong> ${response.data.booking_reference}<br>
                    <strong>{{__("Total")}}:</strong> ${response.data.currency} ${response.data.total_price}<br>
                    {{__("Confirmation email sent!")}}
                `;

                showAlert('success', successMessage);

                // Optional: redirect to booking confirmation page
                // window.location.href = '/booking/confirmation/' + response.data.booking_reference;

            } else {
                showAlert('danger', response.message || '{{__("Booking failed")}}');
            }
        })
        .fail(function(xhr) {
            $btn.html(originalText).prop('disabled', false);

            const response = xhr.responseJSON;
            let errorMessage = '{{__("Booking failed")}}';

            if (response && response.errors) {
                // Show validation errors
                const errors = Object.values(response.errors).flat();
                errorMessage = errors.join('<br>');
            } else if (response && response.message) {
                errorMessage = response.message;
            }

            showAlert('danger', errorMessage);
        });
}
</script>
@endpush
