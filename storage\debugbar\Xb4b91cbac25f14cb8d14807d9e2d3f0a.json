{"__meta": {"id": "Xb4b91cbac25f14cb8d14807d9e2d3f0a", "datetime": "2025-07-08 23:18:51", "utime": **********.941003, "method": "GET", "uri": "/admin/module/hotel/edit/11?lang=ja", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.360119, "end": **********.941048, "duration": 1.5809288024902344, "duration_str": "1.58s", "measures": [{"label": "Booting", "start": **********.360119, "relative_start": 0, "end": **********.830218, "relative_end": **********.830218, "duration": 0.47009897232055664, "duration_str": "470ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.830237, "relative_start": 0.47011780738830566, "end": **********.941054, "relative_end": 6.198883056640625e-06, "duration": 1.1108171939849854, "duration_str": "1.11s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 9293640, "peak_usage_str": "9MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 16, "templates": [{"name": "1x Hotel::admin.detail", "param_count": null, "params": [], "start": **********.990793, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Hotel/Views/admin/detail.blade.phpHotel::admin.detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FViews%2Fadmin%2Fdetail.blade.php&line=1", "ajax": false, "filename": "detail.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::admin.detail"}, {"name": "1x admin.message", "param_count": null, "params": [], "start": **********.994232, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/message.blade.phpadmin.message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.message"}, {"name": "1x Language::admin.navigation", "param_count": null, "params": [], "start": **********.995649, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Language/Views/admin/navigation.blade.phpLanguage::admin.navigation", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLanguage%2FViews%2Fadmin%2Fnavigation.blade.php&line=1", "ajax": false, "filename": "navigation.blade.php", "line": "?"}, "render_count": 1, "name_original": "Language::admin.navigation"}, {"name": "1x Hotel::admin.hotel.content", "param_count": null, "params": [], "start": **********.999231, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Hotel/Views/admin/hotel/content.blade.phpHotel::admin.hotel.content", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FViews%2Fadmin%2Fhotel%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::admin.hotel.content"}, {"name": "1x Hotel::admin.hotel.pricing", "param_count": null, "params": [], "start": **********.013879, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Hotel/Views/admin/hotel/pricing.blade.phpHotel::admin.hotel.pricing", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FViews%2Fadmin%2Fhotel%2Fpricing.blade.php&line=1", "ajax": false, "filename": "pricing.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::admin.hotel.pricing"}, {"name": "1x Hotel::admin.hotel.location", "param_count": null, "params": [], "start": **********.015887, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Hotel/Views/admin/hotel/location.blade.phpHotel::admin.hotel.location", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FViews%2Fadmin%2Fhotel%2Flocation.blade.php&line=1", "ajax": false, "filename": "location.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::admin.hotel.location"}, {"name": "1x Hotel::admin.hotel.surrounding", "param_count": null, "params": [], "start": **********.019168, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Hotel/Views/admin/hotel/surrounding.blade.phpHotel::admin.hotel.surrounding", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FViews%2Fadmin%2Fhotel%2Fsurrounding.blade.php&line=1", "ajax": false, "filename": "surrounding.blade.php", "line": "?"}, "render_count": 1, "name_original": "Hotel::admin.hotel.surrounding"}, {"name": "1x Core::admin.seo-meta.seo-meta", "param_count": null, "params": [], "start": **********.022742, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Core/Views/admin/seo-meta/seo-meta.blade.phpCore::admin.seo-meta.seo-meta", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FViews%2Fadmin%2Fseo-meta%2Fseo-meta.blade.php&line=1", "ajax": false, "filename": "seo-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "Core::admin.seo-meta.seo-meta"}, {"name": "1x admin.layouts.app", "param_count": null, "params": [], "start": **********.086854, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/layouts/app.blade.phpadmin.layouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.layouts.app"}, {"name": "1x Layout::admin.app", "param_count": null, "params": [], "start": **********.087811, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/app.blade.phpLayout::admin.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.app"}, {"name": "1x Layout::admin.parts.global-script", "param_count": null, "params": [], "start": **********.090314, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/global-script.blade.phpLayout::admin.parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.global-script"}, {"name": "1x Layout::admin.parts.header", "param_count": null, "params": [], "start": **********.130508, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.phpLayout::admin.parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.header"}, {"name": "1x Layout::admin.parts.sidebar", "param_count": null, "params": [], "start": **********.153407, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.phpLayout::admin.parts.sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.sidebar"}, {"name": "1x Layout::admin.parts.bc", "param_count": null, "params": [], "start": **********.918259, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/bc.blade.phpLayout::admin.parts.bc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fadmin%2Fparts%2Fbc.blade.php&line=1", "ajax": false, "filename": "bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::admin.parts.bc"}, {"name": "1x Media::browser", "param_count": null, "params": [], "start": **********.920497, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Media/Views/browser.blade.phpMedia::browser", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FViews%2Fbrowser.blade.php&line=1", "ajax": false, "filename": "browser.blade.php", "line": "?"}, "render_count": 1, "name_original": "Media::browser"}, {"name": "1x Ai::frontend.text-generate", "param_count": null, "params": [], "start": **********.925805, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules\\Ai/Views/frontend/text-generate.blade.phpAi::frontend.text-generate", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FAi%2FViews%2Ffrontend%2Ftext-generate.blade.php&line=1", "ajax": false, "filename": "text-generate.blade.php", "line": "?"}, "render_count": 1, "name_original": "Ai::frontend.text-generate"}]}, "route": {"uri": "GET admin/module/hotel/edit/{id}", "middleware": "web, dashboard", "controller": "Modules\\Hotel\\Admin\\HotelController@edit", "namespace": "Modules\\Hotel\\Admin", "prefix": "admin/module/hotel", "where": [], "as": "hotel.admin.edit", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FAdmin%2FHotelController.php&line=152\" onclick=\"\">modules/Hotel/Admin/HotelController.php:152-186</a>"}, "queries": {"nb_statements": 75, "nb_failed_statements": 0, "accumulated_duration": 0.08221999999999997, "accumulated_duration_str": "82.22ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.8764348, "duration": 0.03254, "duration_str": "32.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.916197, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'dashboard_access')", "type": "query", "params": [], "bindings": ["1", "dashboard_access"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.92594, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_update')", "type": "query", "params": [], "bindings": ["1", "hotel_update"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.938085, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_hotels` where `bravo_hotels`.`id` = '11' and `bravo_hotels`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 155}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.945463, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "HotelController.php:155", "source": "modules/Hotel/Admin/HotelController.php:155", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FAdmin%2FHotelController.php&line=155", "ajax": false, "filename": "HotelController.php", "line": "155"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_hotel_translations` where (`origin_id` = 11 and `locale` = 'ja') and `bravo_hotel_translations`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11", "ja"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 56}, {"index": 17, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.95117, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:56", "source": "app/Traits/HasTranslations.php:56", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=56", "ajax": false, "filename": "HasTranslations.php", "line": "56"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_manage_others')", "type": "query", "params": [], "bindings": ["1", "hotel_manage_others"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.958899, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_hotel_term` where `bravo_hotel_term`.`target_id` = 11 and `bravo_hotel_term`.`target_id` is not null", "type": "query", "params": [], "bindings": ["11"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 168}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 45}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.966718, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "HotelController.php:168", "source": "modules/Hotel/Admin/HotelController.php:168", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FAdmin%2FHotelController.php&line=168", "ajax": false, "filename": "HotelController.php", "line": "168"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_attrs` where `service` = 'hotel' and `bravo_attrs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["hotel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 169}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.97201, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "HotelController.php:169", "source": "modules/Hotel/Admin/HotelController.php:169", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FAdmin%2FHotelController.php&line=169", "ajax": false, "filename": "HotelController.php", "line": "169"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `status` = 'publish' and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 170}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9774039, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "HotelController.php:170", "source": "modules/Hotel/Admin/HotelController.php:170", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FAdmin%2FHotelController.php&line=170", "ajax": false, "filename": "HotelController.php", "line": "170"}, "connection": "mazar_travel"}, {"sql": "select * from `location_category` where `status` = 'publish' and `location_category`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 171}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "modules/Hotel/Admin/HotelController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Hotel\\Admin\\HotelController.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9844968, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "HotelController.php:171", "source": "modules/Hotel/Admin/HotelController.php:171", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FAdmin%2FHotelController.php&line=171", "ajax": false, "filename": "HotelController.php", "line": "171"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_seo` where `object_id` = 11 and `object_model` = 'hotel_translation_ja' limit 1", "type": "query", "params": [], "bindings": ["11", "hotel_translation_ja"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 199}, {"index": 17, "namespace": "view", "name": "Core::admin.seo-meta.seo-meta", "file": "C:\\wamp64\\www\\mazar\\modules/Core/Views/admin/seo-meta/seo-meta.blade.php", "line": 5}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.024534, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:199", "source": "app/BaseModel.php:199", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=199", "ajax": false, "filename": "BaseModel.php", "line": "199"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_favicon' limit 1", "type": "query", "params": [], "bindings": ["site_favicon"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.035358, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '228' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["228"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": "view", "name": "Core::admin.seo-meta.seo-meta", "file": "C:\\wamp64\\www\\mazar\\modules/Core/Views/admin/seo-meta/seo-meta.blade.php", "line": 23}], "start": **********.046371, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_title_ja' limit 1", "type": "query", "params": [], "bindings": ["site_title_ja"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 48}], "start": **********.056959, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_provider' limit 1", "type": "query", "params": [], "bindings": ["map_provider"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.07092, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'map_gmap_key' limit 1", "type": "query", "params": [], "bindings": ["map_gmap_key"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.080664, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'date_format' limit 1", "type": "query", "params": [], "bindings": ["date_format"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.0959408, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'site_first_day_of_the_weekin_calendar' limit 1", "type": "query", "params": [], "bindings": ["site_first_day_of_the_weekin_calendar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.108325, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.1323192, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": **********.136083, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 131}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1424851, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 7 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["7", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Layout::admin.parts.header", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/header.blade.php", "line": 132}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.148007, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_disable' limit 1", "type": "query", "params": [], "bindings": ["car_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.1581311, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'flight_disable' limit 1", "type": "query", "params": [], "bindings": ["flight_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.168032, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'boat_disable' limit 1", "type": "query", "params": [], "bindings": ["boat_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.1784391, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'coupon_disable' limit 1", "type": "query", "params": [], "bindings": ["coupon_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.187849, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'location_disable' limit 1", "type": "query", "params": [], "bindings": ["location_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.197156, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'popup_disable' limit 1", "type": "query", "params": [], "bindings": ["popup_disable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.206121, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_news` where `status` = 'pending' and `core_news`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/News/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\News\\ModuleProvider.php", "line": 31}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.290402, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:31", "source": "modules/News/ModuleProvider.php:31", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FNews%2FModuleProvider.php&line=31", "ajax": false, "filename": "ModuleProvider.php", "line": "31"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `users` where `verify_submit_status` in ('new', 'partial') and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["new", "partial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 407}, {"index": 17, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 47}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.296024, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "User.php:407", "source": "app/User.php:407", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=407", "ajax": false, "filename": "User.php", "line": "407"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `user_upgrade_request` where `status` = 'pending' and exists (select * from `users` where `user_upgrade_request`.`user_id` = `users`.`id` and `users`.`deleted_at` is null) and `user_upgrade_request`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["pending"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 48}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.3002422, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:48", "source": "modules/User/ModuleProvider.php:48", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=48", "ajax": false, "filename": "ModuleProvider.php", "line": "48"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'user_disable_verification_feature' limit 1", "type": "query", "params": [], "bindings": ["user_disable_verification_feature"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.3075519, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `bravo_booking_payments` where `object_model` = 'plan' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["plan", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\ModuleProvider.php", "line": 93}, {"index": 19, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 21, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.315108, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ModuleProvider.php:93", "source": "modules/User/ModuleProvider.php:93", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModuleProvider.php&line=93", "ajax": false, "filename": "ModuleProvider.php", "line": "93"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_booking_payments` where `object_model` = 'wallet_deposit' and `status` = 'processing'", "type": "query", "params": [], "bindings": ["wallet_deposit", "processing"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Wallet/DepositPayment.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Wallet\\DepositPayment.php", "line": 15}, {"index": 17, "namespace": null, "name": "modules/Report/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Report\\ModuleProvider.php", "line": 18}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.319796, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "DepositPayment.php:15", "source": "modules/User/Models/Wallet/DepositPayment.php:15", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FWallet%2FDepositPayment.php&line=15", "ajax": false, "filename": "DepositPayment.php", "line": "15"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `bravo_payouts` where `status` = 'initial'", "type": "query", "params": [], "bindings": ["initial"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Vendor/Models/VendorPayout.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\Models\\VendorPayout.php", "line": 63}, {"index": 17, "namespace": null, "name": "modules/Vendor/ModuleProvider.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Vendor\\ModuleProvider.php", "line": 27}, {"index": 20, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 210}, {"index": 21, "namespace": null, "name": "modules/Core/Helpers/AdminMenuManager.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Helpers\\AdminMenuManager.php", "line": 221}, {"index": 22, "namespace": "view", "name": "Layout::admin.parts.sidebar", "file": "C:\\wamp64\\www\\mazar\\modules/Layout/admin/parts/sidebar.blade.php", "line": 2}], "start": **********.324344, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "VendorPayout.php:63", "source": "modules/Vendor/Models/VendorPayout.php:63", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FVendor%2FModels%2FVendorPayout.php&line=63", "ajax": false, "filename": "VendorPayout.php", "line": "63"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'setting_update')", "type": "query", "params": [], "bindings": ["1", "setting_update"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.341128, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'menu_view')", "type": "query", "params": [], "bindings": ["1", "menu_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.3537169, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'language_manage')", "type": "query", "params": [], "bindings": ["1", "language_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.365107, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'language_translation')", "type": "query", "params": [], "bindings": ["1", "language_translation"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.374667, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_view')", "type": "query", "params": [], "bindings": ["1", "hotel_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.386983, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_create')", "type": "query", "params": [], "bindings": ["1", "hotel_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.3989348, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'hotel_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "hotel_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.408045, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_view')", "type": "query", "params": [], "bindings": ["1", "space_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.419631, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_create')", "type": "query", "params": [], "bindings": ["1", "space_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.429648, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'space_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "space_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.4382899, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_view')", "type": "query", "params": [], "bindings": ["1", "car_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.449667, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_create')", "type": "query", "params": [], "bindings": ["1", "car_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.4593349, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'car_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "car_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.4677799, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_view')", "type": "query", "params": [], "bindings": ["1", "event_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.478567, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_create')", "type": "query", "params": [], "bindings": ["1", "event_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.4884052, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'event_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "event_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.4977798, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_view')", "type": "query", "params": [], "bindings": ["1", "tour_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.50808, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_create')", "type": "query", "params": [], "bindings": ["1", "tour_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.5198982, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_manage_others')", "type": "query", "params": [], "bindings": ["1", "tour_manage_others"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.528733, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'tour_manage_attributes')", "type": "query", "params": [], "bindings": ["1", "tour_manage_attributes"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.53773, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'media_upload')", "type": "query", "params": [], "bindings": ["1", "media_upload"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.548996, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_view')", "type": "query", "params": [], "bindings": ["1", "news_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.5580008, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'news_create')", "type": "query", "params": [], "bindings": ["1", "news_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.56826, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'page_view')", "type": "query", "params": [], "bindings": ["1", "page_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.579545, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_view')", "type": "query", "params": [], "bindings": ["1", "user_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.588717, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'role_view')", "type": "query", "params": [], "bindings": ["1", "role_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.5983, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'newsletter_manage')", "type": "query", "params": [], "bindings": ["1", "newsletter_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.610878, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'report_view')", "type": "query", "params": [], "bindings": ["1", "report_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.622751, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'contact_manage')", "type": "query", "params": [], "bindings": ["1", "contact_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.6347659, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'user_create')", "type": "query", "params": [], "bindings": ["1", "user_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.644722, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'coupon_view')", "type": "query", "params": [], "bindings": ["1", "coupon_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.6537728, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'location_view')", "type": "query", "params": [], "bindings": ["1", "location_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.6627572, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'review_manage_others')", "type": "query", "params": [], "bindings": ["1", "review_manage_others"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.6730769, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'popup_view')", "type": "query", "params": [], "bindings": ["1", "popup_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.6828659, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_view')", "type": "query", "params": [], "bindings": ["1", "support_ticket_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.692733, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_ticket_manage')", "type": "query", "params": [], "bindings": ["1", "support_ticket_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.7029438, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_view')", "type": "query", "params": [], "bindings": ["1", "support_topic_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.713094, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'support_topic_create')", "type": "query", "params": [], "bindings": ["1", "support_topic_create"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.7232099, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'theme_manage')", "type": "query", "params": [], "bindings": ["1", "theme_manage"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.733648, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Hotel\\Models\\HotelTerm": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotelTerm.php&line=1", "ajax": false, "filename": "HotelTerm.php", "line": "?"}}, "Modules\\Location\\Models\\Location": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "Modules\\Core\\Models\\Settings": {"value": 7, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Modules\\Core\\Models\\Attributes": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FAttributes.php&line=1", "ajax": false, "filename": "Attributes.php", "line": "?"}}, "Modules\\Location\\Models\\LocationCategory": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FModels%2FLocationCategory.php&line=1", "ajax": false, "filename": "LocationCategory.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Modules\\Hotel\\Models\\Hotel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FHotel%2FModels%2FHotel.php&line=1", "ajax": false, "filename": "Hotel.php", "line": "?"}}, "Modules\\Media\\Models\\MediaFile": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FModels%2FMediaFile.php&line=1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}}, "count": 48, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9tt7BH5AKCIvze70KlIokFcuPXFBUARM1pl66H7S", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/admin/module/hotel/edit/11?lang=ja\"\n]", "bc_current_currency": "", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/module/hotel/edit/11", "status_code": "<pre class=sf-dump id=sf-dump-1486725023 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1486725023\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1183526872 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>lang</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ja</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1183526872\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-927815037 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-927815037\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1114091048 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">http://127.0.0.1:8000/admin/module/hotel/edit/11?lang=egy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2130 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InFSNUR1VnpQazg2MXpEWVByQS8vYUE9PSIsInZhbHVlIjoiWkEzRXdQdm9UWmlndjVhRXE2UGk1N3dzeVptYjZnNkYrbWdZU2tUTFpkZVVucVNKZGRKQmpnWldBKzVIcFE3LzBVUm1EVjVCUmhYYlZ3bjlmRlJVWGFhOGhoUC93ekVkelJNcU1xMGFSSHNVY2xSczc1a2NMMzE2cFdUWUhWbHAiLCJtYWMiOiIwYzc2NTdlMzg4YWQ1OGM5ODFiNDMxMTg2YjA1NGQxZjJiZWU3NWU5ZjJhZjEwZmQ2M2IxZjdjZmEyY2NmNGM2IiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IlUrOERrS1I0SjFFa1BxMWhnbG94aHc9PSIsInZhbHVlIjoiVkY5eTlRZm5kQlBYTnZTVlVFUGR6OHBZV1RCMlNOUW56VlhTQ1FZbEV4OGw5RzdrRk5SVTdzdXh5bVNsd3FNcnhIWkl3eGQxN3JMMXRYOUpmcjZtZ1R0cXJFY1hwTmNZdmVpeDR1TC9GeGZOZlNIc1RkZzF3NTMrSG5VcVI5RmEiLCJtYWMiOiJlNmZhNjEzOTRmOTFjMGE5NjU2YzNkODU3N2UxOGYxZTEyMTMwMThmNWY1Zjk4NTkwNDg1MWU5MzE2MzM4ZDgxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1114091048\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9tt7BH5AKCIvze70KlIokFcuPXFBUARM1pl66H7S</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">N6xCdy0XrkH2rKkL3IT4TF4Xk2kWO7Xx58oUPTNX</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 08 Jul 2025 23:18:50 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Im1pN2FmZnhRWFIxRDZGcHZZZmFUNmc9PSIsInZhbHVlIjoicGVyLy8yQ1Z4OFl0MEhFdWFMc3pKU1dDalUrV0hIZ1FhZ3FxNE5EVWZtQXd5MHIvOTdSODlSNTcvZzNvZFNxRE9DMjBKZkt5MmJaOHI4RTA1dXRDUjUwRzVDL29DVXcxV3lVajZwNkMvTCt5TVpQQWpJZkdHZVhDSDg1RWtjL3oiLCJtYWMiOiI5ZjYxZWE4NWJiODgxNDYyMjZmNTMzMzIxZTI5ODhhZDg1Njc1MWMyZTNmODZhMDUwMmZmNjgwMjYxMzg4OWNlIiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 01:18:51 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IjEvenczWkQ5ZHZ5ZW5TUldVVElOREE9PSIsInZhbHVlIjoiSFFaVWV5MVZCOVQ3bmZkZEUvMFk0QXJ5VFNKeC9OT0ZFNktZYWxxakkrSk5CRXc2cVlreHRzbkpabmVwMFdEaEN5NmVYVUM0UVNoUlFtcERueVViM3FrVDBjdVQ4cUI4YWdEUy9UU1NuNVU2ZEFtSXlnUC9uRmczYlo2NW8xYXMiLCJtYWMiOiI0NzIxYTlkYTQyM2Q0YjExOGU3MjA3M2M1MDA3NGFhYTM4NDg0YzNhZmMzMDJjNGFkNGUzM2ZjMzRhY2IxYTVlIiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 01:18:51 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Im1pN2FmZnhRWFIxRDZGcHZZZmFUNmc9PSIsInZhbHVlIjoicGVyLy8yQ1Z4OFl0MEhFdWFMc3pKU1dDalUrV0hIZ1FhZ3FxNE5EVWZtQXd5MHIvOTdSODlSNTcvZzNvZFNxRE9DMjBKZkt5MmJaOHI4RTA1dXRDUjUwRzVDL29DVXcxV3lVajZwNkMvTCt5TVpQQWpJZkdHZVhDSDg1RWtjL3oiLCJtYWMiOiI5ZjYxZWE4NWJiODgxNDYyMjZmNTMzMzIxZTI5ODhhZDg1Njc1MWMyZTNmODZhMDUwMmZmNjgwMjYxMzg4OWNlIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 01:18:51 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IjEvenczWkQ5ZHZ5ZW5TUldVVElOREE9PSIsInZhbHVlIjoiSFFaVWV5MVZCOVQ3bmZkZEUvMFk0QXJ5VFNKeC9OT0ZFNktZYWxxakkrSk5CRXc2cVlreHRzbkpabmVwMFdEaEN5NmVYVUM0UVNoUlFtcERueVViM3FrVDBjdVQ4cUI4YWdEUy9UU1NuNVU2ZEFtSXlnUC9uRmczYlo2NW8xYXMiLCJtYWMiOiI0NzIxYTlkYTQyM2Q0YjExOGU3MjA3M2M1MDA3NGFhYTM4NDg0YzNhZmMzMDJjNGFkNGUzM2ZjMzRhY2IxYTVlIiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 01:18:51 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-27156695 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9tt7BH5AKCIvze70KlIokFcuPXFBUARM1pl66H7S</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"56 characters\">http://127.0.0.1:8000/admin/module/hotel/edit/11?lang=ja</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-27156695\", {\"maxDepth\":0})</script>\n"}}