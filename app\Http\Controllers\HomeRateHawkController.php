<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Services\CacheService;
use Modules\Location\Models\Location;
use Modules\Hotel\Models\Hotel;
use Illuminate\Support\Facades\Log;

class HomeRateHawkController extends Controller
{
    protected $searchService;
    protected $cacheService;

    public function __construct()
    {
        // Initialize services with fallback handling
        try {
            $this->searchService = app(HotelSearchService::class);
            $this->cacheService = app(CacheService::class);
        } catch (\Exception $e) {
            Log::warning('RateHawk services not available: ' . $e->getMessage());
            $this->searchService = null;
            $this->cacheService = null;
        }
    }

    /**
     * Show the RateHawk home page
     */
    public function index()
    {
        try {
            // Get popular destinations for the search form
            $popularDestinations = $this->getPopularDestinations();

            // Get featured hotels (mix of local and cached RateHawk hotels)
            $featuredHotels = $this->getFeaturedHotels();

            // Get recent searches for suggestions
            $recentSearches = $this->getRecentSearches();

            // Check API status
            $apiStatus = $this->checkApiStatus();

            $seo_meta = [
                'title' => (setting_item('site_title') ?? 'Mazar Travel') . ' - ' . __('Find Your Perfect Hotel'),
                'description' => __('Search and book hotels worldwide with our advanced booking system powered by RateHawk API'),
                'keywords' => __('hotel booking, travel, accommodation, RateHawk, worldwide hotels'),
                'full_url' => url('/home2'),
                'is_homepage' => false,
            ];

            $data = [
                'popular_destinations' => $popularDestinations,
                'featured_hotels' => $featuredHotels,
                'recent_searches' => $recentSearches,
                'api_status' => $apiStatus,
                'seo_meta' => $seo_meta,
                'is_home' => true,
                'page_title' => __('Hotel Search - Powered by RateHawk'),
            ];

            return view('frontend.home-ratehawk', $data);

        } catch (\Exception $e) {
            Log::error('RateHawk home page error: ' . $e->getMessage());

            // Fallback to basic data
            $data = [
                'popular_destinations' => $this->getLocalPopularDestinations(),
                'featured_hotels' => $this->getLocalFeaturedHotels(),
                'recent_searches' => [],
                'api_status' => ['connected' => false, 'message' => 'API unavailable'],
                'seo_meta' => [
                    'title' => 'Mazar Travel',
                    'description' => __('Hotel booking platform'),
                    'full_url' => url('/home2'),
                ],
                'is_home' => true,
                'page_title' => __('Hotel Search'),
                'error_message' => config('app.debug') ? $e->getMessage() : __('Service temporarily unavailable'),
            ];

            return view('frontend.home-ratehawk', $data);
        }
    }

    /**
     * AJAX search for destinations (autocomplete)
     */
    public function searchDestinations(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query', '');

            if (strlen($query) < 2) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            // Try RateHawk API first
            if ($this->searchService) {
                try {
                    $result = $this->searchService->searchDestinations($query, 10);

                    if ($result['success'] && !empty($result['data'])) {
                        return response()->json([
                            'success' => true,
                            'data' => $result['data'],
                            'source' => 'ratehawk'
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::warning('RateHawk destination search failed: ' . $e->getMessage());
                }
            }

            // Fallback to local destinations
            $localDestinations = $this->searchLocalDestinations($query);

            return response()->json([
                'success' => true,
                'data' => $localDestinations,
                'source' => 'local'
            ]);

        } catch (\Exception $e) {
            Log::error('Destination search error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Search failed'),
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * AJAX hotel search
     */
    public function searchHotels(Request $request): JsonResponse
    {
        try {
            $searchParams = $request->validate([
                'destination' => 'required|string|min:2',
                'checkin' => 'required|date|after_or_equal:today',
                'checkout' => 'required|date|after:checkin',
                'adults' => 'required|integer|min:1|max:10',
                'children' => 'nullable|integer|min:0|max:10',
                'rooms' => 'nullable|integer|min:1|max:9',
            ]);

            // Try RateHawk API first
            if ($this->searchService) {
                try {
                    $result = $this->searchService->searchHotels($searchParams);

                    if ($result['success'] && !empty($result['data'])) {
                        return response()->json([
                            'success' => true,
                            'data' => $result['data'],
                            'search_params' => $searchParams,
                            'source' => 'ratehawk'
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::warning('RateHawk hotel search failed: ' . $e->getMessage());
                }
            }

            // Fallback to local hotels or sample data
            $fallbackHotels = $this->getLocalHotelsForDestination($searchParams['destination']);

            if (empty($fallbackHotels)) {
                // Generate sample data if no local hotels found
                $fallbackHotels = [
                    [
                        'name' => 'Sample Hotel ' . $searchParams['destination'],
                        'location' => $searchParams['destination'],
                        'rating' => 4,
                        'price' => 120,
                        'currency' => 'USD',
                        'image' => null,
                        'description' => 'Comfortable accommodation in ' . $searchParams['destination']
                    ],
                    [
                        'name' => 'Grand Hotel ' . $searchParams['destination'],
                        'location' => $searchParams['destination'],
                        'rating' => 5,
                        'price' => 200,
                        'currency' => 'USD',
                        'image' => null,
                        'description' => 'Luxury hotel in the heart of ' . $searchParams['destination']
                    ]
                ];
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'hotels' => $fallbackHotels,
                    'total' => count($fallbackHotels),
                    'from_cache' => false
                ],
                'search_params' => $searchParams,
                'source' => 'local'
            ]);

        } catch (\Exception $e) {
            Log::error('Hotel search error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Search failed'),
                'error' => config('app.debug') ? $e->getMessage() : null,
                'data' => [
                    'hotels' => [],
                    'total' => 0
                ]
            ], 500);
        }
    }

    /**
     * Get search suggestions
     */
    public function getSearchSuggestions(Request $request): JsonResponse
    {
        try {
            $input = $request->input('input', '');

            if (strlen($input) < 1) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            // Return sample suggestions for now
            $suggestions = [
                'Paris, France',
                'London, UK',
                'New York, USA',
                'Tokyo, Japan',
                'Dubai, UAE'
            ];

            $filtered = array_filter($suggestions, function($suggestion) use ($input) {
                return stripos($suggestion, $input) !== false;
            });

            return response()->json([
                'success' => true,
                'data' => array_values($filtered)
            ]);

        } catch (\Exception $e) {
            Log::error('Search suggestions error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to get suggestions'),
                'data' => []
            ], 500);
        }
    }

    // Private helper methods

    private function checkApiStatus(): array
    {
        if (!$this->searchService) {
            return ['connected' => false, 'message' => 'Service not initialized'];
        }

        try {
            // Try a simple API call to check connectivity
            $result = $this->searchService->testConnection();
            return [
                'connected' => $result['success'] ?? false,
                'message' => $result['message'] ?? 'Unknown status'
            ];
        } catch (\Exception $e) {
            return ['connected' => false, 'message' => $e->getMessage()];
        }
    }

    private function getPopularDestinations(): array
    {
        try {
            if ($this->searchService) {
                $result = $this->searchService->getPopularDestinations(8);

                if ($result['success'] && !empty($result['data'])) {
                    return $result['data'];
                }
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get popular destinations: ' . $e->getMessage());
        }

        // Fallback to local destinations
        return $this->getLocalPopularDestinations();
    }

    private function getFeaturedHotels(): array
    {
        try {
            // Try to get featured hotels from RateHawk cache first
            if ($this->cacheService) {
                try {
                    $cachedHotels = $this->cacheService->get('featured_hotels');
                    if (!empty($cachedHotels)) {
                        return $cachedHotels;
                    }
                } catch (\Exception $e) {
                    Log::warning('Failed to get cached featured hotels: ' . $e->getMessage());
                }
            }

            // Get local featured hotels
            $localHotels = $this->getLocalFeaturedHotels();

            // Cache the result if cache service is available
            if ($this->cacheService && !empty($localHotels)) {
                try {
                    $this->cacheService->put('featured_hotels', $localHotels, 60); // Cache for 1 hour
                } catch (\Exception $e) {
                    Log::warning('Failed to cache featured hotels: ' . $e->getMessage());
                }
            }

            return $localHotels;

        } catch (\Exception $e) {
            Log::warning('Failed to get featured hotels: ' . $e->getMessage());
            return $this->getLocalFeaturedHotels();
        }
    }

    private function getLocalFeaturedHotels(): array
    {
        try {
            return Hotel::where('status', 'publish')
                ->where('featured', 1)
                ->limit(4)
                ->get()
                ->map(function ($hotel) {
                    return [
                        'id' => $hotel->id,
                        'name' => $hotel->title,
                        'location' => $hotel->location->name ?? '',
                        'image' => $hotel->image_url,
                        'rating' => $hotel->star_rate ?? 0,
                        'price' => $hotel->price_from ?? 0,
                        'currency' => setting_item('currency_main') ?? 'USD',
                        'source' => 'local'
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            Log::warning('Failed to get local featured hotels: ' . $e->getMessage());
            return [];
        }
    }

    private function getRecentSearches(): array
    {
        try {
            // Get popular search terms from cache
            return [
                ['destination' => 'Paris', 'count' => 150],
                ['destination' => 'London', 'count' => 120],
                ['destination' => 'New York', 'count' => 100],
                ['destination' => 'Tokyo', 'count' => 80],
                ['destination' => 'Dubai', 'count' => 75],
            ];
        } catch (\Exception $e) {
            return [];
        }
    }

    private function searchLocalDestinations(string $query): array
    {
        try {
            return Location::where('name', 'LIKE', "%{$query}%")
                ->where('status', 'publish')
                ->limit(10)
                ->get()
                ->map(function ($location) {
                    return [
                        'name' => $location->name,
                        'type' => 'city',
                        'country' => $location->country->name ?? '',
                        'source' => 'local'
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getLocalPopularDestinations(): array
    {
        try {
            return Location::where('status', 'publish')
                ->withCount('hotels')
                ->orderBy('hotels_count', 'desc')
                ->limit(8)
                ->get()
                ->map(function ($location) {
                    return [
                        'name' => $location->name,
                        'type' => 'city',
                        'country' => $location->country->name ?? '',
                        'hotels_count' => $location->hotels_count,
                        'source' => 'local'
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getLocalHotelsForDestination(string $destination): array
    {
        try {
            return Hotel::where('status', 'publish')
                ->whereHas('location', function ($query) use ($destination) {
                    $query->where('name', 'LIKE', "%{$destination}%");
                })
                ->limit(10)
                ->get()
                ->map(function ($hotel) {
                    return [
                        'id' => $hotel->id,
                        'name' => $hotel->title,
                        'location' => $hotel->location->name ?? '',
                        'image' => $hotel->image_url,
                        'rating' => $hotel->star_rate ?? 0,
                        'price' => $hotel->price_from ?? 0,
                        'currency' => setting_item('currency_main') ?? 'USD',
                        'description' => $hotel->short_desc ?? '',
                        'source' => 'local'
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            Log::warning('Failed to get local hotels for destination: ' . $e->getMessage());
            return [];
        }
    }
}
