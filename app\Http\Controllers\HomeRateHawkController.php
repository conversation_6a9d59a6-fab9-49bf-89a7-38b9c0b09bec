<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\Location\Models\Location;
use Modules\Hotel\Models\Hotel;
use Illuminate\Support\Facades\Log;

class HomeRateHawkController extends Controller
{
    public function __construct()
    {
        // Simple constructor for now
    }

    /**
     * Show the RateHawk home page
     */
    public function index()
    {
        try {
            // Get popular destinations for the search form
            $popularDestinations = $this->getPopularDestinations();

            // Get featured hotels (mix of local and cached RateHawk hotels)
            $featuredHotels = $this->getFeaturedHotels();

            // Get recent searches for suggestions
            $recentSearches = $this->getRecentSearches();

            $seo_meta = [
                'title' => (setting_item('site_title') ?? 'Mazar Travel') . ' - ' . __('Find Your Perfect Hotel'),
                'description' => __('Search and book hotels worldwide with our advanced booking system powered by RateHawk API'),
                'keywords' => __('hotel booking, travel, accommodation, RateHawk, worldwide hotels'),
                'full_url' => url('/home2'),
                'is_homepage' => false,
            ];

            $data = [
                'popular_destinations' => $popularDestinations,
                'featured_hotels' => $featuredHotels,
                'recent_searches' => $recentSearches,
                'seo_meta' => $seo_meta,
                'is_home' => true,
                'page_title' => __('Hotel Search - Powered by RateHawk'),
            ];

            return view('frontend.home-ratehawk', $data);

        } catch (\Exception $e) {
            Log::error('RateHawk home page error: ' . $e->getMessage());

            // Fallback to basic data
            $data = [
                'popular_destinations' => [],
                'featured_hotels' => [],
                'recent_searches' => [],
                'seo_meta' => [
                    'title' => 'Mazar Travel',
                    'description' => __('Hotel booking platform'),
                    'full_url' => url('/home2'),
                ],
                'is_home' => true,
                'page_title' => __('Hotel Search'),
                'error_message' => config('app.debug') ? $e->getMessage() : __('Service temporarily unavailable'),
            ];

            return view('frontend.home-ratehawk', $data);
        }
    }

    /**
     * AJAX search for destinations (autocomplete)
     */
    public function searchDestinations(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query', '');

            if (strlen($query) < 2) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            // For now, use local destinations only
            $localDestinations = $this->searchLocalDestinations($query);

            return response()->json([
                'success' => true,
                'data' => $localDestinations,
                'source' => 'local'
            ]);

        } catch (\Exception $e) {
            Log::error('Destination search error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Search failed'),
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * AJAX hotel search
     */
    public function searchHotels(Request $request): JsonResponse
    {
        try {
            $searchParams = $request->validate([
                'destination' => 'required|string|min:2',
                'checkin' => 'required|date|after_or_equal:today',
                'checkout' => 'required|date|after:checkin',
                'adults' => 'required|integer|min:1|max:10',
                'children' => 'nullable|integer|min:0|max:10',
                'rooms' => 'nullable|integer|min:1|max:9',
            ]);

            // For now, return sample data
            $sampleHotels = [
                [
                    'name' => 'Sample Hotel Paris',
                    'location' => $searchParams['destination'],
                    'rating' => 4,
                    'price' => 120,
                    'currency' => 'USD',
                    'image' => null
                ],
                [
                    'name' => 'Grand Hotel ' . $searchParams['destination'],
                    'location' => $searchParams['destination'],
                    'rating' => 5,
                    'price' => 200,
                    'currency' => 'USD',
                    'image' => null
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => [
                    'hotels' => $sampleHotels,
                    'total' => count($sampleHotels),
                    'from_cache' => false
                ],
                'search_params' => $searchParams
            ]);

        } catch (\Exception $e) {
            Log::error('Hotel search error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Search failed'),
                'error' => config('app.debug') ? $e->getMessage() : null,
                'data' => [
                    'hotels' => [],
                    'total' => 0
                ]
            ], 500);
        }
    }

    /**
     * Get search suggestions
     */
    public function getSearchSuggestions(Request $request): JsonResponse
    {
        try {
            $input = $request->input('input', '');

            if (strlen($input) < 1) {
                return response()->json([
                    'success' => true,
                    'data' => []
                ]);
            }

            // Return sample suggestions for now
            $suggestions = [
                'Paris, France',
                'London, UK',
                'New York, USA',
                'Tokyo, Japan',
                'Dubai, UAE'
            ];

            $filtered = array_filter($suggestions, function($suggestion) use ($input) {
                return stripos($suggestion, $input) !== false;
            });

            return response()->json([
                'success' => true,
                'data' => array_values($filtered)
            ]);

        } catch (\Exception $e) {
            Log::error('Search suggestions error: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => __('Failed to get suggestions'),
                'data' => []
            ], 500);
        }
    }

    // Private helper methods

    private function getPopularDestinations(): array
    {
        try {
            $result = $this->searchService->getPopularDestinations(8);
            
            if ($result['success']) {
                return $result['data'];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to get popular destinations: ' . $e->getMessage());
        }

        // Fallback to local destinations
        return $this->getLocalPopularDestinations();
    }

    private function getFeaturedHotels(): array
    {
        try {
            // Get a mix of local hotels and cached RateHawk hotels
            $localHotels = Hotel::where('status', 'publish')
                ->where('featured', 1)
                ->limit(4)
                ->get()
                ->map(function ($hotel) {
                    return [
                        'id' => $hotel->id,
                        'name' => $hotel->title,
                        'location' => $hotel->location->name ?? '',
                        'image' => $hotel->image_url,
                        'rating' => $hotel->star_rate ?? 0,
                        'price' => $hotel->price_from ?? 0,
                        'currency' => setting_item('currency_main') ?? 'USD',
                        'source' => 'local'
                    ];
                })
                ->toArray();

            return $localHotels;

        } catch (\Exception $e) {
            Log::warning('Failed to get featured hotels: ' . $e->getMessage());
            return [];
        }
    }

    private function getRecentSearches(): array
    {
        try {
            // Get popular search terms from cache
            return [
                ['destination' => 'Paris', 'count' => 150],
                ['destination' => 'London', 'count' => 120],
                ['destination' => 'New York', 'count' => 100],
                ['destination' => 'Tokyo', 'count' => 80],
                ['destination' => 'Dubai', 'count' => 75],
            ];
        } catch (\Exception $e) {
            return [];
        }
    }

    private function searchLocalDestinations(string $query): array
    {
        try {
            return Location::where('name', 'LIKE', "%{$query}%")
                ->where('status', 'publish')
                ->limit(10)
                ->get()
                ->map(function ($location) {
                    return [
                        'name' => $location->name,
                        'type' => 'city',
                        'country' => $location->country->name ?? '',
                        'source' => 'local'
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }

    private function getLocalPopularDestinations(): array
    {
        try {
            return Location::where('status', 'publish')
                ->withCount('hotels')
                ->orderBy('hotels_count', 'desc')
                ->limit(8)
                ->get()
                ->map(function ($location) {
                    return [
                        'name' => $location->name,
                        'type' => 'city',
                        'country' => $location->country->name ?? '',
                        'hotels_count' => $location->hotels_count,
                        'source' => 'local'
                    ];
                })
                ->toArray();
        } catch (\Exception $e) {
            return [];
        }
    }
}
