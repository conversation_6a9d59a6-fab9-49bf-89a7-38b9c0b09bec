<?php

namespace Modules\RateHawk\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Services\StaticContentService;
use Modules\RateHawk\Services\RateHawkApiClient;
use Modules\RateHawk\Exceptions\RateHawkApiException;

class StaticController extends Controller
{
    protected $staticContentService;
    protected $apiClient;

    public function __construct(
        StaticContentService $staticContentService,
        RateHawkApiClient $apiClient
    ) {
        $this->staticContentService = $staticContentService;
        $this->apiClient = $apiClient;
    }

    /**
     * Get regions list
     */
    public function regions(Request $request): JsonResponse
    {
        try {
            $filters = $request->only(['country_code', 'type', 'search']);
            $regions = $this->staticContentService->getRegions($filters);

            return $this->successResponse($regions, 'Regions retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get regions: ' . $e->getMessage());
        }
    }

    /**
     * Get hotels dump
     */
    public function hotelsDump(Request $request): JsonResponse
    {
        try {
            $params = $request->only(['language', 'limit', 'offset']);
            $hotelsDump = $this->staticContentService->getHotelsDump($params);

            return $this->successResponse($hotelsDump, 'Hotels dump retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get hotels dump: ' . $e->getMessage());
        }
    }

    /**
     * Get API endpoints
     */
    public function endpoints(Request $request): JsonResponse
    {
        try {
            $endpoints = $this->apiClient->getEndpoints();

            return $this->successResponse($endpoints, 'API endpoints retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get API endpoints: ' . $e->getMessage());
        }
    }

    /**
     * Search regions
     */
    public function searchRegions(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query');
            $limit = $request->input('limit', 10);

            if (strlen($query) < 2) {
                return $this->errorResponse('Query must be at least 2 characters long', 400);
            }

            $regions = $this->staticContentService->searchRegions($query, $limit);

            return $this->successResponse($regions, 'Region search completed successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to search regions: ' . $e->getMessage());
        }
    }

    /**
     * Get region by ID
     */
    public function getRegion(Request $request, int $regionId): JsonResponse
    {
        try {
            $region = $this->staticContentService->getRegionById($regionId);

            if (!$region) {
                return $this->errorResponse('Region not found', 404);
            }

            return $this->successResponse($region, 'Region retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get region: ' . $e->getMessage());
        }
    }

    /**
     * Clear static content cache
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $this->staticContentService->clearCache();

            return $this->successResponse([], 'Cache cleared successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Return success response
     */
    protected function successResponse(array $data, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Return error response
     */
    protected function errorResponse(
        string $message, 
        int $statusCode = 500, 
        array $errorDetails = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->toISOString()
        ];

        if (!empty($errorDetails)) {
            $response['error_details'] = $errorDetails;
        }

        return response()->json($response, $statusCode);
    }
}
