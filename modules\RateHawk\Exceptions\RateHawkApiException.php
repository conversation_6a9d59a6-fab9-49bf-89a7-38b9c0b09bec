<?php

namespace Modules\RateHawk\Exceptions;

use Exception;
use Throwable;

class RateHawkApiException extends Exception
{
    protected $responseData;
    protected $statusCode;

    public function __construct(
        string $message = '',
        int $statusCode = 0,
        array $responseData = null,
        Throwable $previous = null
    ) {
        parent::__construct($message, $statusCode, $previous);
        
        $this->statusCode = $statusCode;
        $this->responseData = $responseData;
    }

    /**
     * Get the response data from the API
     */
    public function getResponseData(): ?array
    {
        return $this->responseData;
    }

    /**
     * Get the HTTP status code
     */
    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    /**
     * Get error details from response data
     */
    public function getErrorDetails(): array
    {
        if (!$this->responseData) {
            return [];
        }

        return [
            'error' => $this->responseData['error'] ?? null,
            'debug' => $this->responseData['debug'] ?? null,
            'status' => $this->responseData['status'] ?? null,
        ];
    }

    /**
     * Check if this is a validation error
     */
    public function isValidationError(): bool
    {
        return isset($this->responseData['error']) && 
               $this->responseData['error'] === 'invalid_params';
    }

    /**
     * Get validation error details
     */
    public function getValidationErrors(): array
    {
        if (!$this->isValidationError()) {
            return [];
        }

        return $this->responseData['debug']['validation_error'] ?? [];
    }

    /**
     * Convert exception to array for API responses
     */
    public function toArray(): array
    {
        return [
            'error' => true,
            'message' => $this->getMessage(),
            'status_code' => $this->getStatusCode(),
            'error_details' => $this->getErrorDetails(),
            'validation_errors' => $this->getValidationErrors(),
        ];
    }
}
