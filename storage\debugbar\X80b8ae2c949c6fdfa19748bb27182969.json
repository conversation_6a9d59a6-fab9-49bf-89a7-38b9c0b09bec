{"__meta": {"id": "X80b8ae2c949c6fdfa19748bb27182969", "datetime": "2025-07-10 21:37:38", "utime": 1752183458.666264, "method": "GET", "uri": "/car/vinfast-lux-v8-suv", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.005611, "end": 1752183458.666296, "duration": 2.6606850624084473, "duration_str": "2.66s", "measures": [{"label": "Booting", "start": **********.005611, "relative_start": 0, "end": **********.499851, "relative_end": **********.499851, "duration": 0.49424004554748535, "duration_str": "494ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.499918, "relative_start": 0.4943070411682129, "end": 1752183458.6663, "relative_end": 4.0531158447265625e-06, "duration": 2.166382074356079, "duration_str": "2.17s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 8022800, "peak_usage_str": "8MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 33, "templates": [{"name": "1x Car::frontend.detail", "param_count": null, "params": [], "start": **********.793347, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/detail.blade.phpCar::frontend.detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Fdetail.blade.php&line=1", "ajax": false, "filename": "detail.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.detail"}, {"name": "1x Layout::parts.bc", "param_count": null, "params": [], "start": **********.928753, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/bc.blade.phpLayout::parts.bc", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fbc.blade.php&line=1", "ajax": false, "filename": "bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.bc"}, {"name": "1x Car::frontend.layouts.details.banner", "param_count": null, "params": [], "start": **********.98383, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/banner.blade.phpCar::frontend.layouts.details.banner", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fbanner.blade.php&line=1", "ajax": false, "filename": "banner.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.details.banner"}, {"name": "1x Car::frontend.layouts.details.detail", "param_count": null, "params": [], "start": 1752183457.040596, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/detail.blade.phpCar::frontend.layouts.details.detail", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fdetail.blade.php&line=1", "ajax": false, "filename": "detail.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.details.detail"}, {"name": "1x Car::frontend.layouts.details.attributes", "param_count": null, "params": [], "start": 1752183457.335644, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/attributes.blade.phpCar::frontend.layouts.details.attributes", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fattributes.blade.php&line=1", "ajax": false, "filename": "attributes.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.details.attributes"}, {"name": "1x Car::frontend.layouts.details.review", "param_count": null, "params": [], "start": 1752183457.608205, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/review.blade.phpCar::frontend.layouts.details.review", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Freview.blade.php&line=1", "ajax": false, "filename": "review.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.details.review"}, {"name": "1x Review::frontend.form", "param_count": null, "params": [], "start": 1752183457.637971, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.phpReview::frontend.form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FReview%2FViews%2Ffrontend%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Review::frontend.form"}, {"name": "1x pagination::tailwind", "param_count": null, "params": [], "start": 1752183458.000938, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/vendor/pagination/tailwind.blade.phppagination::tailwind", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fvendor%2Fpagination%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}, "render_count": 1, "name_original": "pagination::tailwind"}, {"name": "1x vendor.pagination.default", "param_count": null, "params": [], "start": 1752183458.002007, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/vendor/pagination/default.blade.phpvendor.pagination.default", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fvendor%2Fpagination%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}, "render_count": 1, "name_original": "vendor.pagination.default"}, {"name": "1x admin.message", "param_count": null, "params": [], "start": 1752183458.003738, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/admin/message.blade.phpadmin.message", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fadmin%2Fmessage.blade.php&line=1", "ajax": false, "filename": "message.blade.php", "line": "?"}, "render_count": 1, "name_original": "admin.message"}, {"name": "1x Tour::frontend.layouts.details.vendor", "param_count": null, "params": [], "start": 1752183458.025241, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/details/vendor.blade.phpTour::frontend.layouts.details.vendor", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fvendor.blade.php&line=1", "ajax": false, "filename": "vendor.blade.php", "line": "?"}, "render_count": 1, "name_original": "Tour::frontend.layouts.details.vendor"}, {"name": "1x Car::frontend.layouts.details.form-book", "param_count": null, "params": [], "start": 1752183458.11862, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/form-book.blade.phpCar::frontend.layouts.details.form-book", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fform-book.blade.php&line=1", "ajax": false, "filename": "form-book.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.details.form-book"}, {"name": "1x Booking::frontend.global.enquiry-form", "param_count": null, "params": [], "start": 1752183458.225048, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules\\Booking/Views/frontend/global/enquiry-form.blade.phpBooking::frontend.global.enquiry-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FViews%2Ffrontend%2Fglobal%2Fenquiry-form.blade.php&line=1", "ajax": false, "filename": "enquiry-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Booking::frontend.global.enquiry-form"}, {"name": "1x Car::frontend.layouts.details.related", "param_count": null, "params": [], "start": 1752183458.284259, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/related.blade.phpCar::frontend.layouts.details.related", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Frelated.blade.php&line=1", "ajax": false, "filename": "related.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.details.related"}, {"name": "1x Car::frontend.layouts.details.form-book-mobile", "param_count": null, "params": [], "start": 1752183458.32645, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/form-book-mobile.blade.phpCar::frontend.layouts.details.form-book-mobile", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fform-book-mobile.blade.php&line=1", "ajax": false, "filename": "form-book-mobile.blade.php", "line": "?"}, "render_count": 1, "name_original": "Car::frontend.layouts.details.form-book-mobile"}, {"name": "1x layouts.app", "param_count": null, "params": [], "start": 1752183458.441036, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/layouts/app.blade.phplayouts.app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Flayouts%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app"}, {"name": "1x Layout::app", "param_count": null, "params": [], "start": 1752183458.441973, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/app.blade.phpLayout::app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::app"}, {"name": "1x Layout::parts.favicon", "param_count": null, "params": [], "start": 1752183458.443129, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/favicon.blade.phpLayout::parts.favicon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffavicon.blade.php&line=1", "ajax": false, "filename": "favicon.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.favicon"}, {"name": "1x Layout::parts.seo-meta", "param_count": null, "params": [], "start": 1752183458.446721, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/seo-meta.blade.phpLayout::parts.seo-meta", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fseo-meta.blade.php&line=1", "ajax": false, "filename": "seo-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.seo-meta"}, {"name": "1x Layout::parts.global-script", "param_count": null, "params": [], "start": 1752183458.452615, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/global-script.blade.phpLayout::parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.global-script"}, {"name": "1x Layout::parts.topbar", "param_count": null, "params": [], "start": 1752183458.502264, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/topbar.blade.phpLayout::parts.topbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Ftopbar.blade.php&line=1", "ajax": false, "filename": "topbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.topbar"}, {"name": "2x Core::frontend.currency-switcher", "param_count": null, "params": [], "start": 1752183458.516642, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Core/Views/frontend/currency-switcher.blade.phpCore::frontend.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCore%2FViews%2Ffrontend%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Core::frontend.currency-switcher"}, {"name": "2x Language::frontend.switcher", "param_count": null, "params": [], "start": 1752183458.522415, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Language/Views/frontend/switcher.blade.phpLanguage::frontend.switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLanguage%2FViews%2Ffrontend%2Fswitcher.blade.php&line=1", "ajax": false, "filename": "switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Language::frontend.switcher"}, {"name": "1x Layout::parts.notification", "param_count": null, "params": [], "start": 1752183458.524658, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/notification.blade.phpLayout::parts.notification", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.notification"}, {"name": "1x Layout::parts.header", "param_count": null, "params": [], "start": 1752183458.543224, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/header.blade.phpLayout::parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.header"}, {"name": "1x Layout::parts.footer", "param_count": null, "params": [], "start": 1752183458.619924, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/footer.blade.phpLayout::parts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.footer"}, {"name": "1x Layout::parts.login-register-modal", "param_count": null, "params": [], "start": 1752183458.631338, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/login-register-modal.blade.phpLayout::parts.login-register-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Flogin-register-modal.blade.php&line=1", "ajax": false, "filename": "login-register-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.login-register-modal"}, {"name": "1x Layout::auth.login-form", "param_count": null, "params": [], "start": 1752183458.633669, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/login-form.blade.phpLayout::auth.login-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Flogin-form.blade.php&line=1", "ajax": false, "filename": "login-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.login-form"}, {"name": "1x Layout::auth.register-form", "param_count": null, "params": [], "start": 1752183458.638723, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/register-form.blade.phpLayout::auth.register-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Fregister-form.blade.php&line=1", "ajax": false, "filename": "register-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.register-form"}, {"name": "1x Popup::frontend.popup", "param_count": null, "params": [], "start": 1752183458.642569, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Popup/Views/frontend/popup.blade.phpPopup::frontend.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FPopup%2FViews%2Ffrontend%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "Popup::frontend.popup"}, {"name": "1x demo_script", "param_count": null, "params": [], "start": 1752183458.653809, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/demo_script.blade.phpdemo_script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fdemo_script.blade.php&line=1", "ajax": false, "filename": "demo_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "demo_script"}]}, "route": {"uri": "GET car/{slug}", "middleware": "web", "controller": "Modules\\Car\\Controllers\\CarController@detail", "namespace": "Modules\\Car\\Controllers", "prefix": "/car", "where": [], "as": "car.detail", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FControllers%2FCarController.php&line=101\" onclick=\"\">modules/Car/Controllers/CarController.php:101-136</a>"}, "queries": {"nb_statements": 66, "nb_failed_statements": 0, "accumulated_duration": 0.09099, "accumulated_duration_str": "90.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.545985, "duration": 0.02714, "duration_str": "27.14ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.579452, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_cars` where `slug` = 'vinfast-lux-v8-suv' and `bravo_cars`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["vinfast-lux-v8-suv"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 103}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.588075, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "CarController.php:103", "source": "modules/Car/Controllers/CarController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FControllers%2FCarController.php&line=103", "ajax": false, "filename": "CarController.php", "line": "103"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where `bravo_locations`.`id` in (8) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 103}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.593491, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CarController.php:103", "source": "modules/Car/Controllers/CarController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FControllers%2FCarController.php&line=103", "ajax": false, "filename": "CarController.php", "line": "103"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_location_translations` where `locale` = 'en' and `bravo_location_translations`.`origin_id` in (8)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 103}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.598664, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "CarController.php:103", "source": "modules/Car/Controllers/CarController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FControllers%2FCarController.php&line=103", "ajax": false, "filename": "CarController.php", "line": "103"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_car_translations` where `locale` = 'en' and `bravo_car_translations`.`origin_id` in (11) and `bravo_car_translations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 103}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.604215, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CarController.php:103", "source": "modules/Car/Controllers/CarController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FControllers%2FCarController.php&line=103", "ajax": false, "filename": "CarController.php", "line": "103"}, "connection": "mazar_travel"}, {"sql": "select * from `user_wishlist` where `object_model` = 'car' and `user_id` = 7 and `user_wishlist`.`object_id` in (11)", "type": "query", "params": [], "bindings": ["car", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 103}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.608965, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CarController.php:103", "source": "modules/Car/Controllers/CarController.php:103", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FControllers%2FCarController.php&line=103", "ajax": false, "filename": "CarController.php", "line": "103"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_cars` where `location_id` = 8 and `status` = 'publish' and `id` not in (11) and `bravo_cars`.`deleted_at` is null limit 4", "type": "query", "params": [], "bindings": ["8", "publish", "11"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 111}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.614881, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "CarController.php:111", "source": "modules/Car/Controllers/CarController.php:111", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FControllers%2FCarController.php&line=111", "ajax": false, "filename": "CarController.php", "line": "111"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_review_number_per_page' limit 1", "type": "query", "params": [], "bindings": ["car_review_number_per_page"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.623451, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `bravo_review` where `object_id` = 11 and `object_model` = 'car' and `status` = 'approved' and `bravo_review`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["11", "car", "approved"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Models/Car.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Models\\Car.php", "line": 647}, {"index": 17, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 113}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.630934, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Car.php:647", "source": "modules/Car/Models/Car.php:647", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=647", "ajax": false, "filename": "Car.php", "line": "647"}, "connection": "mazar_travel"}, {"sql": "select `id`, `title`, `content`, `rate_number`, `author_ip`, `status`, `created_at`, `vendor_id`, `author_id` from `bravo_review` where `object_id` = 11 and `object_model` = 'car' and `status` = 'approved' and `bravo_review`.`deleted_at` is null order by `id` desc limit 5 offset 0", "type": "query", "params": [], "bindings": ["11", "car", "approved"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Models/Car.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Models\\Car.php", "line": 647}, {"index": 17, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 113}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.636948, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Car.php:647", "source": "modules/Car/Models/Car.php:647", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=647", "ajax": false, "filename": "Car.php", "line": "647"}, "connection": "mazar_travel"}, {"sql": "select `id`, `name`, `first_name`, `last_name`, `avatar_id` from `users` where `users`.`id` in (8, 11, 14) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": [], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/Car/Models/Car.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Models\\Car.php", "line": 647}, {"index": 22, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 113}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.642788, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Car.php:647", "source": "modules/Car/Models/Car.php:647", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=647", "ajax": false, "filename": "Car.php", "line": "647"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_deposit_enable' limit 1", "type": "query", "params": [], "bindings": ["car_deposit_enable"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.651133, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_deposit_type' limit 1", "type": "query", "params": [], "bindings": ["car_deposit_type"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.659822, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_deposit_amount' limit 1", "type": "query", "params": [], "bindings": ["car_deposit_amount"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.670114, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_deposit_fomular' limit 1", "type": "query", "params": [], "bindings": ["car_deposit_fomular"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.6789808, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'booking_enquiry_for_car' limit 1", "type": "query", "params": [], "bindings": ["booking_enquiry_for_car"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.687953, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'booking_enquiry_type_car' limit 1", "type": "query", "params": [], "bindings": ["booking_enquiry_type_car"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.696291, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_booking_buyer_fees' limit 1", "type": "query", "params": [], "bindings": ["car_booking_buyer_fees"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.769558, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_seo` where `object_id` = 11 and `object_model` = 'car' limit 1", "type": "query", "params": [], "bindings": ["11", "car"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 212}, {"index": 18, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 120}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}], "start": **********.77788, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:199", "source": "app/BaseModel.php:199", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=199", "ajax": false, "filename": "BaseModel.php", "line": "199"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_locations` where (16 between `bravo_locations`.`_lft` and `bravo_locations`.`_rgt` and `bravo_locations`.`id` <> 8) and `bravo_locations`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["16", "8"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/kalnoy/nestedset/src/QueryBuilder.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\kalnoy\\nestedset\\src\\QueryBuilder.php", "line": 165}, {"index": 18, "namespace": null, "name": "modules/Location/Traits/HasLocation.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Location\\Traits\\HasLocation.php", "line": 21}, {"index": 19, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 129}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "modules/Car/Controllers/CarController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Controllers\\CarController.php", "line": 28}], "start": **********.7857628, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "QueryBuilder.php:165", "source": "vendor/kalnoy/nestedset/src/QueryBuilder.php:165", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Fkalnoy%2Fnestedset%2Fsrc%2FQueryBuilder.php&line=165", "ajax": false, "filename": "QueryBuilder.php", "line": "165"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 136 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["136"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.018262, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 11 and `object_model` = 'car' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11", "car", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Models/Car.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Models\\Car.php", "line": 593}, {"index": 22, "namespace": "view", "name": "Car::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/detail.blade.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.0293372, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Car.php:593", "source": "modules/Car/Models/Car.php:593", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=593", "ajax": false, "filename": "Car.php", "line": "593"}, "connection": "mazar_travel"}, {"sql": "select COUNT( CASE WHEN rate_number = 5 THEN rate_number ELSE NULL END ) AS rate_5,\nCOUNT( CASE WHEN rate_number = 4 THEN rate_number ELSE NULL END ) AS rate_4,\nCOUNT( CASE WHEN rate_number = 3 THEN rate_number ELSE NULL END ) AS rate_3,\nCOUNT( CASE WHEN rate_number = 2 THEN rate_number ELSE NULL END ) AS rate_2,\nCOUNT( CASE WHEN rate_number = 1 THEN rate_number ELSE NULL END ) AS rate_1  from `bravo_review` where `object_id` = 11 and `object_model` = 'car' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11", "car", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Models/Car.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Models\\Car.php", "line": 605}, {"index": 22, "namespace": "view", "name": "Car::frontend.detail", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/detail.blade.php", "line": 15}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.034939, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Car.php:605", "source": "modules/Car/Models/Car.php:605", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=605", "ajax": false, "filename": "Car.php", "line": "605"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_enable_review' limit 1", "type": "query", "params": [], "bindings": ["car_enable_review"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752183457.157538, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select  \tCOUNT( id ) AS total_review, COUNT( CASE WHEN rate_number >= 4 THEN 1 ELSE null END )  as total_review_recommend  from `bravo_review` where `object_id` = 11 and `object_model` = 'car' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11", "car", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Booking/Models/Bookable.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Booking\\Models\\Bookable.php", "line": 359}, {"index": 22, "namespace": "view", "name": "Car::frontend.layouts.details.detail", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/detail.blade.php", "line": 31}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.1641939, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "Bookable.php:359", "source": "modules/Booking/Models/Bookable.php:359", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FBooking%2FModels%2FBookable.php&line=359", "ajax": false, "filename": "Bookable.php", "line": "359"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '155' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["155"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.173066, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '156' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["156"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.1927369, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '157' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["157"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.2180529, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '158' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["158"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.240233, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '159' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["159"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.260279, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '160' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["160"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.2846959, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = '161' and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["161"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.30377, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_car_term` where `bravo_car_term`.`target_id` = 11 and `bravo_car_term`.`target_id` is not null", "type": "query", "params": [], "bindings": ["11"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "Car::frontend.layouts.details.attributes", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/attributes.blade.php", "line": 2}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1752183457.474871, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "Car::frontend.layouts.details.attributes:2", "source": "view::Car::frontend.layouts.details.attributes:2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCar%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fattributes.blade.php&line=2", "ajax": false, "filename": "attributes.blade.php", "line": "2"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_terms` where `bravo_terms`.`id` in (61, 63, 65, 68, 69, 70, 71, 72, 73, 74) and `bravo_terms`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "modules/Core/Models/Terms.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Terms.php", "line": 42}, {"index": 18, "namespace": "view", "name": "Car::frontend.layouts.details.attributes", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/attributes.blade.php", "line": 3}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.482101, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Terms.php:42", "source": "modules/Core/Models/Terms.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FTerms.php&line=42", "ajax": false, "filename": "Terms.php", "line": "42"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_terms_translations` where `locale` = 'en' and `bravo_terms_translations`.`origin_id` in (61, 63, 65, 68, 69, 70, 71, 72, 73, 74)", "type": "query", "params": [], "bindings": ["en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "modules/Core/Models/Terms.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Terms.php", "line": 42}, {"index": 23, "namespace": "view", "name": "Car::frontend.layouts.details.attributes", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/attributes.blade.php", "line": 3}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.487296, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Terms.php:42", "source": "modules/Core/Models/Terms.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FTerms.php&line=42", "ajax": false, "filename": "Terms.php", "line": "42"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_attrs` where `bravo_attrs`.`id` in (9, 10) and `bravo_attrs`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["9", "10"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "modules/Core/Models/Terms.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Terms.php", "line": 42}, {"index": 23, "namespace": "view", "name": "Car::frontend.layouts.details.attributes", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/attributes.blade.php", "line": 3}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.491705, "duration": 0.0017900000000000001, "duration_str": "1.79ms", "memory": 0, "memory_str": null, "filename": "Terms.php:42", "source": "modules/Core/Models/Terms.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FTerms.php&line=42", "ajax": false, "filename": "Terms.php", "line": "42"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_attrs_translations` where `bravo_attrs_translations`.`origin_id` = 9 and `bravo_attrs_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["9", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "Car::frontend.layouts.details.attributes", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/attributes.blade.php", "line": 7}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.5008109, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_attrs_translations` where `bravo_attrs_translations`.`origin_id` = 10 and `bravo_attrs_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["10", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": "view", "name": "Car::frontend.layouts.details.attributes", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/attributes.blade.php", "line": 7}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.507182, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 137 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["137"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.5181491, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 138 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["138"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.533171, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 142 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["142"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.5490239, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 139 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["139"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.5649939, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 140 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["140"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.580328, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `media_files` where `media_files`.`id` = 141 and `media_files`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["141"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 135}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 23, "namespace": null, "name": "app/BaseModel.php", "file": "C:\\wamp64\\www\\mazar\\app\\BaseModel.php", "line": 134}, {"index": 24, "namespace": null, "name": "modules/Media/Helpers/FileHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Media\\Helpers\\FileHelper.php", "line": 42}], "start": 1752183457.599087, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "BaseModel.php:135", "source": "app/BaseModel.php:135", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FBaseModel.php&line=135", "ajax": false, "filename": "BaseModel.php", "line": "135"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_review_meta` where `review_id` = 166 and `name` = 'upload_picture' limit 1", "type": "query", "params": [], "bindings": ["166", "upload_picture"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Review/Models/Review.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Review\\Models\\Review.php", "line": 66}, {"index": 17, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.9162621, "duration": 0.00575, "duration_str": "5.75ms", "memory": 0, "memory_str": null, "filename": "Review.php:66", "source": "modules/Review/Models/Review.php:66", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FReview%2FModels%2FReview.php&line=66", "ajax": false, "filename": "Review.php", "line": "66"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` is null and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752183457.925591, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_review_meta` where `review_id` = 165 and `name` = 'upload_picture' limit 1", "type": "query", "params": [], "bindings": ["165", "upload_picture"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Review/Models/Review.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Review\\Models\\Review.php", "line": 66}, {"index": 17, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.93151, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "Review.php:66", "source": "modules/Review/Models/Review.php:66", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FReview%2FModels%2FReview.php&line=66", "ajax": false, "filename": "Review.php", "line": "66"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` is null and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752183457.9373, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_review_meta` where `review_id` = 164 and `name` = 'upload_picture' limit 1", "type": "query", "params": [], "bindings": ["164", "upload_picture"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Review/Models/Review.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Review\\Models\\Review.php", "line": 66}, {"index": 17, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.942282, "duration": 0.0015300000000000001, "duration_str": "1.53ms", "memory": 0, "memory_str": null, "filename": "Review.php:66", "source": "modules/Review/Models/Review.php:66", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FReview%2FModels%2FReview.php&line=66", "ajax": false, "filename": "Review.php", "line": "66"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` is null and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752183457.9473648, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_review_meta` where `review_id` = 163 and `name` = 'upload_picture' limit 1", "type": "query", "params": [], "bindings": ["163", "upload_picture"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Review/Models/Review.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Review\\Models\\Review.php", "line": 66}, {"index": 17, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.9530041, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "Review.php:66", "source": "modules/Review/Models/Review.php:66", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FReview%2FModels%2FReview.php&line=66", "ajax": false, "filename": "Review.php", "line": "66"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` is null and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752183457.957977, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `bravo_review_meta` where `review_id` = 162 and `name` = 'upload_picture' limit 1", "type": "query", "params": [], "bindings": ["162", "upload_picture"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Review/Models/Review.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Review\\Models\\Review.php", "line": 66}, {"index": 17, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183457.9628, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "Review.php:66", "source": "modules/Review/Models/Review.php:66", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FReview%2FModels%2FReview.php&line=66", "ajax": false, "filename": "Review.php", "line": "66"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` is null and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Review::frontend.form", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Review/Views/frontend/form.blade.php", "line": 60}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752183457.968758, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_review_stats' limit 1", "type": "query", "params": [], "bindings": ["car_review_stats"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752183458.009035, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'review_upload_picture' limit 1", "type": "query", "params": [], "bindings": ["review_upload_picture"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752183458.01846, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `users` where `users`.`id` = 4 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["4"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": "view", "name": "Tour::frontend.layouts.details.vendor", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/details/vendor.blade.php", "line": 2}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1752183458.1083, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Tour::frontend.layouts.details.vendor:2", "source": "view::Tour::frontend.layouts.details.vendor:2", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FTour%2FViews%2Ffrontend%2Flayouts%2Fdetails%2Fvendor.blade.php&line=2", "ajax": false, "filename": "vendor.blade.php", "line": "2"}, "connection": "mazar_travel"}, {"sql": "select * from `user_meta` where (`user_id` = 4 and `name` = 'social_meta_avatar') limit 1", "type": "query", "params": [], "bindings": ["4", "social_meta_avatar"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 100}, {"index": 15, "namespace": null, "name": "app/User.php", "file": "C:\\wamp64\\www\\mazar\\app\\User.php", "line": 199}, {"index": 16, "namespace": "view", "name": "Tour::frontend.layouts.details.vendor", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Tour/Views/frontend/layouts/details/vendor.blade.php", "line": 8}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": 1752183458.11237, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "User.php:100", "source": "app/User.php:100", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=100", "ajax": false, "filename": "User.php", "line": "100"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'booking_enquiry_enable_recaptcha' limit 1", "type": "query", "params": [], "bindings": ["booking_enquiry_enable_recaptcha"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752183458.276556, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select  AVG(rate_number) as score_total , COUNT(id) as total_review  from `bravo_review` where `object_id` = 11 and `object_model` = 'car' and `status` = 'approved' and `bravo_review`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["11", "car", "approved"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Car/Models/Car.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Models\\Car.php", "line": 630}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Car/Models/Car.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Car\\Models\\Car.php", "line": 629}, {"index": 21, "namespace": "view", "name": "Car::frontend.layouts.details.form-book-mobile", "file": "C:\\wamp64\\www\\mazar\\themes/BC/Car/Views/frontend/layouts/details/form-book-mobile.blade.php", "line": 16}], "start": 1752183458.420089, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Car.php:630", "source": "modules/Car/Models/Car.php:630", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=630", "ajax": false, "filename": "Car.php", "line": "630"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'car_icon_marker_map' limit 1", "type": "query", "params": [], "bindings": ["car_icon_marker_map"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752183458.433182, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) order by `created_at` desc limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1213}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1752183458.5263698, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1213", "source": "app/Helpers/AppHelper.php:1213", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1213", "ajax": false, "filename": "AppHelper.php", "line": "1213"}, "connection": "mazar_travel"}, {"sql": "select count(*) as aggregate from `notifications` where (`for_admin` = 1 or `notifiable_id` = 7) and `read_at` is null limit 5", "type": "query", "params": [], "bindings": ["1", "7"], "hints": ["<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 1214}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 207}], "start": 1752183458.530601, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "AppHelper.php:1214", "source": "app/Helpers/AppHelper.php:1214", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHelpers%2FAppHelper.php&line=1214", "ajax": false, "filename": "AppHelper.php", "line": "1214"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183458.550567, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752183458.590295, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Media\\Models\\MediaFile": {"value": 14, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FMedia%2FModels%2FMediaFile.php&line=1", "ajax": false, "filename": "MediaFile.php", "line": "?"}}, "Modules\\Car\\Models\\CarTerm": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCarTerm.php&line=1", "ajax": false, "filename": "CarTerm.php", "line": "?"}}, "Modules\\Core\\Models\\Terms": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FTerms.php&line=1", "ajax": false, "filename": "Terms.php", "line": "?"}}, "Modules\\Review\\Models\\Review": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FReview%2FModels%2FReview.php&line=1", "ajax": false, "filename": "Review.php", "line": "?"}}, "Modules\\Core\\Models\\Settings": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Modules\\Core\\Models\\Attributes": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FAttributes.php&line=1", "ajax": false, "filename": "Attributes.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Modules\\Car\\Models\\Car": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCar%2FModels%2FCar.php&line=1", "ajax": false, "filename": "Car.php", "line": "?"}}, "Modules\\Location\\Models\\Location": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 55, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/car/vinfast-lux-v8-suv\"\n]"}, "request": {"path_info": "/car/vinfast-lux-v8-suv", "status_code": "<pre class=sf-dump id=sf-dump-758050432 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-758050432\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-186821583 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-186821583\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1594575309 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1594575309\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1721654535 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2130 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik9GYVg4b28vS2ZWcEU5NUl5VTNVY1E9PSIsInZhbHVlIjoiclJ0UFRMbTlqelRyWWlqQUZaNGI4Ry9SNHI0ZUNkZUxISy9KTDkvUGJEcHNHTzNSUmZoQ0lMYXJ3TG5renhGdUo3Wk8ySFBKb1RRNGcrYlhkL3ZUaWdueVlaWkhqY0l1MFZSNW9UM3ZycmFPQW0veEdsL0lTVVFUNWpKZUF2ckkiLCJtYWMiOiI4MTMyMTliZjhiOGQxYWViYjM5YTlkMGFmNDk0ODVhNGVmMWEyOTg0NzUyNzczOTdlNzk1ZDZlZDQ0NGEyNWJlIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6Imw2N2JJMVROU2YvQ2RoczY5RWVzRHc9PSIsInZhbHVlIjoibVBsdWg5K1VHQk5yZ0JEWTVJM3FCb2NteWdiVjJFZjFIU24ra1ZnNk12WTg0WXowcWlPbklNcEIwV242TG05VWNYeDE3WGlGOXpOVWlFaHJiOTBVYjZqSUpaZy9jSm94WWcxeFJPZThLMkNIbU0wdmV0S0FvclJpeHJEMy8zOXgiLCJtYWMiOiIwOGVmMjMxYjJkZjZmODlhZjE4NzU3OWQyYTQwZGNlZmZkZjJlZWE3ZDBhM2FjYTdmYmYxMzNkYjRmNjBjYWYwIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1721654535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2039253043 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yb4TGjWKGpYF4OBLsQqAoVj4lnjPcZCXwUEApQbS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2039253043\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 21:37:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imp3SDZtK1dDL0RJZlB2VWtqZDErWGc9PSIsInZhbHVlIjoiUmVhYTBPR3hrUXZBaWs0SmRUalhrcnpHaURSN3BTWGZXWVk3ZHVOV1VSQzlzN1pPQ3VSWlNYNnpWYVhpOS8wL1ZxVnRUSlNOOWlXMGJQeUdVTFpaSU9CTXRwSEZuaHkyWlVYUjFkb0VmYTVNUGN3eHcvUnNnTStuM0V1eUZOMGwiLCJtYWMiOiJiNzJhNmQxZDMxMDhmZmQ1OTNlOWMzMTRmNWQ5Y2Y5YTYwZGYxZDBiM2FmNjg3NGJlZmJmMWJmN2I0MjJmMTRkIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:37:38 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6InJldFFsSXZjMGNYOTlLbmhxL2NsM1E9PSIsInZhbHVlIjoiaGtqdkdJMkJuZkIzZjZQRVNPUDcwY3M4OGtROFhycjJuanVtRkJqa0g2azFsMG9WV1dvN2hzSldNRm5rcHdWZ0JsV3J6eUViWFV4TDZXZnNxcmJhR2ZEbTZkRG90K1l4SGdHMlNJNzQwR1hSMnFjZVQ3Q3JCYWE0RnNCYldIUUciLCJtYWMiOiJhODQ1ZmNhYzA1NjVlM2Y3MWJiYTAzN2QyYWVkNmRhZGRhOGQxZWM0ODgyZmYwZTA2MTg1OWVlMDkxOGMyODEyIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:37:38 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imp3SDZtK1dDL0RJZlB2VWtqZDErWGc9PSIsInZhbHVlIjoiUmVhYTBPR3hrUXZBaWs0SmRUalhrcnpHaURSN3BTWGZXWVk3ZHVOV1VSQzlzN1pPQ3VSWlNYNnpWYVhpOS8wL1ZxVnRUSlNOOWlXMGJQeUdVTFpaSU9CTXRwSEZuaHkyWlVYUjFkb0VmYTVNUGN3eHcvUnNnTStuM0V1eUZOMGwiLCJtYWMiOiJiNzJhNmQxZDMxMDhmZmQ1OTNlOWMzMTRmNWQ5Y2Y5YTYwZGYxZDBiM2FmNjg3NGJlZmJmMWJmN2I0MjJmMTRkIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:37:38 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6InJldFFsSXZjMGNYOTlLbmhxL2NsM1E9PSIsInZhbHVlIjoiaGtqdkdJMkJuZkIzZjZQRVNPUDcwY3M4OGtROFhycjJuanVtRkJqa0g2azFsMG9WV1dvN2hzSldNRm5rcHdWZ0JsV3J6eUViWFV4TDZXZnNxcmJhR2ZEbTZkRG90K1l4SGdHMlNJNzQwR1hSMnFjZVQ3Q3JCYWE0RnNCYldIUUciLCJtYWMiOiJhODQ1ZmNhYzA1NjVlM2Y3MWJiYTAzN2QyYWVkNmRhZGRhOGQxZWM0ODgyZmYwZTA2MTg1OWVlMDkxOGMyODEyIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:37:38 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1096260884 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">http://127.0.0.1:8000/car/vinfast-lux-v8-suv</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1096260884\", {\"maxDepth\":0})</script>\n"}}