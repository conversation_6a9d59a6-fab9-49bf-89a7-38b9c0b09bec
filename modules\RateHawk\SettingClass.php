<?php

namespace Modules\RateHawk;

use Modules\Core\Abstracts\BaseSettingsClass;

class SettingClass extends BaseSettingsClass
{
    public static function getSettingPages()
    {
        return [
            [
                'id' => 'ratehawk',
                'title' => __('RateHawk API'),
                'position' => 60,
                'view' => 'RateHawk::admin.settings.index',
                'keys' => [
                    'ratehawk_enable',
                    'ratehawk_key_id',
                    'ratehawk_api_key',
                    'ratehawk_environment',
                    'ratehawk_default_currency',
                    'ratehawk_default_language',
                    'ratehawk_default_residency',
                    'ratehawk_cache_enabled',
                    'ratehawk_logging_enabled',
                    'ratehawk_webhooks_enabled',
                    'ratehawk_webhook_secret',
                ],
            ]
        ];
    }

    public static function getValidationRules()
    {
        return [
            'ratehawk_enable' => 'nullable',
            'ratehawk_key_id' => 'nullable|string|max:255',
            'ratehawk_api_key' => 'nullable|string|max:255',
            'ratehawk_environment' => 'nullable|in:test,production',
            'ratehawk_default_currency' => 'nullable|string|size:3',
            'ratehawk_default_language' => 'nullable|string|size:2',
            'ratehawk_default_residency' => 'nullable|string|size:2',
            'ratehawk_cache_enabled' => 'nullable',
            'ratehawk_logging_enabled' => 'nullable',
            'ratehawk_webhooks_enabled' => 'nullable',
            'ratehawk_webhook_secret' => 'nullable|string|max:255',
        ];
    }

    public static function getSettingsName()
    {
        return __('RateHawk API Settings');
    }
}
