<?php

namespace Modules\RateHawk;

use Modules\Core\Abstracts\BaseSettingsClass;

class SettingClass extends BaseSettingsClass
{
    public static function getSettingPages()
    {
        return [
            [
                'id' => 'ratehawk',
                'title' => __('RateHawk API'),
                'position' => 85,
                'view' => 'RateHawk::admin.settings.ratehawk',
                'keys' => [
                    'ratehawk_enable',
                    'ratehawk_environment',
                    'ratehawk_key_id',
                    'ratehawk_api_key',
                    'ratehawk_default_currency',
                    'ratehawk_default_language',
                    'ratehawk_default_residency',
                    'ratehawk_cache_enabled',
                    'ratehawk_logging_enabled',
                    'ratehawk_webhooks_enabled',
                    'ratehawk_webhook_secret',
                    'ratehawk_api_timeout',
                    'ratehawk_hotels_limit',
                    'ratehawk_cache_search_ttl',
                    'ratehawk_cache_hotel_ttl',
                    'ratehawk_cache_regions_ttl',
                ],
                'filter_demo_mode' => [
                    'ratehawk_key_id',
                    'ratehawk_api_key',
                    'ratehawk_webhook_secret',
                ]
            ]
        ];
    }

    public static function getValidationRules()
    {
        return [
            'ratehawk_enable' => 'nullable|in:0,1',
            'ratehawk_environment' => 'nullable|in:test,production',
            'ratehawk_key_id' => 'nullable|string|max:255',
            'ratehawk_api_key' => 'nullable|string|max:255',
            'ratehawk_default_currency' => 'nullable|string|size:3',
            'ratehawk_default_language' => 'nullable|string|size:2',
            'ratehawk_default_residency' => 'nullable|string|size:2',
            'ratehawk_cache_enabled' => 'nullable|in:0,1',
            'ratehawk_logging_enabled' => 'nullable|in:0,1',
            'ratehawk_webhooks_enabled' => 'nullable|in:0,1',
            'ratehawk_webhook_secret' => 'nullable|string|max:255',
            'ratehawk_api_timeout' => 'nullable|integer|min:5|max:120',
            'ratehawk_hotels_limit' => 'nullable|integer|min:10|max:200',
            'ratehawk_cache_search_ttl' => 'nullable|integer|min:60|max:3600',
            'ratehawk_cache_hotel_ttl' => 'nullable|integer|min:300|max:86400',
            'ratehawk_cache_regions_ttl' => 'nullable|integer|min:3600|max:604800',
        ];
    }

    public static function getSettingsName()
    {
        return __('RateHawk API Settings');
    }

    public static function getSettingDefaults()
    {
        return [
            'ratehawk_enable' => '0',
            'ratehawk_environment' => 'test',
            'ratehawk_default_currency' => 'USD',
            'ratehawk_default_language' => 'en',
            'ratehawk_default_residency' => 'us',
            'ratehawk_cache_enabled' => '1',
            'ratehawk_logging_enabled' => '1',
            'ratehawk_webhooks_enabled' => '1',
            'ratehawk_api_timeout' => '30',
            'ratehawk_hotels_limit' => '50',
            'ratehawk_cache_search_ttl' => '300',
            'ratehawk_cache_hotel_ttl' => '3600',
            'ratehawk_cache_regions_ttl' => '86400',
        ];
    }
}
