<?php

return [
    // API Configuration
    'api' => [
        'base_url' => env('RATEHAWK_API_BASE_URL', 'https://api.worldota.net'),
        'version' => env('RATEHAWK_API_VERSION', 'v3'),
        'timeout' => env('RATEHAWK_API_TIMEOUT', 30),
    ],

    // Authentication
    'auth' => [
        'key_id' => env('RATEHAWK_KEY_ID', ''),
        'api_key' => env('RATEHAWK_API_KEY', ''),
        'environment' => env('RATEHAWK_ENVIRONMENT', 'test'), // test or production
    ],

    // Default settings
    'defaults' => [
        'currency' => env('RATEHAWK_DEFAULT_CURRENCY', 'USD'),
        'language' => env('RATEHAWK_DEFAULT_LANGUAGE', 'en'),
        'residency' => env('RATEHAWK_DEFAULT_RESIDENCY', 'us'),
        'hotels_limit' => env('RATEHAWK_DEFAULT_HOTELS_LIMIT', 50),
    ],

    // Rate limiting
    'rate_limits' => [
        'search' => [
            'requests' => 10,
            'seconds' => 60,
        ],
        'booking' => [
            'requests' => 5,
            'seconds' => 60,
        ],
        'general' => [
            'requests' => 100,
            'seconds' => 3600,
        ],
    ],

    // Caching
    'cache' => [
        'enabled' => env('RATEHAWK_CACHE_ENABLED', true),
        'ttl' => [
            'search_results' => env('RATEHAWK_CACHE_SEARCH_TTL', 300), // 5 minutes
            'hotel_data' => env('RATEHAWK_CACHE_HOTEL_TTL', 3600), // 1 hour
            'regions' => env('RATEHAWK_CACHE_REGIONS_TTL', 86400), // 24 hours
        ],
    ],

    // Logging
    'logging' => [
        'enabled' => env('RATEHAWK_LOGGING_ENABLED', true),
        'level' => env('RATEHAWK_LOG_LEVEL', 'info'),
        'channel' => env('RATEHAWK_LOG_CHANNEL', 'daily'),
    ],

    // Webhook settings
    'webhooks' => [
        'enabled' => env('RATEHAWK_WEBHOOKS_ENABLED', true),
        'secret' => env('RATEHAWK_WEBHOOK_SECRET', ''),
        'endpoints' => [
            'booking_status' => '/api/ratehawk/webhook/booking-status',
        ],
    ],

    // Test hotel for sandbox
    'test_hotel' => [
        'hid' => '8473727',
        'id' => 'test_hotel_do_not_book',
    ],

    // Supported currencies
    'supported_currencies' => [
        'USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'SEK', 'NZD',
        'MXN', 'SGD', 'HKD', 'NOK', 'TRY', 'RUB', 'INR', 'BRL', 'ZAR', 'KRW'
    ],

    // Supported languages
    'supported_languages' => [
        'ar' => 'Arabic',
        'bg' => 'Bulgarian',
        'cs' => 'Czech',
        'da' => 'Danish',
        'de' => 'German',
        'el' => 'Greek',
        'en' => 'English',
        'es' => 'Spanish',
        'fi' => 'Finnish',
        'fr' => 'French',
        'he' => 'Hebrew',
        'hu' => 'Hungarian',
        'it' => 'Italian',
        'ja' => 'Japanese',
        'kk' => 'Kazakh',
        'ko' => 'Korean',
        'nl' => 'Dutch',
        'no' => 'Norwegian',
        'pl' => 'Polish',
        'pt' => 'Portuguese',
        'pt_PT' => 'Portuguese (Portugal)',
        'ro' => 'Romanian',
        'ru' => 'Russian',
        'sq' => 'Albanian',
        'sr' => 'Serbian',
        'sv' => 'Swedish',
        'th' => 'Thai',
        'tr' => 'Turkish',
        'uk' => 'Ukrainian',
        'vi' => 'Vietnamese',
        'zh_CN' => 'Simplified Chinese',
        'zh_TW' => 'Traditional Chinese',
    ],
];
