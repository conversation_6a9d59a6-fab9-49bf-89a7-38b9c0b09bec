<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800"><?php echo e($page_title); ?></h1>
        <div class="btn-group">
            <button type="button" class="btn btn-info btn-sm" id="test-connection">
                <i class="fas fa-plug"></i> <?php echo e(__('Test Connection')); ?>

            </button>
            <a href="<?php echo e(route('ratehawk.admin.index')); ?>" class="btn btn-secondary btn-sm">
                <i class="fas fa-arrow-left"></i> <?php echo e(__('Back to Dashboard')); ?>

            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary"><?php echo e(__('RateHawk API Configuration')); ?></h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo e(route('ratehawk.admin.settings.save')); ?>" id="settings-form">
                        <?php echo csrf_field(); ?>

                        <!-- Enable/Disable -->
                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_enable" 
                                       name="ratehawk_enable" value="1" 
                                       <?php echo e(($settings['ratehawk_enable']->value ?? false) ? 'checked' : ''); ?>>
                                <label class="custom-control-label" for="ratehawk_enable">
                                    <?php echo e(__('Enable RateHawk API Integration')); ?>

                                </label>
                            </div>
                            <small class="form-text text-muted">
                                <?php echo e(__('Enable or disable the RateHawk API integration')); ?>

                            </small>
                        </div>

                        <hr>

                        <!-- API Credentials -->
                        <h6 class="text-primary mb-3"><?php echo e(__('API Credentials')); ?></h6>
                        
                        <div class="form-group">
                            <label for="ratehawk_environment"><?php echo e(__('Environment')); ?></label>
                            <select class="form-control" id="ratehawk_environment" name="ratehawk_environment">
                                <option value="test" <?php echo e(($settings['ratehawk_environment']->value ?? 'test') === 'test' ? 'selected' : ''); ?>>
                                    <?php echo e(__('Test/Sandbox')); ?>

                                </option>
                                <option value="production" <?php echo e(($settings['ratehawk_environment']->value ?? 'test') === 'production' ? 'selected' : ''); ?>>
                                    <?php echo e(__('Production')); ?>

                                </option>
                            </select>
                            <small class="form-text text-muted">
                                <?php echo e(__('Select the API environment to use')); ?>

                            </small>
                        </div>

                        <div class="form-group">
                            <label for="ratehawk_key_id"><?php echo e(__('API Key ID')); ?></label>
                            <input type="text" class="form-control" id="ratehawk_key_id" name="ratehawk_key_id" 
                                   value="<?php echo e($settings['ratehawk_key_id']->value ?? ''); ?>" placeholder="<?php echo e(__('Enter your API Key ID')); ?>">
                            <small class="form-text text-muted">
                                <?php echo e(__('Your RateHawk API Key ID')); ?>

                            </small>
                        </div>

                        <div class="form-group">
                            <label for="ratehawk_api_key"><?php echo e(__('API Key')); ?></label>
                            <input type="password" class="form-control" id="ratehawk_api_key" name="ratehawk_api_key" 
                                   value="<?php echo e($settings['ratehawk_api_key']->value ?? ''); ?>" placeholder="<?php echo e(__('Enter your API Key')); ?>">
                            <small class="form-text text-muted">
                                <?php echo e(__('Your RateHawk API Key (will be encrypted when saved)')); ?>

                            </small>
                        </div>

                        <hr>

                        <!-- Default Settings -->
                        <h6 class="text-primary mb-3"><?php echo e(__('Default Settings')); ?></h6>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ratehawk_default_currency"><?php echo e(__('Default Currency')); ?></label>
                                    <select class="form-control" id="ratehawk_default_currency" name="ratehawk_default_currency">
                                        <?php $__currentLoopData = $supported_currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($currency); ?>" 
                                                <?php echo e(($settings['ratehawk_default_currency']->value ?? 'USD') === $currency ? 'selected' : ''); ?>>
                                                <?php echo e($currency); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ratehawk_default_language"><?php echo e(__('Default Language')); ?></label>
                                    <select class="form-control" id="ratehawk_default_language" name="ratehawk_default_language">
                                        <?php $__currentLoopData = $supported_languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($code); ?>" 
                                                <?php echo e(($settings['ratehawk_default_language']->value ?? 'en') === $code ? 'selected' : ''); ?>>
                                                <?php echo e($name); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="ratehawk_default_residency"><?php echo e(__('Default Residency')); ?></label>
                                    <input type="text" class="form-control" id="ratehawk_default_residency" 
                                           name="ratehawk_default_residency" maxlength="2" 
                                           value="<?php echo e($settings['ratehawk_default_residency']->value ?? 'us'); ?>" 
                                           placeholder="us">
                                    <small class="form-text text-muted">
                                        <?php echo e(__('2-letter country code')); ?>

                                    </small>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <!-- Advanced Settings -->
                        <h6 class="text-primary mb-3"><?php echo e(__('Advanced Settings')); ?></h6>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_cache_enabled" 
                                       name="ratehawk_cache_enabled" value="1" 
                                       <?php echo e(($settings['ratehawk_cache_enabled']->value ?? true) ? 'checked' : ''); ?>>
                                <label class="custom-control-label" for="ratehawk_cache_enabled">
                                    <?php echo e(__('Enable Caching')); ?>

                                </label>
                            </div>
                            <small class="form-text text-muted">
                                <?php echo e(__('Cache API responses to improve performance')); ?>

                            </small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_logging_enabled" 
                                       name="ratehawk_logging_enabled" value="1" 
                                       <?php echo e(($settings['ratehawk_logging_enabled']->value ?? true) ? 'checked' : ''); ?>>
                                <label class="custom-control-label" for="ratehawk_logging_enabled">
                                    <?php echo e(__('Enable API Logging')); ?>

                                </label>
                            </div>
                            <small class="form-text text-muted">
                                <?php echo e(__('Log all API requests and responses for debugging')); ?>

                            </small>
                        </div>

                        <div class="form-group">
                            <div class="custom-control custom-switch">
                                <input type="checkbox" class="custom-control-input" id="ratehawk_webhooks_enabled" 
                                       name="ratehawk_webhooks_enabled" value="1" 
                                       <?php echo e(($settings['ratehawk_webhooks_enabled']->value ?? true) ? 'checked' : ''); ?>>
                                <label class="custom-control-label" for="ratehawk_webhooks_enabled">
                                    <?php echo e(__('Enable Webhooks')); ?>

                                </label>
                            </div>
                            <small class="form-text text-muted">
                                <?php echo e(__('Receive booking status updates via webhooks')); ?>

                            </small>
                        </div>

                        <div class="form-group">
                            <label for="ratehawk_webhook_secret"><?php echo e(__('Webhook Secret')); ?></label>
                            <input type="password" class="form-control" id="ratehawk_webhook_secret" 
                                   name="ratehawk_webhook_secret" 
                                   value="<?php echo e($settings['ratehawk_webhook_secret']->value ?? ''); ?>" 
                                   placeholder="<?php echo e(__('Enter webhook secret for signature verification')); ?>">
                            <small class="form-text text-muted">
                                <?php echo e(__('Secret key for webhook signature verification (optional but recommended)')); ?>

                            </small>
                        </div>

                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> <?php echo e(__('Save Settings')); ?>

                            </button>
                            <button type="button" class="btn btn-secondary" onclick="window.location.reload()">
                                <i class="fas fa-undo"></i> <?php echo e(__('Reset')); ?>

                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info"><?php echo e(__('Connection Status')); ?></h6>
                </div>
                <div class="card-body">
                    <div id="connection-status">
                        <p class="text-muted"><?php echo e(__('Click "Test Connection" to check API connectivity')); ?></p>
                    </div>
                </div>
            </div>

            <div class="card shadow mt-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info"><?php echo e(__('Quick Links')); ?></h6>
                </div>
                <div class="card-body">
                    <div class="list-group list-group-flush">
                        <a href="<?php echo e(route('ratehawk.admin.api-test.index')); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-flask"></i> <?php echo e(__('API Testing')); ?>

                        </a>
                        <a href="<?php echo e(route('ratehawk.admin.bookings.index')); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-calendar-check"></i> <?php echo e(__('View Bookings')); ?>

                        </a>
                        <a href="<?php echo e(route('ratehawk.admin.logs.index')); ?>" class="list-group-item list-group-item-action">
                            <i class="fas fa-list"></i> <?php echo e(__('API Logs')); ?>

                        </a>
                        <a href="https://docs.emergingtravel.com/docs/b2b-api/" target="_blank" class="list-group-item list-group-item-action">
                            <i class="fas fa-external-link-alt"></i> <?php echo e(__('API Documentation')); ?>

                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
$(document).ready(function() {
    $('#test-connection').click(function() {
        var btn = $(this);
        var originalText = btn.html();
        
        btn.html('<i class="fas fa-spinner fa-spin"></i> <?php echo e(__("Testing...")); ?>').prop('disabled', true);
        
        $.ajax({
            url: '<?php echo e(route("ratehawk.admin.test-connection")); ?>',
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    $('#connection-status').html(
                        '<div class="alert alert-success">' +
                        '<i class="fas fa-check-circle"></i> ' + response.message +
                        '</div>'
                    );
                } else {
                    $('#connection-status').html(
                        '<div class="alert alert-danger">' +
                        '<i class="fas fa-times-circle"></i> ' + response.message +
                        '</div>'
                    );
                }
            },
            error: function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '<?php echo e(__("Connection test failed")); ?>';
                $('#connection-status').html(
                    '<div class="alert alert-danger">' +
                    '<i class="fas fa-times-circle"></i> ' + message +
                    '</div>'
                );
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\wamp64\www\mazar\modules\RateHawk/Views/admin/settings.blade.php ENDPATH**/ ?>