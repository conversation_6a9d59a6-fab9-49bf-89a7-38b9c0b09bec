<?php

namespace Modules\RateHawk\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Modules\RateHawk\Services\BookingService;
use Modules\RateHawk\Models\RateHawkBooking;
use Modules\RateHawk\Events\BookingStatusUpdated;

class WebhookController extends Controller
{
    protected $bookingService;

    public function __construct(BookingService $bookingService)
    {
        $this->bookingService = $bookingService;
    }

    /**
     * Handle booking status webhook
     */
    public function bookingStatus(Request $request): JsonResponse
    {
        try {
            // Verify webhook signature if configured
            if (!$this->verifyWebhookSignature($request)) {
                Log::warning('RateHawk webhook signature verification failed', [
                    'ip' => $request->ip(),
                    'headers' => $request->headers->all()
                ]);
                
                return response()->json(['error' => 'Invalid signature'], 401);
            }

            $payload = $request->all();
            
            Log::info('RateHawk booking status webhook received', [
                'payload' => $payload,
                'ip' => $request->ip()
            ]);

            // Extract booking information
            $orderId = $payload['order_id'] ?? null;
            $status = $payload['status'] ?? null;
            $bookingData = $payload['booking_data'] ?? [];

            if (!$orderId || !$status) {
                Log::error('RateHawk webhook missing required fields', [
                    'payload' => $payload
                ]);
                
                return response()->json(['error' => 'Missing required fields'], 400);
            }

            // Update local booking
            $booking = $this->updateLocalBooking($orderId, $status, $bookingData);

            if ($booking) {
                // Fire event for booking status update
                event(new BookingStatusUpdated($booking, $status, $bookingData));
                
                Log::info('RateHawk booking status updated', [
                    'order_id' => $orderId,
                    'old_status' => $booking->getOriginal('status'),
                    'new_status' => $status
                ]);
            }

            return response()->json(['success' => true, 'message' => 'Webhook processed successfully']);

        } catch (\Exception $e) {
            Log::error('RateHawk webhook processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'payload' => $request->all()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Verify webhook signature
     */
    protected function verifyWebhookSignature(Request $request): bool
    {
        $webhookSecret = config('ratehawk.webhooks.secret');
        
        if (!$webhookSecret) {
            // If no secret is configured, skip verification
            return true;
        }

        $signature = $request->header('X-RateHawk-Signature');
        
        if (!$signature) {
            return false;
        }

        $payload = $request->getContent();
        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);

        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Update local booking with webhook data
     */
    protected function updateLocalBooking(string $orderId, string $status, array $bookingData): ?RateHawkBooking
    {
        try {
            $booking = RateHawkBooking::where('order_id', $orderId)->first();

            if (!$booking) {
                Log::warning('RateHawk webhook for unknown booking', [
                    'order_id' => $orderId,
                    'status' => $status
                ]);
                return null;
            }

            $updateData = [
                'status' => $status,
                'updated_at' => now()
            ];

            // Update specific fields based on booking data
            if (isset($bookingData['hotel'])) {
                $updateData['hotel_data'] = array_merge(
                    $booking->hotel_data ?? [], 
                    $bookingData['hotel']
                );
            }

            if (isset($bookingData['room'])) {
                $updateData['room_data'] = array_merge(
                    $booking->room_data ?? [], 
                    $bookingData['room']
                );
            }

            if (isset($bookingData['price'])) {
                $updateData['price_data'] = array_merge(
                    $booking->price_data ?? [], 
                    $bookingData['price']
                );
                
                // Update total amount if available
                if (isset($bookingData['price']['total_amount'])) {
                    $updateData['total_amount'] = $bookingData['price']['total_amount'];
                }
                
                if (isset($bookingData['price']['currency'])) {
                    $updateData['currency'] = $bookingData['price']['currency'];
                }
            }

            if (isset($bookingData['guests'])) {
                $updateData['guest_data'] = $bookingData['guests'];
            }

            if (isset($bookingData['checkin_date'])) {
                $updateData['checkin_date'] = $bookingData['checkin_date'];
            }

            if (isset($bookingData['checkout_date'])) {
                $updateData['checkout_date'] = $bookingData['checkout_date'];
            }

            if (isset($bookingData['cancellation_deadline'])) {
                $updateData['cancellation_deadline'] = $bookingData['cancellation_deadline'];
            }

            if (isset($bookingData['voucher_url'])) {
                $updateData['voucher_url'] = $bookingData['voucher_url'];
            }

            if (isset($bookingData['invoice_url'])) {
                $updateData['invoice_url'] = $bookingData['invoice_url'];
            }

            // Store the complete webhook payload in api_response
            $updateData['api_response'] = array_merge(
                $booking->api_response ?? [],
                ['webhook_' . now()->timestamp => $bookingData]
            );

            $booking->update($updateData);

            return $booking->fresh();

        } catch (\Exception $e) {
            Log::error('Failed to update local booking from webhook', [
                'order_id' => $orderId,
                'error' => $e->getMessage()
            ]);

            return null;
        }
    }
}
