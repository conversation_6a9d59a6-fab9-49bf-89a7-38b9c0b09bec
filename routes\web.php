<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// RateHawk Enhanced Home Page - MUST BE FIRST
Route::get('/home2', function() {
    return view('frontend.home-ratehawk', [
        'popular_destinations' => [
            ['name' => 'Paris', 'country' => 'France', 'hotels_count' => 1250],
            ['name' => 'London', 'country' => 'United Kingdom', 'hotels_count' => 980],
            ['name' => 'New York', 'country' => 'United States', 'hotels_count' => 1500],
            ['name' => 'Tokyo', 'country' => 'Japan', 'hotels_count' => 850],
            ['name' => 'Dubai', 'country' => 'UAE', 'hotels_count' => 650],
            ['name' => 'Barcelona', 'country' => 'Spain', 'hotels_count' => 720],
            ['name' => 'Rome', 'country' => 'Italy', 'hotels_count' => 890],
            ['name' => 'Amsterdam', 'country' => 'Netherlands', 'hotels_count' => 420],
        ],
        'featured_hotels' => [
            [
                'name' => 'Grand Hotel Paris',
                'location' => 'Paris, France',
                'rating' => 5,
                'price' => 250,
                'currency' => 'USD',
                'image' => null
            ],
            [
                'name' => 'London Palace Hotel',
                'location' => 'London, UK',
                'rating' => 4,
                'price' => 180,
                'currency' => 'USD',
                'image' => null
            ],
            [
                'name' => 'Manhattan Luxury Suites',
                'location' => 'New York, USA',
                'rating' => 5,
                'price' => 320,
                'currency' => 'USD',
                'image' => null
            ],
            [
                'name' => 'Tokyo Imperial Hotel',
                'location' => 'Tokyo, Japan',
                'rating' => 4,
                'price' => 200,
                'currency' => 'USD',
                'image' => null
            ],
        ],
        'recent_searches' => [
            ['destination' => 'Paris', 'count' => 150],
            ['destination' => 'London', 'count' => 120],
            ['destination' => 'New York', 'count' => 100],
            ['destination' => 'Tokyo', 'count' => 80],
            ['destination' => 'Dubai', 'count' => 75],
        ],
        'seo_meta' => [
            'title' => 'Mazar Travel - Hotel Search Powered by RateHawk',
            'description' => 'Search and book hotels worldwide with our advanced booking system powered by RateHawk API',
            'full_url' => url('/home2'),
        ],
        'is_home' => true,
        'page_title' => 'Hotel Search - Powered by RateHawk',
    ]);
})->name('home.ratehawk');

Route::get('/intro', 'LandingpageController@index');
Route::get('/', 'HomeController@index');
Route::get('/home', 'HomeController@index')->name('home');

Route::get('/api/search/destinations', 'HomeRateHawkController@searchDestinations')->name('api.search.destinations');
Route::post('/api/search/hotels', 'HomeRateHawkController@searchHotels')->name('api.search.hotels');
Route::get('/api/search/suggestions', 'HomeRateHawkController@getSearchSuggestions')->name('api.search.suggestions');

// Booking Management Pages (public access for booking lookup)
Route::get('/my-bookings', function() {
    return view('frontend.booking-history', [
        'seo_meta' => [
            'title' => 'My Bookings - Mazar Travel',
            'description' => 'View and manage your hotel bookings',
            'full_url' => url('/my-bookings'),
        ],
        'page_title' => 'My Bookings',
        'disable_login_popup' => true, // Disable login popup for this page
    ]);
})->name('bookings.history');

// Booking API Routes (no auth required for public booking system)
Route::post('/api/booking/process', 'BookingController@processBooking')->name('api.booking.process');
Route::get('/api/booking/{reference}', 'BookingController@getBooking')->name('api.booking.get');
Route::post('/api/booking/{reference}/cancel', 'BookingController@cancelBooking')->name('api.booking.cancel');
Route::get('/api/booking/history', 'BookingController@getBookingHistory')->name('api.booking.history');

Route::post('/install/check-db', 'HomeController@checkConnectDatabase');

// Social Login
Route::get('social-login/{provider}', 'Auth\LoginController@socialLogin');
Route::get('social-callback/{provider}', 'Auth\LoginController@socialCallBack');

// Logs
Route::get(config('admin.admin_route_prefix') . '/logs', '\Rap2hpoutre\LaravelLogViewer\LogViewerController@index')->middleware(['auth', 'dashboard', 'system_log_view'])->name('admin.logs');

Route::get('/install', 'InstallerController@redirectToRequirement')->name('LaravelInstaller::welcome');
Route::get('/install/environment', 'InstallerController@redirectToWizard')->name('LaravelInstaller::environment');
Route::fallback([\Modules\Core\Controllers\FallbackController::class, 'FallBack']);

// Hide page update default
Route::get('/update', 'InstallerController@redirectToHome');
Route::get('/update/overview', 'InstallerController@redirectToHome');
Route::get('/update/database', 'InstallerController@redirectToHome');
