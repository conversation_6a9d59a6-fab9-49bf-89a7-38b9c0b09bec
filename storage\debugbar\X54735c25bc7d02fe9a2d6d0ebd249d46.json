{"__meta": {"id": "X54735c25bc7d02fe9a2d6d0ebd249d46", "datetime": "2025-07-10 21:16:55", "utime": **********.696781, "method": "POST", "uri": "/api/search/hotels", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752182214.503181, "end": **********.696869, "duration": 1.193687915802002, "duration_str": "1.19s", "measures": [{"label": "Booting", "start": 1752182214.503181, "relative_start": 0, "end": **********.449312, "relative_end": **********.449312, "duration": 0.9461309909820557, "duration_str": "946ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.449372, "relative_start": 0.9461910724639893, "end": **********.696879, "relative_end": 1.0013580322265625e-05, "duration": 0.24750685691833496, "duration_str": "248ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6274784, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST api/search/hotels", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeRateHawkController@searchHotels", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "api.search.hotels", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeRateHawkController.php&line=112\" onclick=\"\">app/Http/Controllers/HomeRateHawkController.php:112-167</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.01333, "accumulated_duration_str": "13.33ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.627223, "duration": 0.01191, "duration_str": "11.91ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.651265, "duration": 0.0014199999999999998, "duration_str": "1.42ms", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/custom-css\"\n]"}, "request": {"path_info": "/api/search/hotels", "status_code": "<pre class=sf-dump id=sf-dump-529580666 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-529580666\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-303622700 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-303622700\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-820977669 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>destination</span>\" => \"<span class=sf-dump-str title=\"6 characters\">London</span>\"\n  \"<span class=sf-dump-key>checkin</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-11</span>\"\n  \"<span class=sf-dump-key>checkout</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-12</span>\"\n  \"<span class=sf-dump-key>adults</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>children</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>rooms</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-820977669\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1383302690 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">133</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/home2</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2130 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImVaek5TdFZsZzYrVlRnRDJxdDBleUE9PSIsInZhbHVlIjoidDV1cmR2NnBUdTc3WHpMRHY3YmFISDdmdDVJYXNqcmxuNm1rd1E4UHdZTFFJRXNKQVh6QTRVRXhzNGkrS2d4MlNRSWpuUDN0TlFQWndFUUw1VWp1MlZWdTk4QVJtS2lPdDF0VTRhSXozamJudXhGRUZsMXVHNUxyQ1ZlMGxEK1giLCJtYWMiOiI4OTZkYjVjYjQ5ZDMzYmQ3OTI0MjE2NjlmYmI0M2Y3MzViOWIwNDA1YWYyODg1MTY4YTg4YjIzYTcxZDRiZjQ1IiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IlZXbS9ISm52TkpLR3RGcWFRMkZKd1E9PSIsInZhbHVlIjoiQmZuOTBjdlFzUEhabmF2NzVnUU5ZTWxET1YvVDFYUld0R2NFcGdNVEtKUmpNczdIQWI1ZzJjY3RrTFIyaGt5MEZmbHJBZ050NkM0amdtTmZGOWE2TmlxNHlkdVh3K000SGRxTy9TMG5JYXRjY3kvTGkramJISjlNYVFnaGZPSlMiLCJtYWMiOiJiYTFiNDM2YzBkMmI4NzE3ODQ5ZmUwYzQ1NDAyMGIyZDdmYTUxYmEwNjg5ODg5YmVjMGUyZjEyMzM2NWU5ZmJlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383302690\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">yb4TGjWKGpYF4OBLsQqAoVj4lnjPcZCXwUEApQbS</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1826544000 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 21:16:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkxraSs4UDlZT2hHZnRqNUNWL1BrekE9PSIsInZhbHVlIjoiS1pCYk5FMmdtZU80N2kxZk5PZFJpNitFK0lMMlVCaDZlUFZURHl6VW9EdER5NnRRUVY5N1ovN0FPTUkram0xa2szTEVtK3FHT1lEZ3Bpa2lrNGlHcXozYjV0czNNYS9MaDdWeHpOUFJidlVSeVlWRG1JQ3Nudy9kOGRTSEp2WXIiLCJtYWMiOiIwOTM2ZDNhYjU2M2RiYmY0YWM4ODQyYzcyYTQ4ODY0YWU5MWI5ZWRjMThhYjYxY2MxNzExYjk0YjAyOTZiZWIzIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:16:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6InFFRWxXVUpTREUrbENCRE5NakcvMXc9PSIsInZhbHVlIjoidEhGVU9IcTNjd1VTSDBHaWNmZ0FRT3U0OEZEVkV1K3dIOG9ybTdMcUtnRXlzWXp3NEJyS3RPL0srVGFHaHFCN3d1eXI0Rk5tcklaQzhwWE9nd3JwWlhFdzJaL1FFU0gxZWtJUFRMV2FSeVNqa04vbEhoM2V3QWFVTlBSZldHSFEiLCJtYWMiOiIxNzFjZTE3YWEyMjJhZmVlMzE2MjMwNzhlYjQ2YTYyMzE1YzI0NzMyMTE4MTZjMjQ0OTAxOTQwMGY4ZDU4YWU2IiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:16:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkxraSs4UDlZT2hHZnRqNUNWL1BrekE9PSIsInZhbHVlIjoiS1pCYk5FMmdtZU80N2kxZk5PZFJpNitFK0lMMlVCaDZlUFZURHl6VW9EdER5NnRRUVY5N1ovN0FPTUkram0xa2szTEVtK3FHT1lEZ3Bpa2lrNGlHcXozYjV0czNNYS9MaDdWeHpOUFJidlVSeVlWRG1JQ3Nudy9kOGRTSEp2WXIiLCJtYWMiOiIwOTM2ZDNhYjU2M2RiYmY0YWM4ODQyYzcyYTQ4ODY0YWU5MWI5ZWRjMThhYjYxY2MxNzExYjk0YjAyOTZiZWIzIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:16:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6InFFRWxXVUpTREUrbENCRE5NakcvMXc9PSIsInZhbHVlIjoidEhGVU9IcTNjd1VTSDBHaWNmZ0FRT3U0OEZEVkV1K3dIOG9ybTdMcUtnRXlzWXp3NEJyS3RPL0srVGFHaHFCN3d1eXI0Rk5tcklaQzhwWE9nd3JwWlhFdzJaL1FFU0gxZWtJUFRMV2FSeVNqa04vbEhoM2V3QWFVTlBSZldHSFEiLCJtYWMiOiIxNzFjZTE3YWEyMjJhZmVlMzE2MjMwNzhlYjQ2YTYyMzE1YzI0NzMyMTE4MTZjMjQ0OTAxOTQwMGY4ZDU4YWU2IiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:16:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1826544000\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">JiyM64oNv7piLKfLNcyRBySFY9hcwQFUXan3qadS</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/custom-css</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}}