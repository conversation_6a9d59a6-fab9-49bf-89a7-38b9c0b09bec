<?php

namespace Modules\RateHawk\Http\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Models\RateHawkUserSearch;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;

class HotelSearchController extends Controller
{
    protected $searchService;

    public function __construct(HotelSearchService $searchService)
    {
        $this->searchService = $searchService;
    }

    /**
     * Search hotels
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'destination' => 'required|string|min:2',
                'checkin' => 'required|date|after_or_equal:today',
                'checkout' => 'required|date|after:checkin',
                'adults' => 'required|integer|min:1|max:10',
                'children' => 'nullable|integer|min:0|max:10',
                'rooms' => 'nullable|integer|min:1|max:9',
                'currency' => 'nullable|string|size:3',
                'min_price' => 'nullable|numeric|min:0',
                'max_price' => 'nullable|numeric|min:0',
                'star_rating' => 'nullable|array',
                'star_rating.*' => 'integer|between:1,5',
                'amenities' => 'nullable|array',
                'sort_by' => 'nullable|string|in:price,rating,distance,name',
                'sort_order' => 'nullable|string|in:asc,desc',
                'page' => 'nullable|integer|min:1',
                'per_page' => 'nullable|integer|min:1|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $searchParams = $request->all();
            $result = $this->searchService->searchHotels($searchParams);

            // Record search for analytics
            if ($result['success']) {
                RateHawkUserSearch::recordSearch(array_merge($searchParams, [
                    'results_count' => $result['data']['total'] ?? 0
                ]));
            }

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Hotel search API error', ['error' => $e->getMessage(), 'request' => $request->all()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Search failed',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get hotel details
     */
    public function details(Request $request, string $hotelId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'checkin' => 'nullable|date|after_or_equal:today',
                'checkout' => 'nullable|date|after:checkin',
                'adults' => 'nullable|integer|min:1|max:10',
                'children' => 'nullable|integer|min:0|max:10',
                'rooms' => 'nullable|integer|min:1|max:9',
                'currency' => 'nullable|string|size:3',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $searchParams = $request->all();
            $result = $this->searchService->getHotelDetails($hotelId, $searchParams);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Hotel details API error', ['error' => $e->getMessage(), 'hotel_id' => $hotelId]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get hotel details',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Search destinations for autocomplete
     */
    public function destinations(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'query' => 'required|string|min:2|max:100',
                'limit' => 'nullable|integer|min:1|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = $request->input('query');
            $limit = $request->input('limit', 10);

            $result = $this->searchService->searchDestinations($query, $limit);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Destination search API error', ['error' => $e->getMessage(), 'query' => $request->input('query')]);
            
            return response()->json([
                'success' => false,
                'message' => 'Destination search failed',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get popular destinations
     */
    public function popularDestinations(Request $request): JsonResponse
    {
        try {
            $limit = $request->input('limit', 20);
            $result = $this->searchService->getPopularDestinations($limit);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Popular destinations API error', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get popular destinations',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get search suggestions
     */
    public function suggestions(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'input' => 'required|string|min:1|max:100',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $input = $request->input('input');
            $result = $this->searchService->getSearchSuggestions($input);

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Search suggestions API error', ['error' => $e->getMessage(), 'input' => $request->input('input')]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get suggestions',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get hotel pricing
     */
    public function pricing(Request $request, string $hotelId): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'checkin' => 'required|date|after_or_equal:today',
                'checkout' => 'required|date|after:checkin',
                'adults' => 'required|integer|min:1|max:10',
                'children' => 'nullable|integer|min:0|max:10',
                'rooms' => 'nullable|integer|min:1|max:9',
                'currency' => 'nullable|string|size:3',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $searchParams = $request->all();
            $pricing = $this->searchService->getHotelPricing($hotelId, $searchParams);

            if ($pricing) {
                return response()->json([
                    'success' => true,
                    'data' => $pricing
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'Pricing not available'
            ], 404);

        } catch (\Exception $e) {
            Log::error('Hotel pricing API error', ['error' => $e->getMessage(), 'hotel_id' => $hotelId]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get pricing',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get user's search history
     */
    public function searchHistory(Request $request): JsonResponse
    {
        try {
            if (!auth()->check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Authentication required'
                ], 401);
            }

            $limit = $request->input('limit', 20);
            $history = RateHawkUserSearch::getUserSearchHistory(auth()->id(), $limit);

            return response()->json([
                'success' => true,
                'data' => $history
            ]);

        } catch (\Exception $e) {
            Log::error('Search history API error', ['error' => $e->getMessage(), 'user_id' => auth()->id()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get search history',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get search analytics (for admin)
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            // Check if user has admin permissions
            if (!auth()->check() || !auth()->user()->hasRole('admin')) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized'
                ], 403);
            }

            $days = $request->input('days', 7);
            
            $analytics = [
                'popular_destinations' => RateHawkUserSearch::getPopularDestinations(10),
                'search_trends' => RateHawkUserSearch::getSearchTrends($days),
                'conversion_rate' => RateHawkUserSearch::getConversionRate(),
                'total_searches' => RateHawkUserSearch::count(),
                'recent_searches' => RateHawkUserSearch::recent($days)->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $analytics
            ]);

        } catch (\Exception $e) {
            Log::error('Search analytics API error', ['error' => $e->getMessage()]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get analytics',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}
