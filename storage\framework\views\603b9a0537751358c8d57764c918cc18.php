<?php $__env->startSection('content'); ?>
    <div class="container-fluid">
        <div class="d-flex justify-content-between">
            <h1 class="h3 mb-3 text-gray-800"><?php echo e($page_title); ?></h1>
        </div>
        <?php echo $__env->make('admin.message', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        
        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    <?php echo e(__('Total Requests')); ?>

                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['total'] ?? 0); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-list fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    <?php echo e(__('Successful')); ?>

                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['completed'] ?? 0); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-check fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-danger shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                    <?php echo e(__('Errors')); ?>

                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['error'] ?? 0); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    <?php echo e(__('Pending')); ?>

                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800"><?php echo e($stats['pending'] ?? 0); ?></div>
                            </div>
                            <div class="col-auto">
                                <i class="fas fa-clock fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary"><?php echo e(__('Filters')); ?></h6>
            </div>
            <div class="card-body">
                <form method="GET" action="<?php echo e(route('ratehawk.admin.logs.index')); ?>">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="form-group">
                                <label><?php echo e(__('Status')); ?></label>
                                <select name="status" class="form-control">
                                    <option value=""><?php echo e(__('All Statuses')); ?></option>
                                    <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($key); ?>" <?php echo e(($filters['status'] ?? '') == $key ? 'selected' : ''); ?>>
                                            <?php echo e($label); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label><?php echo e(__('Method')); ?></label>
                                <select name="method" class="form-control">
                                    <option value=""><?php echo e(__('All Methods')); ?></option>
                                    <?php $__currentLoopData = $methods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $method): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($method); ?>" <?php echo e(($filters['method'] ?? '') == $method ? 'selected' : ''); ?>>
                                            <?php echo e($method); ?>

                                        </option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label><?php echo e(__('From Date')); ?></label>
                                <input type="date" name="from_date" class="form-control" value="<?php echo e($filters['from_date'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label><?php echo e(__('To Date')); ?></label>
                                <input type="date" name="to_date" class="form-control" value="<?php echo e($filters['to_date'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label><?php echo e(__('Search')); ?></label>
                                <input type="text" name="search" class="form-control" placeholder="<?php echo e(__('Request ID, Endpoint, Error...')); ?>" value="<?php echo e($filters['search'] ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-1">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block"><?php echo e(__('Filter')); ?></button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary"><?php echo e(__('API Logs')); ?></h6>
                <div>
                    <a href="<?php echo e(route('ratehawk.admin.logs.export')); ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-download"></i> <?php echo e(__('Export')); ?>

                    </a>
                    <button type="button" class="btn btn-danger btn-sm" onclick="clearLogs()">
                        <i class="fas fa-trash"></i> <?php echo e(__('Clear All')); ?>

                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th><?php echo e(__('Request ID')); ?></th>
                                <th><?php echo e(__('Method')); ?></th>
                                <th><?php echo e(__('Endpoint')); ?></th>
                                <th><?php echo e(__('Status')); ?></th>
                                <th><?php echo e(__('Response Time')); ?></th>
                                <th><?php echo e(__('User')); ?></th>
                                <th><?php echo e(__('Created At')); ?></th>
                                <th><?php echo e(__('Actions')); ?></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $logs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $log): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td>
                                        <code><?php echo e($log->request_id); ?></code>
                                    </td>
                                    <td>
                                        <span class="badge badge-secondary"><?php echo e($log->method); ?></span>
                                    </td>
                                    <td>
                                        <small><?php echo e($log->endpoint); ?></small>
                                    </td>
                                    <td>
                                        <?php if($log->status == 'completed'): ?>
                                            <span class="badge badge-success"><?php echo e(__('Completed')); ?></span>
                                        <?php elseif($log->status == 'error'): ?>
                                            <span class="badge badge-danger"><?php echo e(__('Error')); ?></span>
                                        <?php else: ?>
                                            <span class="badge badge-warning"><?php echo e(__('Pending')); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($log->response_time): ?>
                                            <?php echo e($log->response_time); ?>ms
                                        <?php else: ?>
                                            -
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if($log->user): ?>
                                            <?php echo e($log->user->name); ?>

                                        <?php else: ?>
                                            <?php echo e(__('System')); ?>

                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php echo e($log->created_at->format('Y-m-d H:i:s')); ?>

                                    </td>
                                    <td>
                                        <a href="<?php echo e(route('ratehawk.admin.logs.show', $log->id)); ?>" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger btn-sm" onclick="deleteLog(<?php echo e($log->id); ?>)">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="8" class="text-center"><?php echo e(__('No logs found')); ?></td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    <?php echo e($logs->appends(request()->query())->links()); ?>

                </div>
            </div>
        </div>
    </div>

    <script>
        function clearLogs() {
            if (confirm('<?php echo e(__("Are you sure you want to clear all logs? This action cannot be undone.")); ?>')) {
                $.post('<?php echo e(route("ratehawk.admin.logs.clear")); ?>', {
                    _token: '<?php echo e(csrf_token()); ?>'
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || '<?php echo e(__("Error occurred")); ?>');
                    }
                });
            }
        }

        function deleteLog(id) {
            if (confirm('<?php echo e(__("Are you sure you want to delete this log?")); ?>')) {
                $.ajax({
                    url: '<?php echo e(route("ratehawk.admin.logs.destroy", ":id")); ?>'.replace(':id', id),
                    method: 'DELETE',
                    data: {
                        _token: '<?php echo e(csrf_token()); ?>'
                    }
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || '<?php echo e(__("Error occurred")); ?>');
                    }
                });
            }
        }
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\wamp64\www\mazar\modules\RateHawk/Views/admin/logs/index.blade.php ENDPATH**/ ?>