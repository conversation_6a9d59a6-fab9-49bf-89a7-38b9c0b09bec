<?php

namespace Modules\RateHawk\Admin;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Modules\Admin\Controllers\Controller;
use Modules\RateHawk\Services\RateHawkApiClient;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Services\StaticContentService;
use Modules\RateHawk\Exceptions\RateHawkApiException;

class ApiTestController extends Controller
{
    protected $apiClient;
    protected $hotelSearchService;
    protected $staticContentService;

    public function __construct(
        RateHawkApiClient $apiClient,
        HotelSearchService $hotelSearchService,
        StaticContentService $staticContentService
    ) {
        parent::__construct();
        $this->apiClient = $apiClient;
        $this->hotelSearchService = $hotelSearchService;
        $this->staticContentService = $staticContentService;
    }

    /**
     * Display API testing interface
     */
    public function index(Request $request): View
    {
        $this->checkPermission('ratehawk_manage');

        $data = [
            'page_title' => __('RateHawk API Testing'),
            'supported_currencies' => config('ratehawk.supported_currencies'),
            'supported_languages' => config('ratehawk.supported_languages'),
            'test_hotel' => config('ratehawk.test_hotel'),
            'breadcrumbs' => [
                ['name' => __('Dashboard'), 'url' => route('admin.index')],
                ['name' => __('RateHawk'), 'url' => route('ratehawk.admin.index')],
                ['name' => __('API Testing'), 'class' => 'active'],
            ],
        ];

        return view('RateHawk::admin.api-test.index', $data);
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        try {
            $result = $this->apiClient->testConnection();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['data'],
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Test hotel search
     */
    public function searchHotels(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        $request->validate([
            'search_type' => 'required|in:region,hotels,coordinates',
            'checkin' => 'required|date|after_or_equal:today',
            'checkout' => 'required|date|after:checkin',
            'guests' => 'required|array|min:1',
            'guests.*.adults' => 'required|integer|min:1|max:6',
            'guests.*.children' => 'nullable|array',
            'region_id' => 'required_if:search_type,region|integer',
            'hotel_ids' => 'required_if:search_type,hotels|array',
            'latitude' => 'required_if:search_type,coordinates|numeric',
            'longitude' => 'required_if:search_type,coordinates|numeric',
            'currency' => 'nullable|string|size:3',
            'language' => 'nullable|string|size:2',
        ]);

        try {
            $params = $request->all();
            $params['residency'] = $params['residency'] ?? config('ratehawk.defaults.residency');
            $params['currency'] = $params['currency'] ?? config('ratehawk.defaults.currency');
            $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');

            $searchType = $request->search_type;
            
            switch ($searchType) {
                case 'region':
                    $results = $this->hotelSearchService->searchByRegion($params);
                    break;
                case 'hotels':
                    $params['ids'] = $request->hotel_ids;
                    $results = $this->hotelSearchService->searchByHotels($params);
                    break;
                case 'coordinates':
                    $results = $this->hotelSearchService->searchByCoordinates($params);
                    break;
                default:
                    throw new \InvalidArgumentException('Invalid search type');
            }

            return response()->json([
                'success' => true,
                'message' => 'Search completed successfully',
                'data' => $results,
                'search_params' => $params,
                'timestamp' => now()->toISOString()
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails(),
                'timestamp' => now()->toISOString()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Search failed: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get API endpoints
     */
    public function getEndpoints(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        try {
            $endpoints = $this->apiClient->getEndpoints();

            return response()->json([
                'success' => true,
                'message' => 'Endpoints retrieved successfully',
                'data' => $endpoints,
                'timestamp' => now()->toISOString()
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails(),
                'timestamp' => now()->toISOString()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get endpoints: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Test hotel suggestions
     */
    public function testSuggestions(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        $request->validate([
            'query' => 'required|string|min:2',
            'language' => 'nullable|string|size:2',
        ]);

        try {
            $suggestions = $this->hotelSearchService->suggest([
                'query' => $request->query,
                'language' => $request->language ?? config('ratehawk.defaults.language')
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Suggestions retrieved successfully',
                'data' => $suggestions,
                'timestamp' => now()->toISOString()
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails(),
                'timestamp' => now()->toISOString()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get suggestions: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Test regions retrieval
     */
    public function testRegions(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        try {
            $filters = $request->only(['country_code', 'type', 'search']);
            $regions = $this->staticContentService->getRegions($filters);

            return response()->json([
                'success' => true,
                'message' => 'Regions retrieved successfully',
                'data' => array_slice($regions, 0, 50), // Limit to first 50 for testing
                'total_count' => count($regions),
                'timestamp' => now()->toISOString()
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails(),
                'timestamp' => now()->toISOString()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get regions: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Test hotel static data
     */
    public function testHotelStatic(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        $request->validate([
            'hotel_id' => 'required|string',
            'language' => 'nullable|string|size:2',
        ]);

        try {
            $hotelData = $this->staticContentService->getHotelStaticData(
                $request->hotel_id,
                ['language' => $request->language ?? config('ratehawk.defaults.language')]
            );

            return response()->json([
                'success' => true,
                'message' => 'Hotel static data retrieved successfully',
                'data' => $hotelData,
                'timestamp' => now()->toISOString()
            ]);

        } catch (RateHawkApiException $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage(),
                'error_details' => $e->getErrorDetails(),
                'timestamp' => now()->toISOString()
            ], $e->getStatusCode() ?: 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get hotel static data: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Clear cache
     */
    public function clearCache(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        try {
            $this->hotelSearchService->clearCache();
            $this->staticContentService->clearCache();

            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }
}
