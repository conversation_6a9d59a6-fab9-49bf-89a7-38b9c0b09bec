<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RateHawk Cache Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the cache configuration for the RateHawk module.
    | You can configure different cache drivers and TTL values for different
    | types of data to optimize performance.
    |
    */

    // Cache driver to use (file, database, redis, memcached, array)
    'driver' => env('RATEHAWK_CACHE_DRIVER', config('cache.default')),

    // Enable/disable caching
    'enabled' => env('RATEHAWK_CACHE_ENABLED', true),

    // Cache TTL values (in minutes)
    'ttl' => [
        // Static hotel data (name, location, amenities, images)
        'static_data' => env('RATEHAWK_CACHE_STATIC_TTL', 1440), // 24 hours

        // Semi-dynamic data (availability calendars, policies)
        'semi_dynamic' => env('RATEHAWK_CACHE_SEMI_DYNAMIC_TTL', 360), // 6 hours

        // Dynamic data (real-time pricing, availability)
        'dynamic_data' => env('RATEHAWK_CACHE_DYNAMIC_TTL', 15), // 15 minutes

        // Search results
        'search_results' => env('RATEHAWK_CACHE_SEARCH_TTL', 30), // 30 minutes

        // Pricing data
        'pricing_data' => env('RATEHAWK_CACHE_PRICING_TTL', 5), // 5 minutes

        // Destination data
        'destinations' => env('RATEHAWK_CACHE_DESTINATIONS_TTL', 720), // 12 hours

        // Popular destinations
        'popular_destinations' => env('RATEHAWK_CACHE_POPULAR_TTL', 180), // 3 hours
    ],

    // Cache key prefixes
    'prefixes' => [
        'hotel_static' => 'rh_hotel_static',
        'hotel_pricing' => 'rh_hotel_pricing',
        'hotel_amenities' => 'rh_hotel_amenities',
        'search_results' => 'rh_search',
        'destinations' => 'rh_destinations',
        'popular_destinations' => 'rh_popular_dest',
        'user_searches' => 'rh_user_search',
    ],

    // Cache warming settings
    'warming' => [
        'enabled' => env('RATEHAWK_CACHE_WARMING_ENABLED', true),
        'popular_destinations_count' => 20,
        'popular_hotels_count' => 50,
        'schedule' => '0 2 * * *', // Daily at 2 AM
    ],

    // Cache cleanup settings
    'cleanup' => [
        'enabled' => env('RATEHAWK_CACHE_CLEANUP_ENABLED', true),
        'schedule' => '0 3 * * *', // Daily at 3 AM
        'keep_days' => 7, // Keep cache entries for 7 days max
    ],

    // Fallback settings
    'fallback' => [
        // Use stale cache if API fails
        'use_stale_on_error' => env('RATEHAWK_CACHE_USE_STALE', true),
        
        // Maximum age of stale cache to use (in hours)
        'max_stale_age' => env('RATEHAWK_CACHE_MAX_STALE_AGE', 24),
        
        // Fallback to database cache if Redis fails
        'fallback_to_database' => env('RATEHAWK_CACHE_FALLBACK_DB', true),
    ],

    // Performance monitoring
    'monitoring' => [
        'enabled' => env('RATEHAWK_CACHE_MONITORING', true),
        'log_slow_queries' => env('RATEHAWK_CACHE_LOG_SLOW', true),
        'slow_query_threshold' => 1000, // milliseconds
        'track_hit_rates' => true,
    ],

    // Cache stores configuration
    'stores' => [
        // Primary cache store (Redis recommended)
        'primary' => [
            'driver' => env('RATEHAWK_CACHE_PRIMARY_DRIVER', 'redis'),
            'connection' => env('RATEHAWK_CACHE_PRIMARY_CONNECTION', 'cache'),
        ],

        // Secondary cache store (Database fallback)
        'secondary' => [
            'driver' => env('RATEHAWK_CACHE_SECONDARY_DRIVER', 'database'),
            'table' => env('RATEHAWK_CACHE_SECONDARY_TABLE', 'ratehawk_search_cache'),
        ],

        // Memory cache for request-level caching
        'memory' => [
            'driver' => 'array',
            'serialize' => false,
        ],
    ],

    // Cache tags (if supported by driver)
    'tags' => [
        'hotels' => 'ratehawk_hotels',
        'searches' => 'ratehawk_searches',
        'destinations' => 'ratehawk_destinations',
        'pricing' => 'ratehawk_pricing',
        'static' => 'ratehawk_static',
    ],

    // Cache compression
    'compression' => [
        'enabled' => env('RATEHAWK_CACHE_COMPRESSION', false),
        'algorithm' => 'gzip', // gzip, deflate, brotli
        'level' => 6, // Compression level (1-9)
        'min_size' => 1024, // Minimum size to compress (bytes)
    ],

    // Development settings
    'development' => [
        // Disable cache in development
        'disable_in_debug' => env('RATEHAWK_CACHE_DISABLE_DEBUG', false),
        
        // Log all cache operations in development
        'log_operations' => env('RATEHAWK_CACHE_LOG_OPS', false),
        
        // Cache debugging
        'debug_headers' => env('RATEHAWK_CACHE_DEBUG_HEADERS', false),
    ],
];
