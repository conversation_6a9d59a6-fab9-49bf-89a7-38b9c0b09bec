<?php

namespace Modules\RateHawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class RateHawkSearchCache extends Model
{
    use HasFactory;

    protected $table = 'ratehawk_search_cache';

    protected $fillable = [
        'search_hash',
        'search_params',
        'results_data',
        'results_count',
        'expires_at',
        'hit_count',
        'last_accessed',
    ];

    protected $casts = [
        'search_params' => 'array',
        'results_count' => 'integer',
        'expires_at' => 'datetime',
        'hit_count' => 'integer',
        'last_accessed' => 'datetime',
    ];

    /**
     * Check if cache is still valid
     */
    public function isValid(): bool
    {
        return $this->expires_at && $this->expires_at->isFuture();
    }

    /**
     * Increment hit count and update last accessed
     */
    public function recordHit(): void
    {
        $this->increment('hit_count');
        $this->update(['last_accessed' => now()]);
    }

    /**
     * Get cached results as array
     */
    public function getResultsAttribute(): array
    {
        return json_decode($this->results_data, true) ?? [];
    }

    /**
     * Set results data
     */
    public function setResultsAttribute(array $results): void
    {
        $this->attributes['results_data'] = json_encode($results);
        $this->attributes['results_count'] = count($results);
    }

    /**
     * Generate search hash from parameters
     */
    public static function generateSearchHash(array $params): string
    {
        // Sort parameters for consistent hashing
        ksort($params);
        return hash('sha256', json_encode($params));
    }

    /**
     * Find cached search results
     */
    public static function findCachedResults(array $searchParams): ?self
    {
        $hash = static::generateSearchHash($searchParams);
        
        return static::where('search_hash', $hash)
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * Cache search results
     */
    public static function cacheResults(array $searchParams, array $results, int $ttlMinutes = 30): self
    {
        $hash = static::generateSearchHash($searchParams);
        
        return static::updateOrCreate(
            ['search_hash' => $hash],
            [
                'search_params' => $searchParams,
                'results_data' => json_encode($results),
                'results_count' => count($results),
                'expires_at' => now()->addMinutes($ttlMinutes),
                'hit_count' => 0,
                'last_accessed' => now(),
            ]
        );
    }

    /**
     * Scope for valid cache entries
     */
    public function scopeValid($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Scope for expired cache entries
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope for popular searches (high hit count)
     */
    public function scopePopular($query, int $minHits = 5)
    {
        return $query->where('hit_count', '>=', $minHits);
    }

    /**
     * Clean up expired cache entries
     */
    public static function cleanupExpired(): int
    {
        return static::expired()->delete();
    }

    /**
     * Get cache statistics
     */
    public static function getCacheStats(): array
    {
        $total = static::count();
        $valid = static::valid()->count();
        $expired = static::expired()->count();
        $totalHits = static::sum('hit_count');
        $avgHits = $total > 0 ? round($totalHits / $total, 2) : 0;

        return [
            'total_entries' => $total,
            'valid_entries' => $valid,
            'expired_entries' => $expired,
            'total_hits' => $totalHits,
            'average_hits' => $avgHits,
            'cache_hit_rate' => $total > 0 ? round(($valid / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Get popular search destinations
     */
    public static function getPopularDestinations(int $limit = 10): array
    {
        return static::valid()
            ->popular()
            ->orderBy('hit_count', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($cache) {
                return [
                    'destination' => $cache->search_params['destination'] ?? 'Unknown',
                    'hit_count' => $cache->hit_count,
                    'last_accessed' => $cache->last_accessed,
                ];
            })
            ->toArray();
    }

    /**
     * Get search trends for a specific period
     */
    public static function getSearchTrends(int $days = 7): array
    {
        $startDate = now()->subDays($days);
        
        return static::where('created_at', '>=', $startDate)
            ->selectRaw('DATE(created_at) as date, COUNT(*) as searches, SUM(hit_count) as total_hits')
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Warm cache for popular searches
     */
    public static function warmPopularSearches(): array
    {
        $popularSearches = static::popular(10)
            ->orderBy('hit_count', 'desc')
            ->limit(20)
            ->get();

        $warmed = [];
        foreach ($popularSearches as $search) {
            if (!$search->isValid()) {
                // This would trigger a new API call to refresh the cache
                $warmed[] = $search->search_params;
            }
        }

        return $warmed;
    }

    /**
     * Get cache performance metrics
     */
    public static function getPerformanceMetrics(): array
    {
        $recentSearches = static::where('created_at', '>=', now()->subHours(24))->count();
        $recentHits = static::where('last_accessed', '>=', now()->subHours(24))->sum('hit_count');
        
        return [
            'recent_searches' => $recentSearches,
            'recent_hits' => $recentHits,
            'cache_efficiency' => $recentSearches > 0 ? round(($recentHits / $recentSearches) * 100, 2) : 0,
            'storage_size_mb' => round(static::sum(\DB::raw('LENGTH(results_data)')) / 1024 / 1024, 2),
        ];
    }
}
