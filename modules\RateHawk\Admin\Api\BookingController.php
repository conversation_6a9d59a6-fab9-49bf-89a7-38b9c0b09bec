<?php

namespace Modules\RateHawk\Admin\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Models\RateHawkBooking;
use Modules\RateHawk\Models\RateHawkApiLog;
use Modules\RateHawk\Services\BookingService;

class BookingController extends Controller
{
    protected $bookingService;

    public function __construct(BookingService $bookingService)
    {
        $this->bookingService = $bookingService;
    }

    /**
     * Get bookings list with pagination and filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = RateHawkBooking::with('user');

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('from_date')) {
                $query->where('created_at', '>=', $request->from_date);
            }

            if ($request->filled('to_date')) {
                $query->where('created_at', '<=', $request->to_date . ' 23:59:59');
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('order_id', 'like', "%{$search}%")
                      ->orWhere('partner_order_id', 'like', "%{$search}%")
                      ->orWhereJsonContains('hotel_data->name', $search);
                });
            }

            $perPage = $request->input('per_page', 20);
            $bookings = $query->orderBy('created_at', 'desc')->paginate($perPage);

            $data = [
                'bookings' => $bookings->items(),
                'pagination' => [
                    'current_page' => $bookings->currentPage(),
                    'last_page' => $bookings->lastPage(),
                    'per_page' => $bookings->perPage(),
                    'total' => $bookings->total(),
                    'from' => $bookings->firstItem(),
                    'to' => $bookings->lastItem(),
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get bookings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking details
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $booking = RateHawkBooking::with('user')->findOrFail($id);

            $data = [
                'booking' => $booking->toArray(),
                'summary' => $booking->summary,
                'can_cancel' => $booking->canBeCancelled(),
                'is_active' => $booking->isActive(),
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get booking details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel booking
     */
    public function cancel(Request $request, int $id): JsonResponse
    {
        try {
            $booking = RateHawkBooking::findOrFail($id);

            if (!$booking->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This booking cannot be cancelled'
                ], 400);
            }

            $result = $this->bookingService->cancelBooking($booking->order_id);

            return response()->json([
                'success' => true,
                'message' => 'Booking cancelled successfully',
                'data' => $result,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel booking: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking logs
     */
    public function logs(Request $request, int $id): JsonResponse
    {
        try {
            $booking = RateHawkBooking::findOrFail($id);

            $logs = RateHawkApiLog::where('request_id', 'like', '%' . $booking->order_id . '%')
                ->orWhere('endpoint', 'like', '%booking%')
                ->where('created_at', '>=', $booking->created_at->subHours(1))
                ->orderBy('created_at', 'desc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $logs,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get booking logs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', '30'); // days
            $startDate = now()->subDays($period)->startOfDay();

            $stats = [
                'total_bookings' => RateHawkBooking::where('created_at', '>=', $startDate)->count(),
                'confirmed_bookings' => RateHawkBooking::where('created_at', '>=', $startDate)
                    ->where('status', 'confirmed')->count(),
                'cancelled_bookings' => RateHawkBooking::where('created_at', '>=', $startDate)
                    ->where('status', 'cancelled')->count(),
                'failed_bookings' => RateHawkBooking::where('created_at', '>=', $startDate)
                    ->where('status', 'failed')->count(),
                'total_revenue' => RateHawkBooking::where('created_at', '>=', $startDate)
                    ->where('status', 'confirmed')
                    ->sum('total_amount'),
                'avg_booking_value' => RateHawkBooking::where('created_at', '>=', $startDate)
                    ->where('status', 'confirmed')
                    ->avg('total_amount'),
            ];

            // Status distribution
            $statusDistribution = RateHawkBooking::where('created_at', '>=', $startDate)
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            // Daily booking counts
            $dailyBookings = [];
            for ($date = $startDate->copy(); $date->lte(now()); $date->addDay()) {
                $dayStart = $date->copy()->startOfDay();
                $dayEnd = $date->copy()->endOfDay();
                
                $count = RateHawkBooking::whereBetween('created_at', [$dayStart, $dayEnd])->count();
                $dailyBookings[] = [
                    'date' => $date->format('Y-m-d'),
                    'count' => $count
                ];
            }

            $data = [
                'summary' => $stats,
                'status_distribution' => $statusDistribution,
                'daily_bookings' => $dailyBookings,
                'period' => $period,
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get booking statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk operations on bookings
     */
    public function bulkAction(Request $request): JsonResponse
    {
        $request->validate([
            'action' => 'required|in:cancel,check_status,export',
            'booking_ids' => 'required|array|min:1',
            'booking_ids.*' => 'integer|exists:ratehawk_bookings,id'
        ]);

        try {
            $bookingIds = $request->booking_ids;
            $action = $request->action;
            $results = [];

            foreach ($bookingIds as $bookingId) {
                $booking = RateHawkBooking::find($bookingId);
                if (!$booking) {
                    continue;
                }

                switch ($action) {
                    case 'cancel':
                        if ($booking->canBeCancelled()) {
                            try {
                                $this->bookingService->cancelBooking($booking->order_id);
                                $results[] = [
                                    'booking_id' => $bookingId,
                                    'success' => true,
                                    'message' => 'Cancelled successfully'
                                ];
                            } catch (\Exception $e) {
                                $results[] = [
                                    'booking_id' => $bookingId,
                                    'success' => false,
                                    'message' => $e->getMessage()
                                ];
                            }
                        } else {
                            $results[] = [
                                'booking_id' => $bookingId,
                                'success' => false,
                                'message' => 'Cannot be cancelled'
                            ];
                        }
                        break;

                    case 'check_status':
                        try {
                            $this->bookingService->checkBookingStatus($booking->order_id);
                            $results[] = [
                                'booking_id' => $bookingId,
                                'success' => true,
                                'message' => 'Status check initiated'
                            ];
                        } catch (\Exception $e) {
                            $results[] = [
                                'booking_id' => $bookingId,
                                'success' => false,
                                'message' => $e->getMessage()
                            ];
                        }
                        break;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Bulk action completed',
                'data' => $results,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Bulk action failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
