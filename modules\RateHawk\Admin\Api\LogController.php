<?php

namespace Modules\RateHawk\Admin\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Models\RateHawkApiLog;

class LogController extends Controller
{
    /**
     * Get API logs with pagination and filters
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = RateHawkApiLog::with('user');

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('method')) {
                $query->where('method', $request->method);
            }

            if ($request->filled('endpoint')) {
                $query->where('endpoint', 'like', '%' . $request->endpoint . '%');
            }

            if ($request->filled('from_date')) {
                $query->where('created_at', '>=', $request->from_date);
            }

            if ($request->filled('to_date')) {
                $query->where('created_at', '<=', $request->to_date . ' 23:59:59');
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('request_id', 'like', "%{$search}%")
                      ->orWhere('endpoint', 'like', "%{$search}%")
                      ->orWhere('error_message', 'like', "%{$search}%");
                });
            }

            $perPage = $request->input('per_page', 50);
            $logs = $query->orderBy('created_at', 'desc')->paginate($perPage);

            $data = [
                'logs' => $logs->items(),
                'pagination' => [
                    'current_page' => $logs->currentPage(),
                    'last_page' => $logs->lastPage(),
                    'per_page' => $logs->perPage(),
                    'total' => $logs->total(),
                    'from' => $logs->firstItem(),
                    'to' => $logs->lastItem(),
                ]
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get logs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get log details
     */
    public function show(Request $request, int $id): JsonResponse
    {
        try {
            $log = RateHawkApiLog::with('user')->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $log->toArray(),
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get log details: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete log entry
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        try {
            $log = RateHawkApiLog::findOrFail($id);
            $log->delete();

            return response()->json([
                'success' => true,
                'message' => 'Log entry deleted successfully',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete log entry: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear logs
     */
    public function clear(Request $request): JsonResponse
    {
        $request->validate([
            'clear_type' => 'required|in:all,old,errors,completed',
            'days_old' => 'nullable|integer|min:1|max:365',
        ]);

        try {
            $deletedCount = 0;

            switch ($request->clear_type) {
                case 'all':
                    $deletedCount = RateHawkApiLog::count();
                    RateHawkApiLog::truncate();
                    break;

                case 'old':
                    $daysOld = $request->days_old ?? 30;
                    $cutoffDate = now()->subDays($daysOld);
                    $deletedCount = RateHawkApiLog::where('created_at', '<', $cutoffDate)->count();
                    RateHawkApiLog::where('created_at', '<', $cutoffDate)->delete();
                    break;

                case 'errors':
                    $deletedCount = RateHawkApiLog::where('status', 'error')->count();
                    RateHawkApiLog::where('status', 'error')->delete();
                    break;

                case 'completed':
                    $deletedCount = RateHawkApiLog::where('status', 'completed')->count();
                    RateHawkApiLog::where('status', 'completed')->delete();
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => "Cleared {$deletedCount} log entries",
                'deleted_count' => $deletedCount,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear logs: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get log statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', '30'); // days
            $startDate = now()->subDays($period)->startOfDay();

            $stats = [
                'total_requests' => RateHawkApiLog::where('created_at', '>=', $startDate)->count(),
                'successful_requests' => RateHawkApiLog::where('created_at', '>=', $startDate)
                    ->where('status', 'completed')->count(),
                'failed_requests' => RateHawkApiLog::where('created_at', '>=', $startDate)
                    ->where('status', 'error')->count(),
                'pending_requests' => RateHawkApiLog::where('created_at', '>=', $startDate)
                    ->where('status', 'pending')->count(),
                'avg_response_time' => RateHawkApiLog::where('created_at', '>=', $startDate)
                    ->where('status', 'completed')
                    ->avg('duration'),
            ];

            // Method distribution
            $methodDistribution = RateHawkApiLog::where('created_at', '>=', $startDate)
                ->selectRaw('method, COUNT(*) as count')
                ->groupBy('method')
                ->pluck('count', 'method')
                ->toArray();

            // Status distribution
            $statusDistribution = RateHawkApiLog::where('created_at', '>=', $startDate)
                ->selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status')
                ->toArray();

            // Endpoint usage
            $endpointUsage = RateHawkApiLog::where('created_at', '>=', $startDate)
                ->selectRaw('endpoint, COUNT(*) as count')
                ->groupBy('endpoint')
                ->orderByDesc('count')
                ->limit(10)
                ->pluck('count', 'endpoint')
                ->toArray();

            // Daily request counts
            $dailyRequests = [];
            for ($date = $startDate->copy(); $date->lte(now()); $date->addDay()) {
                $dayStart = $date->copy()->startOfDay();
                $dayEnd = $date->copy()->endOfDay();
                
                $count = RateHawkApiLog::whereBetween('created_at', [$dayStart, $dayEnd])->count();
                $errors = RateHawkApiLog::whereBetween('created_at', [$dayStart, $dayEnd])
                    ->where('status', 'error')->count();
                
                $dailyRequests[] = [
                    'date' => $date->format('Y-m-d'),
                    'total' => $count,
                    'errors' => $errors,
                    'success' => $count - $errors,
                ];
            }

            $data = [
                'summary' => $stats,
                'method_distribution' => $methodDistribution,
                'status_distribution' => $statusDistribution,
                'endpoint_usage' => $endpointUsage,
                'daily_requests' => $dailyRequests,
                'period' => $period,
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get log statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get error analysis
     */
    public function errorAnalysis(Request $request): JsonResponse
    {
        try {
            $period = $request->input('period', '7'); // days
            $startDate = now()->subDays($period)->startOfDay();

            $errorLogs = RateHawkApiLog::where('created_at', '>=', $startDate)
                ->where('status', 'error')
                ->get();

            // Group errors by type
            $errorsByType = $errorLogs->groupBy('error_type')->map(function ($errors, $type) {
                return [
                    'type' => $type ?: 'Unknown',
                    'count' => $errors->count(),
                    'percentage' => 0, // Will be calculated below
                    'recent_messages' => $errors->take(5)->pluck('error_message')->unique()->values(),
                ];
            });

            $totalErrors = $errorLogs->count();
            if ($totalErrors > 0) {
                $errorsByType = $errorsByType->map(function ($error) use ($totalErrors) {
                    $error['percentage'] = round(($error['count'] / $totalErrors) * 100, 2);
                    return $error;
                });
            }

            // Group errors by endpoint
            $errorsByEndpoint = $errorLogs->groupBy('endpoint')->map(function ($errors, $endpoint) {
                return [
                    'endpoint' => $endpoint,
                    'count' => $errors->count(),
                    'error_types' => $errors->pluck('error_type')->unique()->filter()->values(),
                ];
            })->sortByDesc('count')->take(10);

            $data = [
                'total_errors' => $totalErrors,
                'errors_by_type' => $errorsByType->values(),
                'errors_by_endpoint' => $errorsByEndpoint->values(),
                'period' => $period,
            ];

            return response()->json([
                'success' => true,
                'data' => $data,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get error analysis: ' . $e->getMessage()
            ], 500);
        }
    }
}
