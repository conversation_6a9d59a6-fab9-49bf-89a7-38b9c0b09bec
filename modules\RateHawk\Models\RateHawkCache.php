<?php

namespace Modules\RateHawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class RateHawkCache extends Model
{
    use HasFactory;

    protected $table = 'ratehawk_cache';

    protected $fillable = [
        'cache_key',
        'cache_type',
        'cache_data',
        'expires_at',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
        'expires_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope for non-expired cache entries
     */
    public function scopeValid($query)
    {
        return $query->where('expires_at', '>', now());
    }

    /**
     * Scope for expired cache entries
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope for specific cache type
     */
    public function scopeType($query, string $type)
    {
        return $query->where('cache_type', $type);
    }

    /**
     * Check if cache entry is expired
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if cache entry is valid
     */
    public function isValid(): bool
    {
        return !$this->isExpired();
    }

    /**
     * Get cached data with automatic JSON decoding
     */
    public function getData()
    {
        $data = $this->cache_data;
        
        if (is_string($data)) {
            $decoded = json_decode($data, true);
            return $decoded !== null ? $decoded : $data;
        }
        
        return $data;
    }

    /**
     * Set cached data with automatic JSON encoding
     */
    public function setData($data): void
    {
        $this->cache_data = is_string($data) ? $data : json_encode($data);
    }

    /**
     * Store cache entry
     */
    public static function store(
        string $key, 
        $data, 
        string $type, 
        int $ttlSeconds, 
        array $metadata = []
    ): self {
        $expiresAt = now()->addSeconds($ttlSeconds);
        
        return static::updateOrCreate(
            ['cache_key' => $key],
            [
                'cache_type' => $type,
                'cache_data' => is_string($data) ? $data : json_encode($data),
                'expires_at' => $expiresAt,
                'metadata' => $metadata,
            ]
        );
    }

    /**
     * Retrieve cache entry by key
     */
    public static function retrieve(string $key)
    {
        $cache = static::where('cache_key', $key)->valid()->first();
        
        if (!$cache) {
            return null;
        }
        
        return $cache->getData();
    }

    /**
     * Check if cache key exists and is valid
     */
    public static function has(string $key): bool
    {
        return static::where('cache_key', $key)->valid()->exists();
    }

    /**
     * Remove cache entry by key
     */
    public static function forget(string $key): bool
    {
        return static::where('cache_key', $key)->delete() > 0;
    }

    /**
     * Clear all cache entries of a specific type
     */
    public static function clearType(string $type): int
    {
        return static::where('cache_type', $type)->delete();
    }

    /**
     * Clear all expired cache entries
     */
    public static function clearExpired(): int
    {
        return static::expired()->delete();
    }

    /**
     * Clear all cache entries
     */
    public static function clearAll(): int
    {
        return static::query()->delete();
    }

    /**
     * Get cache statistics
     */
    public static function getStats(): array
    {
        $total = static::count();
        $valid = static::valid()->count();
        $expired = static::expired()->count();
        
        $typeStats = static::selectRaw('cache_type, COUNT(*) as count')
            ->groupBy('cache_type')
            ->pluck('count', 'cache_type')
            ->toArray();

        return [
            'total' => $total,
            'valid' => $valid,
            'expired' => $expired,
            'by_type' => $typeStats,
            'hit_rate' => $total > 0 ? round(($valid / $total) * 100, 2) : 0,
        ];
    }
}
