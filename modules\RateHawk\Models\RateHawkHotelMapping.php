<?php

namespace Modules\RateHawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Hotel\Models\Hotel;
use Carbon\Carbon;

class RateHawkHotelMapping extends Model
{
    use HasFactory;

    protected $table = 'ratehawk_hotel_mappings';

    protected $fillable = [
        'ratehawk_hotel_id',
        'local_hotel_id',
        'hotel_static_data',
        'amenities_data',
        'images_data',
        'location_data',
        'commission_rate',
        'is_active',
        'last_synced',
        'data_expires_at',
    ];

    protected $casts = [
        'hotel_static_data' => 'array',
        'amenities_data' => 'array',
        'images_data' => 'array',
        'location_data' => 'array',
        'commission_rate' => 'decimal:2',
        'is_active' => 'boolean',
        'last_synced' => 'datetime',
        'data_expires_at' => 'datetime',
    ];

    /**
     * Relationship to local hotel
     */
    public function localHotel()
    {
        return $this->belongsTo(Hotel::class, 'local_hotel_id');
    }

    /**
     * Check if cached data is still valid
     */
    public function isCacheValid(): bool
    {
        if (!$this->data_expires_at) {
            return false;
        }

        return $this->data_expires_at->isFuture();
    }

    /**
     * Check if data needs refresh
     */
    public function needsRefresh(): bool
    {
        if (!$this->last_synced) {
            return true;
        }

        // Refresh if older than 24 hours
        return $this->last_synced->diffInHours(now()) > 24;
    }

    /**
     * Mark data as synced
     */
    public function markAsSynced(): void
    {
        $this->update([
            'last_synced' => now(),
            'data_expires_at' => now()->addHours(24), // Cache for 24 hours
        ]);
    }

    /**
     * Get hotel name from cached data
     */
    public function getHotelNameAttribute(): ?string
    {
        return $this->hotel_static_data['name'] ?? null;
    }

    /**
     * Get hotel location from cached data
     */
    public function getHotelLocationAttribute(): ?string
    {
        $location = $this->location_data;
        if (!$location) {
            return null;
        }

        $parts = array_filter([
            $location['city'] ?? null,
            $location['region'] ?? null,
            $location['country'] ?? null,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get hotel star rating
     */
    public function getStarRatingAttribute(): ?int
    {
        return $this->hotel_static_data['star_rating'] ?? null;
    }

    /**
     * Get hotel amenities list
     */
    public function getAmenitiesListAttribute(): array
    {
        return $this->amenities_data['amenities'] ?? [];
    }

    /**
     * Get hotel images
     */
    public function getImagesListAttribute(): array
    {
        return $this->images_data['images'] ?? [];
    }

    /**
     * Scope for active hotels
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for hotels with valid cache
     */
    public function scopeWithValidCache($query)
    {
        return $query->where('data_expires_at', '>', now());
    }

    /**
     * Scope for hotels needing refresh
     */
    public function scopeNeedsRefresh($query)
    {
        return $query->where(function ($q) {
            $q->whereNull('last_synced')
              ->orWhere('last_synced', '<', now()->subHours(24));
        });
    }

    /**
     * Find by RateHawk hotel ID
     */
    public static function findByRateHawkId(string $rateHawkHotelId): ?self
    {
        return static::where('ratehawk_hotel_id', $rateHawkHotelId)->first();
    }

    /**
     * Create or update mapping
     */
    public static function createOrUpdateMapping(string $rateHawkHotelId, array $data): self
    {
        return static::updateOrCreate(
            ['ratehawk_hotel_id' => $rateHawkHotelId],
            $data
        );
    }

    /**
     * Get popular hotels (most searched/booked)
     */
    public static function getPopularHotels(int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->withValidCache()
            ->orderBy('last_synced', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Search hotels by location
     */
    public static function searchByLocation(string $location, int $limit = 50): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->withValidCache()
            ->whereJsonContains('location_data->city', $location)
            ->orWhereJsonContains('location_data->region', $location)
            ->orWhereJsonContains('location_data->country', $location)
            ->limit($limit)
            ->get();
    }

    /**
     * Get hotels by star rating
     */
    public static function getByStarRating(int $minStars, int $maxStars = 5): \Illuminate\Database\Eloquent\Collection
    {
        return static::active()
            ->withValidCache()
            ->whereJsonBetween('hotel_static_data->star_rating', [$minStars, $maxStars])
            ->get();
    }

    /**
     * Clean up expired cache entries
     */
    public static function cleanupExpiredCache(): int
    {
        return static::where('data_expires_at', '<', now()->subDays(7))->delete();
    }

    /**
     * Get cache statistics
     */
    public static function getCacheStats(): array
    {
        $total = static::count();
        $active = static::active()->count();
        $validCache = static::withValidCache()->count();
        $needsRefresh = static::needsRefresh()->count();

        return [
            'total_mappings' => $total,
            'active_mappings' => $active,
            'valid_cache' => $validCache,
            'needs_refresh' => $needsRefresh,
            'cache_hit_rate' => $total > 0 ? round(($validCache / $total) * 100, 2) : 0,
        ];
    }
}
