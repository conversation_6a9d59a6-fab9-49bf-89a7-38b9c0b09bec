<?php

namespace Modules\RateHawk\Services;

use Modules\RateHawk\Models\RateHawkSetting;
use Modules\RateHawk\Helpers\ConfigHelper;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;

class ConfigurationService
{
    /**
     * Get all configuration settings
     */
    public function getAllSettings(): array
    {
        return ConfigHelper::getAll();
    }

    /**
     * Get configuration status
     */
    public function getStatus(): array
    {
        return ConfigHelper::getStatus();
    }

    /**
     * Update multiple settings
     */
    public function updateSettings(array $settings): array
    {
        $this->validateSettings($settings);
        
        $updated = [];
        $errors = [];

        foreach ($settings as $key => $value) {
            try {
                $type = $this->determineSettingType($key, $value);
                ConfigHelper::set($key, $value, $type);
                $updated[] = $key;
            } catch (\Exception $e) {
                $errors[$key] = $e->getMessage();
            }
        }

        return [
            'updated' => $updated,
            'errors' => $errors,
            'count' => count($updated),
        ];
    }

    /**
     * Update single setting
     */
    public function updateSetting(string $key, $value, string $type = null): void
    {
        $this->validateSetting($key, $value);
        
        if (!$type) {
            $type = $this->determineSettingType($key, $value);
        }
        
        ConfigHelper::set($key, $value, $type);
    }

    /**
     * Reset settings to defaults
     */
    public function resetToDefaults(array $keys = null): array
    {
        $defaultSettings = $this->getDefaultSettings();
        
        if ($keys === null) {
            $keys = array_keys($defaultSettings);
        }

        $reset = [];
        $errors = [];

        foreach ($keys as $key) {
            if (isset($defaultSettings[$key])) {
                try {
                    $value = $defaultSettings[$key]['value'];
                    $type = $defaultSettings[$key]['type'];
                    ConfigHelper::set($key, $value, $type);
                    $reset[] = $key;
                } catch (\Exception $e) {
                    $errors[$key] = $e->getMessage();
                }
            }
        }

        return [
            'reset' => $reset,
            'errors' => $errors,
            'count' => count($reset),
        ];
    }

    /**
     * Test API connection with current settings
     */
    public function testConnection(): array
    {
        if (!ConfigHelper::hasApiCredentials()) {
            return [
                'success' => false,
                'message' => 'API credentials are not configured',
                'data' => null
            ];
        }

        try {
            $apiClient = app(RateHawkApiClient::class);
            return $apiClient->testConnection();
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Validate configuration settings
     */
    public function validateConfiguration(): array
    {
        return ConfigHelper::validate();
    }

    /**
     * Export configuration
     */
    public function exportConfiguration(bool $includeSecrets = false): array
    {
        return ConfigHelper::export($includeSecrets);
    }

    /**
     * Import configuration
     */
    public function importConfiguration(array $config): array
    {
        return ConfigHelper::import($config);
    }

    /**
     * Get configuration schema
     */
    public function getConfigurationSchema(): array
    {
        return [
            'ratehawk_enable' => [
                'type' => 'boolean',
                'default' => true,
                'description' => 'Enable RateHawk API integration',
                'required' => false,
                'group' => 'general',
            ],
            'ratehawk_environment' => [
                'type' => 'string',
                'default' => 'test',
                'description' => 'API environment (test or production)',
                'required' => true,
                'options' => ['test', 'production'],
                'group' => 'api',
            ],
            'ratehawk_key_id' => [
                'type' => 'string',
                'default' => '',
                'description' => 'RateHawk API Key ID',
                'required' => true,
                'sensitive' => true,
                'group' => 'api',
            ],
            'ratehawk_api_key' => [
                'type' => 'string',
                'default' => '',
                'description' => 'RateHawk API Key',
                'required' => true,
                'sensitive' => true,
                'group' => 'api',
            ],
            'ratehawk_default_currency' => [
                'type' => 'string',
                'default' => 'USD',
                'description' => 'Default currency for searches',
                'required' => false,
                'options' => ConfigHelper::getSupportedCurrencies(),
                'group' => 'defaults',
            ],
            'ratehawk_default_language' => [
                'type' => 'string',
                'default' => 'en',
                'description' => 'Default language for searches',
                'required' => false,
                'options' => array_keys(ConfigHelper::getSupportedLanguages()),
                'group' => 'defaults',
            ],
            'ratehawk_default_residency' => [
                'type' => 'string',
                'default' => 'us',
                'description' => 'Default residency country code',
                'required' => false,
                'group' => 'defaults',
            ],
            'ratehawk_cache_enabled' => [
                'type' => 'boolean',
                'default' => true,
                'description' => 'Enable response caching',
                'required' => false,
                'group' => 'performance',
            ],
            'ratehawk_logging_enabled' => [
                'type' => 'boolean',
                'default' => true,
                'description' => 'Enable API request logging',
                'required' => false,
                'group' => 'debugging',
            ],
            'ratehawk_webhooks_enabled' => [
                'type' => 'boolean',
                'default' => true,
                'description' => 'Enable webhook notifications',
                'required' => false,
                'group' => 'notifications',
            ],
            'ratehawk_webhook_secret' => [
                'type' => 'string',
                'default' => '',
                'description' => 'Webhook signature verification secret',
                'required' => false,
                'sensitive' => true,
                'group' => 'notifications',
            ],
        ];
    }

    /**
     * Validate settings array
     */
    protected function validateSettings(array $settings): void
    {
        $rules = [];
        $schema = $this->getConfigurationSchema();

        foreach ($settings as $key => $value) {
            if (isset($schema[$key])) {
                $rules[$key] = $this->getValidationRule($schema[$key]);
            }
        }

        $validator = Validator::make($settings, $rules);

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }
    }

    /**
     * Validate single setting
     */
    protected function validateSetting(string $key, $value): void
    {
        $schema = $this->getConfigurationSchema();
        
        if (isset($schema[$key])) {
            $rule = $this->getValidationRule($schema[$key]);
            $validator = Validator::make([$key => $value], [$key => $rule]);

            if ($validator->fails()) {
                throw new ValidationException($validator);
            }
        }
    }

    /**
     * Get validation rule for setting
     */
    protected function getValidationRule(array $schema): string
    {
        $rules = [];

        if ($schema['required'] ?? false) {
            $rules[] = 'required';
        } else {
            $rules[] = 'nullable';
        }

        switch ($schema['type']) {
            case 'boolean':
                $rules[] = 'boolean';
                break;
            case 'integer':
                $rules[] = 'integer';
                break;
            case 'string':
                $rules[] = 'string';
                if (isset($schema['max_length'])) {
                    $rules[] = 'max:' . $schema['max_length'];
                }
                break;
        }

        if (isset($schema['options'])) {
            $rules[] = 'in:' . implode(',', $schema['options']);
        }

        return implode('|', $rules);
    }

    /**
     * Determine setting type from key and value
     */
    protected function determineSettingType(string $key, $value): string
    {
        $schema = $this->getConfigurationSchema();
        
        if (isset($schema[$key]['type'])) {
            return $schema[$key]['type'];
        }

        // Auto-detect type
        if (is_bool($value)) {
            return 'boolean';
        } elseif (is_int($value)) {
            return 'integer';
        } elseif (is_float($value)) {
            return 'float';
        } elseif (is_array($value)) {
            return 'json';
        }

        return 'string';
    }

    /**
     * Get default settings
     */
    protected function getDefaultSettings(): array
    {
        $schema = $this->getConfigurationSchema();
        $defaults = [];

        foreach ($schema as $key => $config) {
            $defaults[$key] = [
                'value' => $config['default'],
                'type' => $config['type'],
            ];
        }

        return $defaults;
    }
}
