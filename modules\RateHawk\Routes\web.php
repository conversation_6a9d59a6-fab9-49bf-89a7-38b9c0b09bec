<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group.
|
*/

Route::group(['prefix' => 'ratehawk'], function () {
    // Public routes for RateHawk integration
    Route::get('/', 'RateHawkController@index')->name('ratehawk.index');
    Route::post('/search', 'RateHawkController@search')->name('ratehawk.search');
    Route::get('/hotel/{id}', 'RateHawkController@hotel')->name('ratehawk.hotel');
    Route::post('/suggest', 'RateHawkController@suggest')->name('ratehawk.suggest');
    Route::get('/regions', 'RateHawkController@regions')->name('ratehawk.regions');
    Route::post('/regions/search', 'RateHawkController@searchRegions')->name('ratehawk.regions.search');
    Route::post('/prebook', 'RateHawkController@prebook')->name('ratehawk.prebook');


});
