<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;
use Modules\RateHawk\Exceptions\RateHawkApiException;

class StaticContentService
{
    protected $apiClient;
    protected $cacheEnabled;
    protected $cacheTtl;

    public function __construct(RateHawkApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
        $this->cacheEnabled = config('ratehawk.cache.enabled');
        $this->cacheTtl = config('ratehawk.cache.ttl');
    }

    /**
     * Get regions dump
     */
    public function getRegionsDump(): array
    {
        $cacheKey = 'ratehawk_regions_dump';

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/hotel/regions/dump/';
        $response = $this->apiClient->get($endpoint);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl['regions']);
        }

        return $response;
    }

    /**
     * Get hotels dump
     */
    public function getHotelsDump(array $params = []): array
    {
        $cacheKey = 'ratehawk_hotels_dump_' . md5(json_encode($params));

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/hotel/dump/';
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl['hotel_data']);
        }

        return $response;
    }

    /**
     * Get hotel custom dump
     */
    public function getHotelCustomDump(array $params): array
    {
        $endpoint = '/api/b2b/v3/hotel/custom_dump/';
        return $this->apiClient->post($endpoint, $params);
    }

    /**
     * Get hotel incremental dump
     */
    public function getHotelIncrementalDump(array $params): array
    {
        $endpoint = '/api/b2b/v3/hotel/incremental_dump/';
        return $this->apiClient->post($endpoint, $params);
    }

    /**
     * Get hotel reviews dump
     */
    public function getHotelReviewsDump(array $params = []): array
    {
        $endpoint = '/api/b2b/v3/hotel/reviews/dump/';
        return $this->apiClient->post($endpoint, $params);
    }

    /**
     * Get hotel static data
     */
    public function getHotelStaticData(string $hotelId, array $params = []): array
    {
        $cacheKey = 'ratehawk_hotel_static_' . $hotelId . '_' . md5(json_encode($params));

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/hotel/static/';
        $params['id'] = $hotelId;
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl['hotel_data']);
        }

        return $response;
    }

    /**
     * Get hotel content
     */
    public function getHotelContent(string $hotelId, array $params = []): array
    {
        $cacheKey = 'ratehawk_hotel_content_' . $hotelId . '_' . md5(json_encode($params));

        if ($this->cacheEnabled && Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        }

        $endpoint = '/api/b2b/v3/hotel/content/';
        $params['id'] = $hotelId;
        $response = $this->apiClient->post($endpoint, $params);

        if ($this->cacheEnabled) {
            Cache::put($cacheKey, $response, $this->cacheTtl['hotel_data']);
        }

        return $response;
    }

    /**
     * Download and store dump file
     */
    public function downloadDump(string $dumpUrl, string $filename): bool
    {
        try {
            $content = file_get_contents($dumpUrl);
            
            if ($content === false) {
                throw new RateHawkApiException('Failed to download dump file');
            }

            Storage::disk('local')->put('ratehawk/dumps/' . $filename, $content);
            
            return true;
        } catch (\Exception $e) {
            throw new RateHawkApiException('Failed to download dump: ' . $e->getMessage());
        }
    }

    /**
     * Process and decompress dump file
     */
    public function processDumpFile(string $filename): array
    {
        try {
            $filePath = storage_path('app/ratehawk/dumps/' . $filename);
            
            if (!file_exists($filePath)) {
                throw new RateHawkApiException('Dump file not found: ' . $filename);
            }

            // Handle different compression formats
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            
            switch ($extension) {
                case 'gz':
                    $content = gzfile($filePath);
                    break;
                case 'bz2':
                    $content = bzfile($filePath);
                    break;
                case 'json':
                    $content = file_get_contents($filePath);
                    break;
                default:
                    throw new RateHawkApiException('Unsupported dump file format: ' . $extension);
            }

            if (is_array($content)) {
                $content = implode('', $content);
            }

            return json_decode($content, true) ?: [];
            
        } catch (\Exception $e) {
            throw new RateHawkApiException('Failed to process dump file: ' . $e->getMessage());
        }
    }

    /**
     * Get available regions with caching
     */
    public function getRegions(array $filters = []): array
    {
        $regions = $this->getRegionsDump();
        
        if (empty($filters)) {
            return $regions['data'] ?? [];
        }

        $filteredRegions = $regions['data'] ?? [];

        // Apply filters
        if (isset($filters['country_code'])) {
            $filteredRegions = array_filter($filteredRegions, function ($region) use ($filters) {
                return isset($region['country_code']) && 
                       $region['country_code'] === $filters['country_code'];
            });
        }

        if (isset($filters['type'])) {
            $filteredRegions = array_filter($filteredRegions, function ($region) use ($filters) {
                return isset($region['type']) && 
                       $region['type'] === $filters['type'];
            });
        }

        if (isset($filters['search'])) {
            $search = strtolower($filters['search']);
            $filteredRegions = array_filter($filteredRegions, function ($region) use ($search) {
                return strpos(strtolower($region['name'] ?? ''), $search) !== false ||
                       strpos(strtolower($region['full_name'] ?? ''), $search) !== false;
            });
        }

        return array_values($filteredRegions);
    }

    /**
     * Search regions by name
     */
    public function searchRegions(string $query, int $limit = 10): array
    {
        $regions = $this->getRegions(['search' => $query]);
        
        // Sort by relevance (exact matches first, then partial matches)
        usort($regions, function ($a, $b) use ($query) {
            $queryLower = strtolower($query);
            $aName = strtolower($a['name'] ?? '');
            $bName = strtolower($b['name'] ?? '');
            
            $aExact = $aName === $queryLower ? 1 : 0;
            $bExact = $bName === $queryLower ? 1 : 0;
            
            if ($aExact !== $bExact) {
                return $bExact - $aExact;
            }
            
            $aStarts = strpos($aName, $queryLower) === 0 ? 1 : 0;
            $bStarts = strpos($bName, $queryLower) === 0 ? 1 : 0;
            
            if ($aStarts !== $bStarts) {
                return $bStarts - $aStarts;
            }
            
            return strcmp($aName, $bName);
        });

        return array_slice($regions, 0, $limit);
    }

    /**
     * Get region by ID
     */
    public function getRegionById(int $regionId): ?array
    {
        $regions = $this->getRegionsDump();
        
        foreach ($regions['data'] ?? [] as $region) {
            if (isset($region['id']) && $region['id'] == $regionId) {
                return $region;
            }
        }

        return null;
    }

    /**
     * Clear static content cache
     */
    public function clearCache(): void
    {
        if ($this->cacheEnabled) {
            Cache::forget('ratehawk_regions_dump');
            // Clear other static content caches
            $keys = [
                'ratehawk_hotels_dump_*',
                'ratehawk_hotel_static_*',
                'ratehawk_hotel_content_*',
            ];
            
            foreach ($keys as $pattern) {
                Cache::flush(); // In production, use more specific cache clearing
            }
        }
    }
}
