<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class ExtendHotelTablesForRatehawk extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Extend existing hotels table for RateHawk integration
        Schema::table('bravo_hotels', function (Blueprint $table) {
            $table->string('ratehawk_hotel_id')->nullable()->index()->after('id');
            $table->enum('data_source', ['local', 'ratehawk', 'hybrid'])->default('local')->after('ratehawk_hotel_id');
            $table->timestamp('last_api_sync')->nullable()->after('data_source');
            $table->json('ratehawk_data')->nullable()->after('last_api_sync');
            $table->decimal('ratehawk_commission_rate', 5, 2)->nullable()->after('ratehawk_data');
            $table->boolean('is_ratehawk_active')->default(false)->after('ratehawk_commission_rate');
        });

        // Extend existing bookings table for RateHawk bookings
        Schema::table('bravo_bookings', function (Blueprint $table) {
            $table->string('ratehawk_booking_id')->nullable()->index()->after('id');
            $table->string('ratehawk_order_id')->nullable()->after('ratehawk_booking_id');
            $table->json('ratehawk_booking_data')->nullable()->after('ratehawk_order_id');
            $table->enum('booking_source', ['local', 'ratehawk'])->default('local')->after('ratehawk_booking_data');
            $table->string('ratehawk_status')->nullable()->after('booking_source');
            $table->timestamp('ratehawk_last_sync')->nullable()->after('ratehawk_status');
        });

        // Create RateHawk hotel mappings table
        Schema::create('ratehawk_hotel_mappings', function (Blueprint $table) {
            $table->id();
            $table->string('ratehawk_hotel_id')->unique()->index();
            $table->unsignedBigInteger('local_hotel_id')->nullable()->index();
            $table->json('hotel_static_data')->nullable();
            $table->json('amenities_data')->nullable();
            $table->json('images_data')->nullable();
            $table->json('location_data')->nullable();
            $table->decimal('commission_rate', 5, 2)->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_synced')->nullable();
            $table->timestamp('data_expires_at')->nullable();
            $table->timestamps();

            $table->foreign('local_hotel_id')->references('id')->on('bravo_hotels')->onDelete('set null');
            $table->index(['is_active', 'last_synced']);
        });

        // Create search cache table
        Schema::create('ratehawk_search_cache', function (Blueprint $table) {
            $table->id();
            $table->string('search_hash', 64)->unique()->index();
            $table->json('search_params');
            $table->longText('results_data');
            $table->integer('results_count')->default(0);
            $table->timestamp('expires_at')->index();
            $table->integer('hit_count')->default(0);
            $table->timestamp('last_accessed')->nullable();
            $table->timestamps();

            $table->index(['expires_at', 'last_accessed']);
        });

        // Create hotel static data cache table
        Schema::create('ratehawk_hotel_cache', function (Blueprint $table) {
            $table->id();
            $table->string('ratehawk_hotel_id')->index();
            $table->enum('cache_type', ['static', 'amenities', 'images', 'reviews'])->index();
            $table->longText('cache_data');
            $table->timestamp('expires_at')->index();
            $table->integer('hit_count')->default(0);
            $table->timestamp('last_accessed')->nullable();
            $table->timestamps();

            $table->unique(['ratehawk_hotel_id', 'cache_type']);
            $table->index(['expires_at', 'cache_type']);
        });

        // Create pricing cache table
        Schema::create('ratehawk_pricing_cache', function (Blueprint $table) {
            $table->id();
            $table->string('ratehawk_hotel_id')->index();
            $table->string('search_hash', 64)->index();
            $table->date('checkin_date')->index();
            $table->date('checkout_date')->index();
            $table->integer('adults')->default(1);
            $table->integer('children')->default(0);
            $table->json('pricing_data');
            $table->decimal('min_price', 12, 2)->nullable()->index();
            $table->decimal('max_price', 12, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->timestamp('expires_at')->index();
            $table->integer('hit_count')->default(0);
            $table->timestamps();

            $table->index(['ratehawk_hotel_id', 'checkin_date', 'checkout_date']);
            $table->index(['expires_at', 'min_price']);
        });

        // Create destination cache table
        Schema::create('ratehawk_destinations_cache', function (Blueprint $table) {
            $table->id();
            $table->string('destination_id')->unique()->index();
            $table->string('destination_type'); // city, region, country
            $table->string('name');
            $table->string('country_code', 2)->nullable();
            $table->string('region_name')->nullable();
            $table->decimal('latitude', 10, 8)->nullable();
            $table->decimal('longitude', 11, 8)->nullable();
            $table->integer('hotels_count')->default(0);
            $table->json('destination_data')->nullable();
            $table->boolean('is_popular')->default(false);
            $table->timestamp('last_synced')->nullable();
            $table->timestamps();

            $table->index(['destination_type', 'is_popular']);
            $table->index(['country_code', 'name']);
        });

        // Create API performance monitoring table
        Schema::create('ratehawk_api_performance', function (Blueprint $table) {
            $table->id();
            $table->string('endpoint')->index();
            $table->string('method', 10)->default('GET');
            $table->integer('response_time_ms');
            $table->string('status_code', 3);
            $table->boolean('from_cache')->default(false);
            $table->json('request_params')->nullable();
            $table->text('error_message')->nullable();
            $table->timestamp('created_at');

            $table->index(['endpoint', 'created_at']);
            $table->index(['status_code', 'created_at']);
            $table->index(['from_cache', 'response_time_ms']);
        });

        // Create user search history table
        Schema::create('ratehawk_user_searches', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->index();
            $table->string('session_id')->nullable()->index();
            $table->string('destination');
            $table->date('checkin_date');
            $table->date('checkout_date');
            $table->integer('adults')->default(1);
            $table->integer('children')->default(0);
            $table->json('filters_applied')->nullable();
            $table->integer('results_count')->default(0);
            $table->boolean('resulted_in_booking')->default(false);
            $table->string('user_ip', 45)->nullable();
            $table->string('user_agent')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            $table->index(['user_id', 'created_at']);
            $table->index(['destination', 'checkin_date']);
        });

        // Create popular searches tracking table
        Schema::create('ratehawk_popular_searches', function (Blueprint $table) {
            $table->id();
            $table->string('destination')->index();
            $table->string('destination_type'); // city, region, country
            $table->integer('search_count')->default(1);
            $table->integer('booking_count')->default(0);
            $table->decimal('conversion_rate', 5, 2)->default(0);
            $table->date('date')->index();
            $table->timestamps();

            $table->unique(['destination', 'date']);
            $table->index(['search_count', 'date']);
            $table->index(['conversion_rate', 'date']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove columns from existing tables
        Schema::table('bravo_hotels', function (Blueprint $table) {
            $table->dropColumn([
                'ratehawk_hotel_id',
                'data_source',
                'last_api_sync',
                'ratehawk_data',
                'ratehawk_commission_rate',
                'is_ratehawk_active'
            ]);
        });

        Schema::table('bravo_bookings', function (Blueprint $table) {
            $table->dropColumn([
                'ratehawk_booking_id',
                'ratehawk_order_id',
                'ratehawk_booking_data',
                'booking_source',
                'ratehawk_status',
                'ratehawk_last_sync'
            ]);
        });

        // Drop new tables
        Schema::dropIfExists('ratehawk_popular_searches');
        Schema::dropIfExists('ratehawk_user_searches');
        Schema::dropIfExists('ratehawk_api_performance');
        Schema::dropIfExists('ratehawk_destinations_cache');
        Schema::dropIfExists('ratehawk_pricing_cache');
        Schema::dropIfExists('ratehawk_hotel_cache');
        Schema::dropIfExists('ratehawk_search_cache');
        Schema::dropIfExists('ratehawk_hotel_mappings');
    }
}
