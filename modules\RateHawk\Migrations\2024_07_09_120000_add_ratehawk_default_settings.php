<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Core\Models\Settings;

class AddRatehawkDefaultSettings extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Add default RateHawk settings
        $defaultSettings = [
            'ratehawk_enable' => '1',
            'ratehawk_environment' => 'test',
            'ratehawk_default_currency' => 'USD',
            'ratehawk_default_language' => 'en',
            'ratehawk_default_residency' => 'us',
            'ratehawk_cache_enabled' => '1',
            'ratehawk_logging_enabled' => '1',
            'ratehawk_webhooks_enabled' => '1',
            'ratehawk_api_timeout' => '30',
            'ratehawk_hotels_limit' => '50',
            'ratehawk_cache_search_ttl' => '300',
            'ratehawk_cache_hotel_ttl' => '3600',
            'ratehawk_cache_regions_ttl' => '86400',
        ];

        foreach ($defaultSettings as $key => $value) {
            // Only add if setting doesn't already exist
            if (!Settings::where('name', $key)->exists()) {
                Settings::create([
                    'name' => $key,
                    'val' => $value,
                    'group' => 'ratehawk'
                ]);
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // Remove RateHawk settings
        $settingsToRemove = [
            'ratehawk_enable',
            'ratehawk_environment',
            'ratehawk_default_currency',
            'ratehawk_default_language',
            'ratehawk_default_residency',
            'ratehawk_cache_enabled',
            'ratehawk_logging_enabled',
            'ratehawk_webhooks_enabled',
            'ratehawk_api_timeout',
            'ratehawk_hotels_limit',
            'ratehawk_cache_search_ttl',
            'ratehawk_cache_hotel_ttl',
            'ratehawk_cache_regions_ttl',
        ];

        Settings::whereIn('name', $settingsToRemove)->delete();
    }
}
