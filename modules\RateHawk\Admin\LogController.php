<?php

namespace Modules\RateHawk\Admin;

use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Modules\AdminController;
use Modules\RateHawk\Models\RateHawkApiLog;

class LogController extends AdminController
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Display API logs list
     */
    public function index(Request $request): View
    {
        $this->checkPermission('ratehawk_view');

        $query = RateHawkApiLog::with('user');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('method')) {
            $query->where('method', $request->method);
        }

        if ($request->filled('endpoint')) {
            $query->where('endpoint', 'like', '%' . $request->endpoint . '%');
        }

        if ($request->filled('from_date')) {
            $query->where('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->where('created_at', '<=', $request->to_date . ' 23:59:59');
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('request_id', 'like', "%{$search}%")
                  ->orWhere('endpoint', 'like', "%{$search}%")
                  ->orWhere('error_message', 'like', "%{$search}%");
            });
        }

        $logs = $query->orderBy('created_at', 'desc')->paginate(50);

        // Get statistics
        $stats = $this->getLogStats($request);

        $data = [
            'page_title' => __('RateHawk API Logs'),
            'logs' => $logs,
            'stats' => $stats,
            'filters' => $request->only(['status', 'method', 'endpoint', 'from_date', 'to_date', 'search']),
            'statuses' => [
                'pending' => __('Pending'),
                'completed' => __('Completed'),
                'error' => __('Error'),
            ],
            'methods' => ['GET', 'POST', 'PUT', 'DELETE'],
            'breadcrumbs' => [
                ['name' => __('Dashboard'), 'url' => route('admin.index')],
                ['name' => __('RateHawk'), 'url' => route('ratehawk.admin.index')],
                ['name' => __('API Logs'), 'class' => 'active'],
            ],
        ];

        return view('RateHawk::admin.logs.index', $data);
    }

    /**
     * Show log details
     */
    public function show(Request $request, int $id): View
    {
        $this->checkPermission('ratehawk_view');

        $log = RateHawkApiLog::with('user')->findOrFail($id);

        $data = [
            'page_title' => __('API Log Details') . ' - ' . $log->request_id,
            'log' => $log,
            'breadcrumbs' => [
                ['name' => __('Dashboard'), 'url' => route('admin.index')],
                ['name' => __('RateHawk'), 'url' => route('ratehawk.admin.index')],
                ['name' => __('API Logs'), 'url' => route('ratehawk.admin.logs.index')],
                ['name' => $log->request_id, 'class' => 'active'],
            ],
        ];

        return view('RateHawk::admin.logs.show', $data);
    }

    /**
     * Delete log entry
     */
    public function destroy(Request $request, int $id): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        try {
            $log = RateHawkApiLog::findOrFail($id);
            $log->delete();

            return response()->json([
                'success' => true,
                'message' => __('Log entry deleted successfully')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to delete log entry: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear logs
     */
    public function clear(Request $request): JsonResponse
    {
        $this->checkPermission('ratehawk_manage');

        $request->validate([
            'clear_type' => 'required|in:all,old,errors,completed',
            'days_old' => 'nullable|integer|min:1|max:365',
        ]);

        try {
            $deletedCount = 0;

            switch ($request->clear_type) {
                case 'all':
                    $deletedCount = RateHawkApiLog::count();
                    RateHawkApiLog::truncate();
                    break;

                case 'old':
                    $daysOld = $request->days_old ?? 30;
                    $cutoffDate = now()->subDays($daysOld);
                    $deletedCount = RateHawkApiLog::where('created_at', '<', $cutoffDate)->count();
                    RateHawkApiLog::where('created_at', '<', $cutoffDate)->delete();
                    break;

                case 'errors':
                    $deletedCount = RateHawkApiLog::where('status', 'error')->count();
                    RateHawkApiLog::where('status', 'error')->delete();
                    break;

                case 'completed':
                    $deletedCount = RateHawkApiLog::where('status', 'completed')->count();
                    RateHawkApiLog::where('status', 'completed')->delete();
                    break;
            }

            return response()->json([
                'success' => true,
                'message' => __('Cleared :count log entries', ['count' => $deletedCount]),
                'deleted_count' => $deletedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to clear logs: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export logs
     */
    public function export(Request $request)
    {
        $this->checkPermission('ratehawk_view');

        $query = RateHawkApiLog::with('user');

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('method')) {
            $query->where('method', $request->method);
        }

        if ($request->filled('endpoint')) {
            $query->where('endpoint', 'like', '%' . $request->endpoint . '%');
        }

        if ($request->filled('from_date')) {
            $query->where('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->where('created_at', '<=', $request->to_date . ' 23:59:59');
        }

        $logs = $query->orderBy('created_at', 'desc')->get();

        $filename = 'ratehawk_api_logs_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($logs) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Request ID',
                'Method',
                'Endpoint',
                'Status',
                'Status Code',
                'Duration (s)',
                'Error Type',
                'Error Message',
                'User Email',
                'IP Address',
                'Created At'
            ]);

            foreach ($logs as $log) {
                fputcsv($file, [
                    $log->request_id,
                    $log->method,
                    $log->endpoint,
                    $log->status,
                    $log->status_code,
                    $log->duration,
                    $log->error_type,
                    $log->error_message,
                    $log->user?->email,
                    $log->ip_address,
                    $log->created_at->format('Y-m-d H:i:s')
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get log statistics
     */
    protected function getLogStats(Request $request): array
    {
        $query = RateHawkApiLog::query();

        // Apply same date filters
        if ($request->filled('from_date')) {
            $query->where('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->where('created_at', '<=', $request->to_date . ' 23:59:59');
        }

        $total = $query->count();
        $completed = (clone $query)->where('status', 'completed')->count();
        $errors = (clone $query)->where('status', 'error')->count();
        $pending = (clone $query)->where('status', 'pending')->count();

        $avgDuration = (clone $query)->where('status', 'completed')->avg('duration');

        return [
            'total' => $total,
            'completed' => $completed,
            'errors' => $errors,
            'pending' => $pending,
            'success_rate' => $total > 0 ? round(($completed / $total) * 100, 2) : 0,
            'error_rate' => $total > 0 ? round(($errors / $total) * 100, 2) : 0,
            'avg_duration' => $avgDuration ? round($avgDuration, 3) : 0,
        ];
    }
}
