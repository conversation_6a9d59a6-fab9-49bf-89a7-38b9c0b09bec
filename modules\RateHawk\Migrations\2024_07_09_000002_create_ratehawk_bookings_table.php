<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ratehawk_bookings', function (Blueprint $table) {
            $table->id();
            $table->string('order_id')->unique()->index();
            $table->string('partner_order_id')->nullable()->index();
            $table->string('search_hash');
            $table->string('match_hash');
            $table->string('book_hash')->nullable();
            $table->enum('status', [
                'created', 'started', 'confirmed', 'cancelled', 
                'completed', 'failed', 'pending_payment'
            ])->default('created')->index();
            
            // Hotel and room information
            $table->json('hotel_data')->nullable();
            $table->json('room_data')->nullable();
            $table->json('price_data')->nullable();
            $table->json('guest_data')->nullable();
            $table->json('payment_data')->nullable();
            $table->json('api_response')->nullable();
            
            // User information
            $table->unsignedBigInteger('user_id')->nullable();
            $table->string('user_ip', 45)->nullable();
            
            // Booking dates
            $table->date('checkin_date')->nullable()->index();
            $table->date('checkout_date')->nullable()->index();
            
            // Financial information
            $table->decimal('total_amount', 10, 2)->nullable();
            $table->string('currency', 3)->nullable();
            $table->decimal('commission_amount', 10, 2)->nullable();
            
            // Important dates
            $table->timestamp('cancellation_deadline')->nullable();
            
            // Document URLs
            $table->string('voucher_url')->nullable();
            $table->string('invoice_url')->nullable();
            
            // Additional notes
            $table->text('notes')->nullable();
            
            $table->timestamps();

            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['user_id', 'created_at']);
            $table->index(['checkin_date', 'checkout_date']);
            $table->index(['total_amount', 'currency']);
            $table->index('created_at');

            // Foreign key
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ratehawk_bookings');
    }
};
