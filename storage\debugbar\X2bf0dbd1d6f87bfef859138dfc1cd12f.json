{"__meta": {"id": "X2bf0dbd1d6f87bfef859138dfc1cd12f", "datetime": "2025-07-10 21:29:28", "utime": **********.261173, "method": "GET", "uri": "/my-bookings", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752182966.298606, "end": **********.261234, "duration": 1.9626281261444092, "duration_str": "1.96s", "measures": [{"label": "Booting", "start": 1752182966.298606, "relative_start": 0, "end": 1752182967.590636, "relative_end": 1752182967.590636, "duration": 1.2920300960540771, "duration_str": "1.29s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752182967.59074, "relative_start": 1.2921340465545654, "end": **********.26124, "relative_end": 5.9604644775390625e-06, "duration": 0.6705000400543213, "duration_str": "671ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6701248, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 17, "templates": [{"name": "1x frontend.booking-history", "param_count": null, "params": [], "start": 1752182967.717974, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/frontend/booking-history.blade.phpfrontend.booking-history", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Ffrontend%2Fbooking-history.blade.php&line=1", "ajax": false, "filename": "booking-history.blade.php", "line": "?"}, "render_count": 1, "name_original": "frontend.booking-history"}, {"name": "1x Layout::app", "param_count": null, "params": [], "start": 1752182967.737742, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/app.blade.phpLayout::app", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::app"}, {"name": "1x Layout::parts.favicon", "param_count": null, "params": [], "start": 1752182967.741138, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/favicon.blade.phpLayout::parts.favicon", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffavicon.blade.php&line=1", "ajax": false, "filename": "favicon.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.favicon"}, {"name": "1x Layout::parts.seo-meta", "param_count": null, "params": [], "start": 1752182967.748539, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/seo-meta.blade.phpLayout::parts.seo-meta", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fseo-meta.blade.php&line=1", "ajax": false, "filename": "seo-meta.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.seo-meta"}, {"name": "1x Layout::parts.global-script", "param_count": null, "params": [], "start": 1752182967.764382, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/global-script.blade.phpLayout::parts.global-script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Fglobal-script.blade.php&line=1", "ajax": false, "filename": "global-script.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.global-script"}, {"name": "1x Layout::parts.topbar", "param_count": null, "params": [], "start": 1752182967.877679, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/topbar.blade.phpLayout::parts.topbar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Ftopbar.blade.php&line=1", "ajax": false, "filename": "topbar.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.topbar"}, {"name": "2x Core::frontend.currency-switcher", "param_count": null, "params": [], "start": 1752182967.915613, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Core/Views/frontend/currency-switcher.blade.phpCore::frontend.currency-switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FCore%2FViews%2Ffrontend%2Fcurrency-switcher.blade.php&line=1", "ajax": false, "filename": "currency-switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Core::frontend.currency-switcher"}, {"name": "2x Language::frontend.switcher", "param_count": null, "params": [], "start": 1752182967.931116, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Language/Views/frontend/switcher.blade.phpLanguage::frontend.switcher", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLanguage%2FViews%2Ffrontend%2Fswitcher.blade.php&line=1", "ajax": false, "filename": "switcher.blade.php", "line": "?"}, "render_count": 2, "name_original": "Language::frontend.switcher"}, {"name": "1x Layout::parts.header", "param_count": null, "params": [], "start": 1752182967.938204, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/BC/Layout/parts/header.blade.phpLayout::parts.header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBC%2FLayout%2Fparts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.header"}, {"name": "1x Layout::parts.footer", "param_count": null, "params": [], "start": **********.155375, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/footer.blade.phpLayout::parts.footer", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.footer"}, {"name": "1x Layout::parts.login-register-modal", "param_count": null, "params": [], "start": **********.182291, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/parts/login-register-modal.blade.phpLayout::parts.login-register-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fparts%2Flogin-register-modal.blade.php&line=1", "ajax": false, "filename": "login-register-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::parts.login-register-modal"}, {"name": "1x Layout::auth.login-form", "param_count": null, "params": [], "start": **********.184943, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/login-form.blade.phpLayout::auth.login-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Flogin-form.blade.php&line=1", "ajax": false, "filename": "login-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.login-form"}, {"name": "1x Layout::auth.register-form", "param_count": null, "params": [], "start": **********.198158, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\modules/Layout/auth/register-form.blade.phpLayout::auth.register-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLayout%2Fauth%2Fregister-form.blade.php&line=1", "ajax": false, "filename": "register-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "Layout::auth.register-form"}, {"name": "1x Popup::frontend.popup", "param_count": null, "params": [], "start": **********.20779, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\themes/Base/Popup/Views/frontend/popup.blade.phpPopup::frontend.popup", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fthemes%2FBase%2FPopup%2FViews%2Ffrontend%2Fpopup.blade.php&line=1", "ajax": false, "filename": "popup.blade.php", "line": "?"}, "render_count": 1, "name_original": "Popup::frontend.popup"}, {"name": "1x demo_script", "param_count": null, "params": [], "start": **********.231896, "type": "blade", "hash": "bladeC:\\wamp64\\www\\mazar\\resources\\views/demo_script.blade.phpdemo_script", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fresources%2Fviews%2Fdemo_script.blade.php&line=1", "ajax": false, "filename": "demo_script.blade.php", "line": "?"}, "render_count": 1, "name_original": "demo_script"}]}, "route": {"uri": "GET my-bookings", "middleware": "web", "uses": "Closure() {#1490\n  class: \"App\\Providers\\RouteServiceProvider\"\n  this: App\\Providers\\RouteServiceProvider {#355 …}\n  file: \"C:\\wamp64\\www\\mazar\\routes\\web.php\"\n  line: \"90 to 100\"\n}", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "bookings.history", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Froutes%2Fweb.php&line=90\" onclick=\"\">routes/web.php:90-100</a>"}, "queries": {"nb_statements": 2, "nb_failed_statements": 0, "accumulated_duration": 0.015, "accumulated_duration_str": "15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": 1752182967.9654229, "duration": 0.01283, "duration_str": "12.83ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}, {"sql": "select * from `core_menu_translations` where `core_menu_translations`.`origin_id` = 1 and `core_menu_translations`.`origin_id` is not null and `locale` = 'en' limit 1", "type": "query", "params": [], "bindings": ["1", "en"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Traits/HasTranslations.php", "file": "C:\\wamp64\\www\\mazar\\app\\Traits\\HasTranslations.php", "line": 51}, {"index": 22, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 111}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.0930989, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "HasTranslations.php:51", "source": "app/Traits/HasTranslations.php:51", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FTraits%2FHasTranslations.php&line=51", "ajax": false, "filename": "HasTranslations.php", "line": "51"}, "connection": "mazar_travel"}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "6ufzaMsoz1wQfcve3urTCIGuEQLAfvh50hFmGTbV", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/my-bookings\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/my-bookings", "status_code": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1677556309 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">curl/8.9.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1677556309\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-971180320 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-971180320\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1776883967 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 21:29:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkkzcU84eXJiSk5RTGs2RmJDMW5VelE9PSIsInZhbHVlIjoiamF4RzJpeWxva3pjWThXS3BKNHlYVlFuYkRjeERZblZSRDBzZXF3Ris0TVpiaDBLMnRLanRycjk5YldweU9nczROd3NkNUZCUHJQWlVvR3pDUjFOTm9tRkVya0ljcW05WVU3M29WdjFFMG5HUlBheHh0eU1xMGR2UElHVEVMNXIiLCJtYWMiOiJjMjc0MzUyOGFlNjVjNzhhNDgyNmJjYmZkYTk3NmUxNDI1YTBlNWZkMmNmYTcyMjliNmY0ZDkyNGJhYjk0ZjY2IiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:29:28 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IjNCWnZPSnlEMGpwV2JGRG83bTN0NVE9PSIsInZhbHVlIjoiYVQxZGtMMW1LTEJuL2UyYnFzMnhGRm1RZGVLZXRVNGdKMEdWSzc1cTBTeEJLQzJvNzNVWk5vNXhPaUFNRUdoRzVNeTYwK015Q0oyRlE3K3RoWEdHZlkrTElESjRobE4wYXVpd01KYmNkYjdVZFNiNVFjWlpIcWhBR0M2OHpkRm4iLCJtYWMiOiJjZTNhNTk3ZGZiYTE1MTM1ODk1NTgyMDk3NDdlNWEzMDMzNDY2ODc5ZmYzOTRjMDIxZDQ3NTkxZmY4MmZhYjA0IiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:29:28 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkkzcU84eXJiSk5RTGs2RmJDMW5VelE9PSIsInZhbHVlIjoiamF4RzJpeWxva3pjWThXS3BKNHlYVlFuYkRjeERZblZSRDBzZXF3Ris0TVpiaDBLMnRLanRycjk5YldweU9nczROd3NkNUZCUHJQWlVvR3pDUjFOTm9tRkVya0ljcW05WVU3M29WdjFFMG5HUlBheHh0eU1xMGR2UElHVEVMNXIiLCJtYWMiOiJjMjc0MzUyOGFlNjVjNzhhNDgyNmJjYmZkYTk3NmUxNDI1YTBlNWZkMmNmYTcyMjliNmY0ZDkyNGJhYjk0ZjY2IiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:29:28 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IjNCWnZPSnlEMGpwV2JGRG83bTN0NVE9PSIsInZhbHVlIjoiYVQxZGtMMW1LTEJuL2UyYnFzMnhGRm1RZGVLZXRVNGdKMEdWSzc1cTBTeEJLQzJvNzNVWk5vNXhPaUFNRUdoRzVNeTYwK015Q0oyRlE3K3RoWEdHZlkrTElESjRobE4wYXVpd01KYmNkYjdVZFNiNVFjWlpIcWhBR0M2OHpkRm4iLCJtYWMiOiJjZTNhNTk3ZGZiYTE1MTM1ODk1NTgyMDk3NDdlNWEzMDMzNDY2ODc5ZmYzOTRjMDIxZDQ3NTkxZmY4MmZhYjA0IiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:29:28 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1776883967\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2005609410 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6ufzaMsoz1wQfcve3urTCIGuEQLAfvh50hFmGTbV</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"33 characters\">http://127.0.0.1:8000/my-bookings</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2005609410\", {\"maxDepth\":0})</script>\n"}}