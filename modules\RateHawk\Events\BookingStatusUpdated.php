<?php

namespace Modules\RateHawk\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Modules\RateHawk\Models\RateHawkBooking;

class BookingStatusUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $booking;
    public $newStatus;
    public $bookingData;

    /**
     * Create a new event instance.
     */
    public function __construct(RateHawkBooking $booking, string $newStatus, array $bookingData = [])
    {
        $this->booking = $booking;
        $this->newStatus = $newStatus;
        $this->bookingData = $bookingData;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return \Illuminate\Broadcasting\Channel|array
     */
    public function broadcastOn()
    {
        return new PrivateChannel('ratehawk-booking.' . $this->booking->order_id);
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith()
    {
        return [
            'booking_id' => $this->booking->order_id,
            'status' => $this->newStatus,
            'booking_data' => $this->bookingData,
            'timestamp' => now()->toISOString()
        ];
    }
}
