<?php

namespace Modules\RateHawk\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Http\JsonResponse;
use Modules\Admin\Controllers\Controller;
use Modules\RateHawk\Services\BookingService;
use Modules\RateHawk\Models\RateHawkBooking;
use Modules\RateHawk\Jobs\ProcessBookingOperation;

class BookingController extends Controller
{
    protected $bookingService;

    public function __construct(BookingService $bookingService)
    {
        parent::__construct();
        $this->bookingService = $bookingService;
    }

    /**
     * Display bookings list
     */
    public function index(Request $request): View
    {
        $this->checkPermission('ratehawk_bookings_view');

        $query = RateHawkBooking::with('user');

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('from_date')) {
            $query->where('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->where('created_at', '<=', $request->to_date . ' 23:59:59');
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('order_id', 'like', "%{$search}%")
                  ->orWhere('partner_order_id', 'like', "%{$search}%")
                  ->orWhereJsonContains('hotel_data->name', $search)
                  ->orWhereJsonContains('guest_data', function ($query) use ($search) {
                      $query->where('first_name', 'like', "%{$search}%")
                            ->orWhere('last_name', 'like', "%{$search}%");
                  });
            });
        }

        $bookings = $query->orderBy('created_at', 'desc')->paginate(20);

        $data = [
            'page_title' => __('RateHawk Bookings'),
            'bookings' => $bookings,
            'filters' => $request->only(['status', 'from_date', 'to_date', 'search']),
            'statuses' => [
                'created' => __('Created'),
                'started' => __('Started'),
                'confirmed' => __('Confirmed'),
                'cancelled' => __('Cancelled'),
                'completed' => __('Completed'),
                'failed' => __('Failed'),
            ],
            'breadcrumbs' => [
                ['name' => __('Dashboard'), 'url' => route('admin.index')],
                ['name' => __('RateHawk'), 'url' => route('ratehawk.admin.index')],
                ['name' => __('Bookings'), 'class' => 'active'],
            ],
        ];

        return view('RateHawk::admin.bookings.index', $data);
    }

    /**
     * Show booking details
     */
    public function show(Request $request, int $id): View
    {
        $this->checkPermission('ratehawk_bookings_view');

        $booking = RateHawkBooking::with('user', 'apiLogs')->findOrFail($id);

        $data = [
            'page_title' => __('Booking Details') . ' - ' . $booking->order_id,
            'booking' => $booking,
            'breadcrumbs' => [
                ['name' => __('Dashboard'), 'url' => route('admin.index')],
                ['name' => __('RateHawk'), 'url' => route('ratehawk.admin.index')],
                ['name' => __('Bookings'), 'url' => route('ratehawk.admin.bookings.index')],
                ['name' => $booking->order_id, 'class' => 'active'],
            ],
        ];

        return view('RateHawk::admin.bookings.show', $data);
    }

    /**
     * Cancel booking
     */
    public function cancel(Request $request, int $id): JsonResponse
    {
        $this->checkPermission('ratehawk_bookings_cancel');

        try {
            $booking = RateHawkBooking::findOrFail($id);

            if (!$booking->canBeCancelled()) {
                return response()->json([
                    'success' => false,
                    'message' => __('This booking cannot be cancelled')
                ], 400);
            }

            // Queue the cancellation job
            ProcessBookingOperation::dispatch('cancel', $booking->order_id);

            return response()->json([
                'success' => true,
                'message' => __('Booking cancellation has been queued for processing')
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to cancel booking: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Failed to cancel booking: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Check booking status
     */
    public function checkStatus(Request $request, int $id): JsonResponse
    {
        $this->checkPermission('ratehawk_bookings_view');

        try {
            $booking = RateHawkBooking::findOrFail($id);

            // Queue the status check job
            ProcessBookingOperation::dispatch('check_status', $booking->order_id);

            return response()->json([
                'success' => true,
                'message' => __('Status check has been queued for processing')
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to check booking status: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Failed to check booking status: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking voucher
     */
    public function getVoucher(Request $request, int $id): JsonResponse
    {
        $this->checkPermission('ratehawk_bookings_view');

        try {
            $booking = RateHawkBooking::findOrFail($id);

            if ($booking->voucher_url) {
                return response()->json([
                    'success' => true,
                    'voucher_url' => $booking->voucher_url,
                    'message' => __('Voucher is available')
                ]);
            }

            // Queue the voucher retrieval job
            ProcessBookingOperation::dispatch('get_voucher', $booking->order_id);

            return response()->json([
                'success' => true,
                'message' => __('Voucher retrieval has been queued for processing')
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get booking voucher: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Failed to get booking voucher: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get booking invoice
     */
    public function getInvoice(Request $request, int $id): JsonResponse
    {
        $this->checkPermission('ratehawk_bookings_view');

        try {
            $booking = RateHawkBooking::findOrFail($id);

            if ($booking->invoice_url) {
                return response()->json([
                    'success' => true,
                    'invoice_url' => $booking->invoice_url,
                    'message' => __('Invoice is available')
                ]);
            }

            // Queue the invoice retrieval job
            ProcessBookingOperation::dispatch('get_invoice', $booking->order_id);

            return response()->json([
                'success' => true,
                'message' => __('Invoice retrieval has been queued for processing')
            ]);

        } catch (\Exception $e) {
            \Log::error('Failed to get booking invoice: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => __('Failed to get booking invoice: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update booking notes
     */
    public function updateNotes(Request $request, int $id): JsonResponse
    {
        $this->checkPermission('ratehawk_bookings_view');

        $request->validate([
            'notes' => 'nullable|string|max:1000'
        ]);

        try {
            $booking = RateHawkBooking::findOrFail($id);
            $booking->update(['notes' => $request->notes]);

            return response()->json([
                'success' => true,
                'message' => __('Notes updated successfully')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('Failed to update notes: ') . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export bookings
     */
    public function export(Request $request)
    {
        $this->checkPermission('ratehawk_bookings_view');

        $query = RateHawkBooking::with('user');

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('from_date')) {
            $query->where('created_at', '>=', $request->from_date);
        }

        if ($request->filled('to_date')) {
            $query->where('created_at', '<=', $request->to_date . ' 23:59:59');
        }

        $bookings = $query->orderBy('created_at', 'desc')->get();

        $filename = 'ratehawk_bookings_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function () use ($bookings) {
            $file = fopen('php://output', 'w');
            
            // CSV headers
            fputcsv($file, [
                'Order ID',
                'Partner Order ID',
                'Status',
                'Hotel Name',
                'Room Name',
                'Guest Name',
                'Check-in',
                'Check-out',
                'Total Amount',
                'Currency',
                'Created At',
                'User Email'
            ]);

            foreach ($bookings as $booking) {
                fputcsv($file, [
                    $booking->order_id,
                    $booking->partner_order_id,
                    $booking->status,
                    $booking->hotel_name,
                    $booking->room_name,
                    $booking->primary_guest_name,
                    $booking->checkin_date?->format('Y-m-d'),
                    $booking->checkout_date?->format('Y-m-d'),
                    $booking->total_amount,
                    $booking->currency,
                    $booking->created_at->format('Y-m-d H:i:s'),
                    $booking->user?->email
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
