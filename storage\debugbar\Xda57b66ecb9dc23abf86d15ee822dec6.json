{"__meta": {"id": "Xda57b66ecb9dc23abf86d15ee822dec6", "datetime": "2025-07-10 21:37:57", "utime": 1752183477.644078, "method": "GET", "uri": "/api/ratehawk-booking/history?email=<EMAIL>", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752183476.710962, "end": 1752183477.6441, "duration": 0.9331378936767578, "duration_str": "933ms", "measures": [{"label": "Booting", "start": 1752183476.710962, "relative_start": 0, "end": 1752183477.166883, "relative_end": 1752183477.166883, "duration": 0.455920934677124, "duration_str": "456ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752183477.166901, "relative_start": 0.45593905448913574, "end": 1752183477.644103, "relative_end": 3.0994415283203125e-06, "duration": 0.4772019386291504, "duration_str": "477ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5636872, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/ratehawk-booking/history", "middleware": "web", "controller": "App\\Http\\Controllers\\BookingController@getBookingHistory", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "api.ratehawk.booking.history", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=208\" onclick=\"\">app/Http/Controllers/BookingController.php:208-273</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "aZBQTXrNO6dKTCYrH9oYoqd2tQgdBq9GibkqyekY", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/api/ratehawk-booking/history?email=test%40example.com\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/api/ratehawk-booking/history", "status_code": "<pre class=sf-dump id=sf-dump-530266852 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-530266852\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1388964782 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1388964782\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1327200784 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1327200784\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1650144429 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">curl/8.9.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1650144429\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1348404416 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1348404416\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 21:37:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik5vekdnSmtzVFAvUnRmWHV3VldYWHc9PSIsInZhbHVlIjoiL0RVaW9TbFM2SVpPcFNNTWFTbUgwdnhkcm5jbk40aldvYTFPK243NTFpcnhvQlh6VHVHUCtqTWFsV1l6ZnB6T1hhOXN2YitVWlEyWExGUk45WFVHTkZyV3p1bTFBR0toSWEycjIvelRzTy9XeUtQVEhvK05SVW1vNTlIUzRhTnUiLCJtYWMiOiJiZjk5NDE0YTZjMThkYmVmMDQ0MTE2ZmFkOGM4NmExMTU4NzNkOTIxYjZhOTc3NjExZmRmZTUwY2RhN2JmZTNkIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:37:57 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IkxqYXBJaWg3Unh5MDJWbUxtYnh6MFE9PSIsInZhbHVlIjoiM1NBM0tZc2hVb093eFZZS3pnZDlLaFJWalBvUGZHVTNPcHM2MjIzaTQrUnpMWmdCVXlJWS9TU3QvL0UyYmJFZUE0VFFSUXc2M1ZybnAvb0VmZW5TaytONHFjRnFSYXVmWFB5MkkrZUs0d2VwSGM3WEtyMnkycWY3T0FjOHozQVQiLCJtYWMiOiIwZTkxNzZmOWI2ZGNiODUwMjM3YmRiNzBlYTkxMjMxNTI1NDc0M2Y2OGQ0NjM0MGUxZjI1ODlkZGMxMzlkNzM0IiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:37:57 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik5vekdnSmtzVFAvUnRmWHV3VldYWHc9PSIsInZhbHVlIjoiL0RVaW9TbFM2SVpPcFNNTWFTbUgwdnhkcm5jbk40aldvYTFPK243NTFpcnhvQlh6VHVHUCtqTWFsV1l6ZnB6T1hhOXN2YitVWlEyWExGUk45WFVHTkZyV3p1bTFBR0toSWEycjIvelRzTy9XeUtQVEhvK05SVW1vNTlIUzRhTnUiLCJtYWMiOiJiZjk5NDE0YTZjMThkYmVmMDQ0MTE2ZmFkOGM4NmExMTU4NzNkOTIxYjZhOTc3NjExZmRmZTUwY2RhN2JmZTNkIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:37:57 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IkxqYXBJaWg3Unh5MDJWbUxtYnh6MFE9PSIsInZhbHVlIjoiM1NBM0tZc2hVb093eFZZS3pnZDlLaFJWalBvUGZHVTNPcHM2MjIzaTQrUnpMWmdCVXlJWS9TU3QvL0UyYmJFZUE0VFFSUXc2M1ZybnAvb0VmZW5TaytONHFjRnFSYXVmWFB5MkkrZUs0d2VwSGM3WEtyMnkycWY3T0FjOHozQVQiLCJtYWMiOiIwZTkxNzZmOWI2ZGNiODUwMjM3YmRiNzBlYTkxMjMxNTI1NDc0M2Y2OGQ0NjM0MGUxZjI1ODlkZGMxMzlkNzM0IiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:37:57 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1884545127 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">aZBQTXrNO6dKTCYrH9oYoqd2tQgdBq9GibkqyekY</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"75 characters\">http://127.0.0.1:8000/api/ratehawk-booking/history?email=test%40example.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1884545127\", {\"maxDepth\":0})</script>\n"}}