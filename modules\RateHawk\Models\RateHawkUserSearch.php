<?php

namespace Modules\RateHawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\User;
use Carbon\Carbon;

class RateHawkUserSearch extends Model
{
    use HasFactory;

    protected $table = 'ratehawk_user_searches';

    protected $fillable = [
        'user_id',
        'session_id',
        'destination',
        'checkin_date',
        'checkout_date',
        'adults',
        'children',
        'filters_applied',
        'results_count',
        'resulted_in_booking',
        'user_ip',
        'user_agent',
    ];

    protected $casts = [
        'checkin_date' => 'date',
        'checkout_date' => 'date',
        'adults' => 'integer',
        'children' => 'integer',
        'filters_applied' => 'array',
        'results_count' => 'integer',
        'resulted_in_booking' => 'boolean',
    ];

    /**
     * Relationship to user
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Record a new search
     */
    public static function recordSearch(array $searchData): self
    {
        return static::create([
            'user_id' => auth()->id(),
            'session_id' => session()->getId(),
            'destination' => $searchData['destination'],
            'checkin_date' => $searchData['checkin'],
            'checkout_date' => $searchData['checkout'],
            'adults' => $searchData['adults'] ?? 1,
            'children' => $searchData['children'] ?? 0,
            'filters_applied' => $searchData['filters'] ?? [],
            'results_count' => $searchData['results_count'] ?? 0,
            'user_ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Mark search as resulted in booking
     */
    public function markAsBooked(): void
    {
        $this->update(['resulted_in_booking' => true]);
    }

    /**
     * Get popular destinations
     */
    public static function getPopularDestinations(int $limit = 10): array
    {
        return static::selectRaw('destination, COUNT(*) as search_count')
            ->where('created_at', '>=', now()->subDays(30))
            ->groupBy('destination')
            ->orderBy('search_count', 'desc')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    /**
     * Get search trends
     */
    public static function getSearchTrends(int $days = 7): array
    {
        return static::selectRaw('DATE(created_at) as date, COUNT(*) as searches')
            ->where('created_at', '>=', now()->subDays($days))
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->toArray();
    }

    /**
     * Get conversion rate
     */
    public static function getConversionRate(): float
    {
        $totalSearches = static::count();
        $bookingSearches = static::where('resulted_in_booking', true)->count();

        return $totalSearches > 0 ? round(($bookingSearches / $totalSearches) * 100, 2) : 0;
    }

    /**
     * Scope for recent searches
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope for searches by user
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for searches that resulted in booking
     */
    public function scopeWithBooking($query)
    {
        return $query->where('resulted_in_booking', true);
    }

    /**
     * Get user's search history
     */
    public static function getUserSearchHistory(int $userId, int $limit = 20): \Illuminate\Database\Eloquent\Collection
    {
        return static::byUser($userId)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean up old search records
     */
    public static function cleanupOldSearches(int $daysToKeep = 90): int
    {
        return static::where('created_at', '<', now()->subDays($daysToKeep))->delete();
    }
}
