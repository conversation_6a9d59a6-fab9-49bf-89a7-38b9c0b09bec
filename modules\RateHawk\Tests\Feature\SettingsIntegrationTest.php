<?php

namespace Modules\RateHawk\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Modules\Core\Models\Settings;
use Modules\RateHawk\SettingClass;

class SettingsIntegrationTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_get_setting_pages_configuration()
    {
        // Act
        $settingPages = SettingClass::getSettingPages();

        // Assert
        $this->assertIsArray($settingPages);
        $this->assertCount(1, $settingPages);
        
        $rateHawkSettings = $settingPages[0];
        $this->assertEquals('ratehawk', $rateHawkSettings['id']);
        $this->assertEquals('RateHawk API', $rateHawkSettings['title']);
        $this->assertEquals('RateHawk::admin.settings.ratehawk', $rateHawkSettings['view']);
        $this->assertArrayHasKey('keys', $rateHawkSettings);
        $this->assertContains('ratehawk_enable', $rateHawkSettings['keys']);
        $this->assertContains('ratehawk_api_key', $rateHawkSettings['keys']);
    }

    /** @test */
    public function it_has_proper_validation_rules()
    {
        // Act
        $rules = SettingClass::getValidationRules();

        // Assert
        $this->assertIsArray($rules);
        $this->assertArrayHasKey('ratehawk_enable', $rules);
        $this->assertArrayHasKey('ratehawk_environment', $rules);
        $this->assertArrayHasKey('ratehawk_key_id', $rules);
        $this->assertArrayHasKey('ratehawk_api_key', $rules);
        
        // Check specific validation rules
        $this->assertEquals('nullable|in:0,1', $rules['ratehawk_enable']);
        $this->assertEquals('nullable|in:test,production', $rules['ratehawk_environment']);
        $this->assertEquals('nullable|string|size:3', $rules['ratehawk_default_currency']);
    }

    /** @test */
    public function it_has_setting_defaults()
    {
        // Act
        $defaults = SettingClass::getSettingDefaults();

        // Assert
        $this->assertIsArray($defaults);
        $this->assertEquals('0', $defaults['ratehawk_enable']);
        $this->assertEquals('test', $defaults['ratehawk_environment']);
        $this->assertEquals('USD', $defaults['ratehawk_default_currency']);
        $this->assertEquals('en', $defaults['ratehawk_default_language']);
        $this->assertEquals('1', $defaults['ratehawk_cache_enabled']);
    }

    /** @test */
    public function it_can_save_and_retrieve_settings()
    {
        // Arrange
        $settingData = [
            'ratehawk_enable' => '1',
            'ratehawk_environment' => 'production',
            'ratehawk_key_id' => 'test_key_id',
            'ratehawk_api_key' => 'test_api_key',
            'ratehawk_default_currency' => 'EUR',
        ];

        // Act - Save settings
        foreach ($settingData as $key => $value) {
            Settings::updateOrCreate(
                ['name' => $key],
                ['val' => $value, 'group' => 'ratehawk']
            );
        }

        // Assert - Retrieve settings
        foreach ($settingData as $key => $expectedValue) {
            $actualValue = setting_item($key);
            $this->assertEquals($expectedValue, $actualValue, "Setting {$key} should be {$expectedValue}");
        }
    }

    /** @test */
    public function it_integrates_with_core_settings_system()
    {
        // This test verifies that RateHawk settings appear in the core settings system
        
        // Act
        $allSettingPages = Settings::getSettingPages();

        // Assert
        $rateHawkPage = collect($allSettingPages)->firstWhere('id', 'ratehawk');
        
        $this->assertNotNull($rateHawkPage, 'RateHawk settings should be available in core settings');
        $this->assertEquals('RateHawk API', $rateHawkPage['title']);
        $this->assertEquals(85, $rateHawkPage['position']);
    }

    /** @test */
    public function it_filters_sensitive_settings_in_demo_mode()
    {
        // Act
        $settingPages = SettingClass::getSettingPages();
        $rateHawkSettings = $settingPages[0];

        // Assert
        $this->assertArrayHasKey('filter_demo_mode', $rateHawkSettings);
        $sensitiveKeys = $rateHawkSettings['filter_demo_mode'];
        
        $this->assertContains('ratehawk_key_id', $sensitiveKeys);
        $this->assertContains('ratehawk_api_key', $sensitiveKeys);
        $this->assertContains('ratehawk_webhook_secret', $sensitiveKeys);
    }

    /** @test */
    public function it_validates_currency_format()
    {
        // Arrange
        $rules = SettingClass::getValidationRules();

        // Assert
        $this->assertEquals('nullable|string|size:3', $rules['ratehawk_default_currency']);
    }

    /** @test */
    public function it_validates_language_format()
    {
        // Arrange
        $rules = SettingClass::getValidationRules();

        // Assert
        $this->assertEquals('nullable|string|size:2', $rules['ratehawk_default_language']);
        $this->assertEquals('nullable|string|size:2', $rules['ratehawk_default_residency']);
    }

    /** @test */
    public function it_validates_numeric_settings()
    {
        // Arrange
        $rules = SettingClass::getValidationRules();

        // Assert
        $this->assertEquals('nullable|integer|min:5|max:120', $rules['ratehawk_api_timeout']);
        $this->assertEquals('nullable|integer|min:10|max:200', $rules['ratehawk_hotels_limit']);
        $this->assertEquals('nullable|integer|min:60|max:3600', $rules['ratehawk_cache_search_ttl']);
    }

    /** @test */
    public function it_has_proper_settings_name()
    {
        // Act
        $settingsName = SettingClass::getSettingsName();

        // Assert
        $this->assertEquals('RateHawk API Settings', $settingsName);
    }

    /** @test */
    public function settings_view_file_exists()
    {
        // Assert
        $viewPath = resource_path('views/modules/RateHawk/admin/settings/ratehawk.blade.php');
        
        // Since we're testing in the module structure, check the module view path
        $moduleViewPath = base_path('modules/RateHawk/Views/admin/settings/ratehawk.blade.php');
        $this->assertFileExists($moduleViewPath, 'RateHawk settings view file should exist');
    }
}
