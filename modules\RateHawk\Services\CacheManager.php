<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Config;

class CacheManager
{
    protected $config;
    protected $primaryStore;
    protected $secondaryStore;
    protected $memoryCache = [];

    public function __construct()
    {
        $this->config = config('ratehawk.cache', []);
        $this->initializeStores();
    }

    /**
     * Initialize cache stores
     */
    protected function initializeStores(): void
    {
        try {
            // Primary store (Redis preferred)
            $primaryDriver = $this->config['stores']['primary']['driver'] ?? 'file';
            $this->primaryStore = Cache::store($primaryDriver);

            // Secondary store (Database fallback)
            $secondaryDriver = $this->config['stores']['secondary']['driver'] ?? 'file';
            $this->secondaryStore = Cache::store($secondaryDriver);

        } catch (\Exception $e) {
            Log::warning('Failed to initialize cache stores: ' . $e->getMessage());
            // Fallback to default cache
            $this->primaryStore = Cache::store();
            $this->secondaryStore = Cache::store();
        }
    }

    /**
     * Get data from cache with fallback strategy
     */
    public function get(string $key, $default = null)
    {
        // Check memory cache first (request-level)
        if (isset($this->memoryCache[$key])) {
            return $this->memoryCache[$key];
        }

        try {
            // Try primary cache
            $value = $this->primaryStore->get($key);
            if ($value !== null) {
                $this->memoryCache[$key] = $value;
                return $value;
            }

            // Try secondary cache if fallback enabled
            if ($this->config['fallback']['fallback_to_database'] ?? true) {
                $value = $this->secondaryStore->get($key);
                if ($value !== null) {
                    // Restore to primary cache
                    $this->primaryStore->put($key, $value, $this->getDefaultTtl());
                    $this->memoryCache[$key] = $value;
                    return $value;
                }
            }

        } catch (\Exception $e) {
            Log::warning('Cache get failed for key: ' . $key . ' - ' . $e->getMessage());
        }

        return $default;
    }

    /**
     * Store data in cache with redundancy
     */
    public function put(string $key, $value, int $ttl = null): bool
    {
        $ttl = $ttl ?? $this->getDefaultTtl();
        $success = false;

        // Store in memory cache
        $this->memoryCache[$key] = $value;

        try {
            // Store in primary cache
            $success = $this->primaryStore->put($key, $value, $ttl);

            // Store in secondary cache if fallback enabled
            if ($this->config['fallback']['fallback_to_database'] ?? true) {
                $this->secondaryStore->put($key, $value, $ttl);
            }

        } catch (\Exception $e) {
            Log::warning('Cache put failed for key: ' . $key . ' - ' . $e->getMessage());
        }

        return $success;
    }

    /**
     * Remember pattern with fallback
     */
    public function remember(string $key, int $ttl, callable $callback)
    {
        $value = $this->get($key);
        
        if ($value !== null) {
            return $value;
        }

        try {
            $value = $callback();
            $this->put($key, $value, $ttl);
            return $value;
        } catch (\Exception $e) {
            Log::error('Cache remember callback failed for key: ' . $key . ' - ' . $e->getMessage());
            
            // Try to get stale data if enabled
            if ($this->config['fallback']['use_stale_on_error'] ?? true) {
                return $this->getStale($key);
            }
            
            throw $e;
        }
    }

    /**
     * Forget cache key from all stores
     */
    public function forget(string $key): bool
    {
        unset($this->memoryCache[$key]);
        
        $success = true;
        
        try {
            $success &= $this->primaryStore->forget($key);
            
            if ($this->config['fallback']['fallback_to_database'] ?? true) {
                $success &= $this->secondaryStore->forget($key);
            }
        } catch (\Exception $e) {
            Log::warning('Cache forget failed for key: ' . $key . ' - ' . $e->getMessage());
            $success = false;
        }

        return $success;
    }

    /**
     * Flush all caches
     */
    public function flush(): bool
    {
        $this->memoryCache = [];
        
        $success = true;
        
        try {
            $success &= $this->primaryStore->flush();
            
            if ($this->config['fallback']['fallback_to_database'] ?? true) {
                $success &= $this->secondaryStore->flush();
            }
        } catch (\Exception $e) {
            Log::warning('Cache flush failed: ' . $e->getMessage());
            $success = false;
        }

        return $success;
    }

    /**
     * Get cache key with prefix
     */
    public function getKey(string $type, string $identifier): string
    {
        $prefix = $this->config['prefixes'][$type] ?? 'rh';
        return "{$prefix}:{$identifier}";
    }

    /**
     * Get TTL for cache type
     */
    public function getTtl(string $type): int
    {
        return $this->config['ttl'][$type] ?? $this->getDefaultTtl();
    }

    /**
     * Get default TTL
     */
    protected function getDefaultTtl(): int
    {
        return $this->config['ttl']['search_results'] ?? 30;
    }

    /**
     * Get stale data (expired but still in cache)
     */
    protected function getStale(string $key)
    {
        try {
            // This is a simplified implementation
            // In a real scenario, you'd need to track expiration times separately
            return $this->secondaryStore->get($key . '_stale');
        } catch (\Exception $e) {
            Log::warning('Failed to get stale cache: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Test cache connectivity
     */
    public function testConnection(): array
    {
        $results = [
            'primary' => false,
            'secondary' => false,
            'memory' => true,
        ];

        // Test primary cache
        try {
            $testKey = 'ratehawk_test_' . time();
            $testValue = 'test_value';
            
            $this->primaryStore->put($testKey, $testValue, 1);
            $retrieved = $this->primaryStore->get($testKey);
            $this->primaryStore->forget($testKey);
            
            $results['primary'] = ($retrieved === $testValue);
        } catch (\Exception $e) {
            Log::warning('Primary cache test failed: ' . $e->getMessage());
        }

        // Test secondary cache
        try {
            $testKey = 'ratehawk_test_secondary_' . time();
            $testValue = 'test_value';
            
            $this->secondaryStore->put($testKey, $testValue, 1);
            $retrieved = $this->secondaryStore->get($testKey);
            $this->secondaryStore->forget($testKey);
            
            $results['secondary'] = ($retrieved === $testValue);
        } catch (\Exception $e) {
            Log::warning('Secondary cache test failed: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Get cache statistics
     */
    public function getStats(): array
    {
        return [
            'config' => [
                'driver' => $this->config['driver'] ?? 'unknown',
                'enabled' => $this->config['enabled'] ?? false,
                'fallback_enabled' => $this->config['fallback']['fallback_to_database'] ?? false,
            ],
            'connectivity' => $this->testConnection(),
            'memory_cache_size' => count($this->memoryCache),
            'ttl_config' => $this->config['ttl'] ?? [],
        ];
    }

    /**
     * Check if caching is enabled
     */
    public function isEnabled(): bool
    {
        return $this->config['enabled'] ?? true;
    }

    /**
     * Check if we're in development mode with cache disabled
     */
    public function shouldDisableInDebug(): bool
    {
        return ($this->config['development']['disable_in_debug'] ?? false) && config('app.debug');
    }
}
