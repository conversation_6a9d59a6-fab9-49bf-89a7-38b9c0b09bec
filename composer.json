{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.1.0", "eluceo/ical": "^0.15.1", "flutterwavedev/flutterwave-v3": "^1.0", "geoip2/geoip2": "~2.0", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.4", "johngrogg/ics-parser": "^2.1", "kalnoy/nestedset": "^6", "laravel/fortify": "^1.10", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/socialite": "^5.0", "laravel/tinker": "^2.7", "laravel/ui": "^4.3", "league/flysystem-aws-s3-v3": "^3.0", "league/omnipay": "^3", "maatwebsite/excel": "^3.1", "mews/purifier": "^3.3", "munafio/chatify": "^1.3.4", "omnipay/migs": "~3.0", "omnipay/paypal": "^3.0", "omnipay/stripe": "^3.0", "payrexx/payrexx": "dev-master", "predis/predis": "*", "propaganistas/laravel-phone": "^5.1", "pusher/pusher-php-server": "^7.0", "rachidlaasri/laravel-installer": "^4.0", "rap2hpoutre/laravel-log-viewer": "^2.2.0", "simplesoftwareio/simple-qrcode": "^4.2.0", "spatie/laravel-google-cloud-storage": "^2.0.3", "stripe/stripe-php": "^7.113", "symfony/http-client": "^6.3", "symfony/mailgun-mailer": "^6.0", "symfony/postmark-mailer": "^6.3", "tijsverkoyen/css-to-inline-styles": "^2.2", "ua-parser/uap-php": "^3.9", "unicodeveloper/laravel-paystack": "^1.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.7", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Plugins\\": "plugins/", "Custom\\": "custom/", "Modules\\": "modules/", "Themes\\": "themes/"}, "files": ["app/Helpers/AppHelper.php", "app/Helpers/ProHelper.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}