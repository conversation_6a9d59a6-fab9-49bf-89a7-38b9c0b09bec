<?php

namespace Modules\RateHawk\Http\Controllers\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Modules\RateHawk\Http\Controllers\Admin\AdminController;
use Modules\RateHawk\Services\CacheService;
use Modules\RateHawk\Services\CacheManager;
use Illuminate\Support\Facades\Log;

class CacheTestController extends AdminController
{
    protected $cacheService;
    protected $cacheManager;

    public function __construct(CacheService $cacheService, CacheManager $cacheManager)
    {
        $this->setActiveMenu(route('ratehawk.admin.cache-test.index'));
        $this->cacheService = $cacheService;
        $this->cacheManager = $cacheManager;
    }

    /**
     * Show cache test interface
     */
    public function index()
    {
        $this->setPageTitle('Cache System Test');
        
        $cacheStats = $this->cacheManager->getStats();
        $testResults = $this->runCacheTests();
        
        return view('RateHawk::admin.cache-test.index', compact('cacheStats', 'testResults'));
    }

    /**
     * Test cache functionality via AJAX
     */
    public function testCache(Request $request): JsonResponse
    {
        try {
            $testResults = $this->runCacheTests();
            
            return response()->json([
                'success' => true,
                'data' => $testResults
            ]);
        } catch (\Exception $e) {
            Log::error('Cache test failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Cache test failed',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all caches
     */
    public function clearCache(Request $request): JsonResponse
    {
        try {
            $results = [
                'cache_manager' => $this->cacheManager->flush(),
                'search_cache' => $this->cacheService->clearSearchCache(),
            ];
            
            return response()->json([
                'success' => true,
                'message' => 'Cache cleared successfully',
                'data' => $results
            ]);
        } catch (\Exception $e) {
            Log::error('Cache clear failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get cache statistics
     */
    public function getStats(Request $request): JsonResponse
    {
        try {
            $stats = [
                'cache_manager' => $this->cacheManager->getStats(),
                'cache_service' => $this->cacheService->getCacheStats(),
            ];
            
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get cache stats: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to get cache statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Run comprehensive cache tests
     */
    protected function runCacheTests(): array
    {
        $results = [
            'basic_cache_test' => $this->testBasicCache(),
            'hotel_static_cache' => $this->testHotelStaticCache(),
            'search_cache' => $this->testSearchCache(),
            'fallback_test' => $this->testCacheFallback(),
            'performance_test' => $this->testCachePerformance(),
        ];

        return $results;
    }

    /**
     * Test basic cache functionality
     */
    protected function testBasicCache(): array
    {
        try {
            $key = 'test_basic_' . time();
            $value = ['test' => 'data', 'timestamp' => time()];
            
            // Test put
            $putResult = $this->cacheManager->put($key, $value, 60);
            
            // Test get
            $getValue = $this->cacheManager->get($key);
            
            // Test forget
            $forgetResult = $this->cacheManager->forget($key);
            
            return [
                'success' => $putResult && ($getValue === $value) && $forgetResult,
                'put_result' => $putResult,
                'get_result' => $getValue === $value,
                'forget_result' => $forgetResult,
                'details' => [
                    'stored_value' => $value,
                    'retrieved_value' => $getValue,
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test hotel static data caching
     */
    protected function testHotelStaticCache(): array
    {
        try {
            $hotelId = 'test_hotel_' . time();
            $hotelData = [
                'name' => 'Test Hotel',
                'location' => 'Test City',
                'rating' => 4.5,
                'amenities' => ['wifi', 'pool', 'spa']
            ];
            
            // Test cache
            $cacheResult = $this->cacheService->cacheHotelStatic($hotelId, $hotelData);
            
            // Test retrieve
            $retrievedData = $this->cacheService->getHotelStatic($hotelId);
            
            return [
                'success' => $cacheResult && ($retrievedData === $hotelData),
                'cache_result' => $cacheResult,
                'data_match' => $retrievedData === $hotelData,
                'details' => [
                    'stored_data' => $hotelData,
                    'retrieved_data' => $retrievedData,
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test search results caching
     */
    protected function testSearchCache(): array
    {
        try {
            $searchParams = [
                'destination' => 'Test City',
                'checkin' => '2025-01-15',
                'checkout' => '2025-01-17',
                'adults' => 2
            ];
            
            $searchResults = [
                'hotels' => [
                    ['id' => 1, 'name' => 'Hotel A'],
                    ['id' => 2, 'name' => 'Hotel B'],
                ],
                'total' => 2
            ];
            
            // Test cache
            $cacheResult = $this->cacheService->cacheSearchResults($searchParams, $searchResults);
            
            // Test retrieve
            $retrievedResults = $this->cacheService->getSearchResults($searchParams);
            
            return [
                'success' => $cacheResult && ($retrievedResults === $searchResults),
                'cache_result' => $cacheResult,
                'data_match' => $retrievedResults === $searchResults,
                'details' => [
                    'search_params' => $searchParams,
                    'stored_results' => $searchResults,
                    'retrieved_results' => $retrievedResults,
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test cache fallback mechanisms
     */
    protected function testCacheFallback(): array
    {
        try {
            $connectivity = $this->cacheManager->testConnection();
            
            return [
                'success' => true,
                'connectivity' => $connectivity,
                'fallback_available' => $connectivity['secondary'] || $connectivity['memory'],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Test cache performance
     */
    protected function testCachePerformance(): array
    {
        try {
            $iterations = 100;
            $testData = ['performance' => 'test', 'data' => range(1, 100)];
            
            // Test write performance
            $writeStart = microtime(true);
            for ($i = 0; $i < $iterations; $i++) {
                $this->cacheManager->put("perf_test_{$i}", $testData, 60);
            }
            $writeTime = (microtime(true) - $writeStart) * 1000; // Convert to milliseconds
            
            // Test read performance
            $readStart = microtime(true);
            for ($i = 0; $i < $iterations; $i++) {
                $this->cacheManager->get("perf_test_{$i}");
            }
            $readTime = (microtime(true) - $readStart) * 1000; // Convert to milliseconds
            
            // Cleanup
            for ($i = 0; $i < $iterations; $i++) {
                $this->cacheManager->forget("perf_test_{$i}");
            }
            
            return [
                'success' => true,
                'iterations' => $iterations,
                'write_time_ms' => round($writeTime, 2),
                'read_time_ms' => round($readTime, 2),
                'avg_write_ms' => round($writeTime / $iterations, 4),
                'avg_read_ms' => round($readTime / $iterations, 4),
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
