@extends('admin.layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between">
            <h1 class="h3 mb-3 text-gray-800">{{$page_title}}</h1>
            <div>
                <a href="{{route('ratehawk.admin.bookings.index')}}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> {{__('Back to Bookings')}}
                </a>
            </div>
        </div>
        @include('admin.message')

        <!-- Booking Details -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Booking Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Booking Information')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Order ID')}}:</strong><br>
                                <code>{{$booking->order_id}}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Status')}}:</strong><br>
                                @if($booking->status == 'confirmed')
                                    <span class="badge badge-success">{{__('Confirmed')}}</span>
                                @elseif($booking->status == 'cancelled')
                                    <span class="badge badge-danger">{{__('Cancelled')}}</span>
                                @elseif($booking->status == 'pending')
                                    <span class="badge badge-warning">{{__('Pending')}}</span>
                                @else
                                    <span class="badge badge-secondary">{{ucfirst($booking->status)}}</span>
                                @endif
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('RateHawk Booking ID')}}:</strong><br>
                                @if($booking->ratehawk_booking_id)
                                    <code>{{$booking->ratehawk_booking_id}}</code>
                                @else
                                    -
                                @endif
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Total Amount')}}:</strong><br>
                                @if($booking->total_amount)
                                    <strong>{{$booking->currency}} {{number_format($booking->total_amount, 2)}}</strong>
                                @else
                                    -
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Guest Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Guest Information')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Name')}}:</strong><br>
                                {{$booking->guest_name}}
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Email')}}:</strong><br>
                                {{$booking->guest_email}}
                            </div>
                        </div>
                        @if($booking->guest_phone)
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Phone')}}:</strong><br>
                                {{$booking->guest_phone}}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Hotel Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Hotel Information')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-12">
                                <strong>{{__('Hotel Name')}}:</strong><br>
                                <h5>{{$booking->hotel_name}}</h5>
                            </div>
                        </div>
                        @if($booking->hotel_location)
                        <hr>
                        <div class="row">
                            <div class="col-md-12">
                                <strong>{{__('Location')}}:</strong><br>
                                {{$booking->hotel_location}}
                            </div>
                        </div>
                        @endif
                        @if($booking->room_type)
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Room Type')}}:</strong><br>
                                {{$booking->room_type}}
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Guests')}}:</strong><br>
                                {{$booking->adults ?? 0}} {{__('Adults')}}
                                @if($booking->children)
                                    + {{$booking->children}} {{__('Children')}}
                                @endif
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Stay Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Stay Information')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Check-in Date')}}:</strong><br>
                                @if($booking->check_in_date)
                                    {{\Carbon\Carbon::parse($booking->check_in_date)->format('Y-m-d')}}
                                @else
                                    -
                                @endif
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Check-out Date')}}:</strong><br>
                                @if($booking->check_out_date)
                                    {{\Carbon\Carbon::parse($booking->check_out_date)->format('Y-m-d')}}
                                @else
                                    -
                                @endif
                            </div>
                        </div>
                        @if($booking->check_in_date && $booking->check_out_date)
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Duration')}}:</strong><br>
                                {{\Carbon\Carbon::parse($booking->check_in_date)->diffInDays(\Carbon\Carbon::parse($booking->check_out_date))}} {{__('nights')}}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Additional Information -->
                @if($booking->special_requests || $booking->notes)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Additional Information')}}</h6>
                    </div>
                    <div class="card-body">
                        @if($booking->special_requests)
                        <div class="mb-3">
                            <strong>{{__('Special Requests')}}:</strong><br>
                            {{$booking->special_requests}}
                        </div>
                        @endif
                        @if($booking->notes)
                        <div class="mb-3">
                            <strong>{{__('Notes')}}:</strong><br>
                            {{$booking->notes}}
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>

            <div class="col-lg-4">
                <!-- Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Actions')}}</h6>
                    </div>
                    <div class="card-body">
                        @if($booking->status == 'confirmed')
                        <button type="button" class="btn btn-danger btn-block mb-2" onclick="cancelBooking()">
                            <i class="fas fa-times"></i> {{__('Cancel Booking')}}
                        </button>
                        @endif
                        <button type="button" class="btn btn-info btn-block mb-2" onclick="checkStatus()">
                            <i class="fas fa-sync"></i> {{__('Check Status')}}
                        </button>
                        @if($booking->status == 'confirmed')
                        <button type="button" class="btn btn-success btn-block mb-2" onclick="getVoucher()">
                            <i class="fas fa-file-pdf"></i> {{__('Get Voucher')}}
                        </button>
                        <button type="button" class="btn btn-warning btn-block mb-2" onclick="getInvoice()">
                            <i class="fas fa-file-invoice"></i> {{__('Get Invoice')}}
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Timeline -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Timeline')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{__('Booking Created')}}</h6>
                                    <p class="timeline-text">{{$booking->created_at->format('Y-m-d H:i:s')}}</p>
                                </div>
                            </div>
                            @if($booking->updated_at != $booking->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{__('Last Updated')}}</h6>
                                    <p class="timeline-text">{{$booking->updated_at->format('Y-m-d H:i:s')}}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- User Information -->
                @if($booking->user)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Booked By')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <strong>{{$booking->user->name}}</strong><br>
                            <small class="text-muted">{{$booking->user->email}}</small>
                        </div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e3e6f0;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
        }
        .timeline-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }
    </style>

    <script>
        function cancelBooking() {
            if (confirm('{{__("Are you sure you want to cancel this booking?")}}')) {
                $.post('{{route("ratehawk.admin.bookings.cancel", $booking->id)}}', {
                    _token: '{{csrf_token()}}'
                }).done(function(response) {
                    if (response.success) {
                        location.reload();
                    } else {
                        alert(response.message || '{{__("Error occurred")}}');
                    }
                });
            }
        }

        function checkStatus() {
            $.post('{{route("ratehawk.admin.bookings.check-status", $booking->id)}}', {
                _token: '{{csrf_token()}}'
            }).done(function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert(response.message || '{{__("Error occurred")}}');
                }
            });
        }

        function getVoucher() {
            $.post('{{route("ratehawk.admin.bookings.voucher", $booking->id)}}', {
                _token: '{{csrf_token()}}'
            }).done(function(response) {
                if (response.success && response.voucher_url) {
                    window.open(response.voucher_url, '_blank');
                } else {
                    alert(response.message || '{{__("Error occurred")}}');
                }
            });
        }

        function getInvoice() {
            $.post('{{route("ratehawk.admin.bookings.invoice", $booking->id)}}', {
                _token: '{{csrf_token()}}'
            }).done(function(response) {
                if (response.success && response.invoice_url) {
                    window.open(response.invoice_url, '_blank');
                } else {
                    alert(response.message || '{{__("Error occurred")}}');
                }
            });
        }
    </script>
@endsection
