<?php

namespace Modules\RateHawk\Admin\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Models\RateHawkSetting;
use Modules\RateHawk\Services\RateHawkApiClient;

class SettingsController extends Controller
{
    protected $apiClient;

    public function __construct(RateHawkApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
    }

    /**
     * Get all settings
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $includePrivate = $request->boolean('include_private', false);
            
            if ($includePrivate) {
                $settings = RateHawkSetting::getAllSettings();
            } else {
                $settings = RateHawkSetting::getPublicSettings();
            }

            return response()->json([
                'success' => true,
                'data' => $settings,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update settings
     */
    public function update(Request $request): JsonResponse
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.*.key' => 'required|string',
            'settings.*.value' => 'nullable',
            'settings.*.type' => 'nullable|string|in:string,boolean,integer,float,json,array',
        ]);

        try {
            $updatedSettings = [];

            foreach ($request->settings as $settingData) {
                $key = $settingData['key'];
                $value = $settingData['value'];
                $type = $settingData['type'] ?? 'string';

                $setting = RateHawkSetting::set($key, $value, $type);
                $updatedSettings[] = [
                    'key' => $key,
                    'value' => $setting->value,
                    'type' => $setting->type,
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Settings updated successfully',
                'data' => $updatedSettings,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(Request $request): JsonResponse
    {
        try {
            $result = $this->apiClient->testConnection();

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'data' => $result['data'],
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
                'timestamp' => now()->toISOString()
            ], 500);
        }
    }

    /**
     * Get setting by key
     */
    public function show(Request $request, string $key): JsonResponse
    {
        try {
            $setting = RateHawkSetting::where('key', $key)->first();

            if (!$setting) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'key' => $setting->key,
                    'value' => $setting->value,
                    'type' => $setting->type,
                    'description' => $setting->description,
                    'is_public' => $setting->is_public,
                    'is_encrypted' => $setting->is_encrypted,
                ],
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get setting: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update single setting
     */
    public function updateSetting(Request $request, string $key): JsonResponse
    {
        $request->validate([
            'value' => 'nullable',
            'type' => 'nullable|string|in:string,boolean,integer,float,json,array',
            'description' => 'nullable|string',
        ]);

        try {
            $setting = RateHawkSetting::set(
                $key,
                $request->value,
                $request->type ?? 'string',
                $request->description
            );

            return response()->json([
                'success' => true,
                'message' => 'Setting updated successfully',
                'data' => [
                    'key' => $setting->key,
                    'value' => $setting->value,
                    'type' => $setting->type,
                    'description' => $setting->description,
                ],
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update setting: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete setting
     */
    public function destroy(Request $request, string $key): JsonResponse
    {
        try {
            $deleted = RateHawkSetting::forget($key);

            if (!$deleted) {
                return response()->json([
                    'success' => false,
                    'message' => 'Setting not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'message' => 'Setting deleted successfully',
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete setting: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reset settings to defaults
     */
    public function reset(Request $request): JsonResponse
    {
        $request->validate([
            'keys' => 'nullable|array',
            'keys.*' => 'string',
        ]);

        try {
            $keysToReset = $request->keys ?? [
                'ratehawk_enable',
                'ratehawk_environment',
                'ratehawk_default_currency',
                'ratehawk_default_language',
                'ratehawk_default_residency',
                'ratehawk_cache_enabled',
                'ratehawk_logging_enabled',
                'ratehawk_webhooks_enabled',
            ];

            $defaultValues = [
                'ratehawk_enable' => true,
                'ratehawk_environment' => 'test',
                'ratehawk_default_currency' => 'USD',
                'ratehawk_default_language' => 'en',
                'ratehawk_default_residency' => 'us',
                'ratehawk_cache_enabled' => true,
                'ratehawk_logging_enabled' => true,
                'ratehawk_webhooks_enabled' => true,
            ];

            $resetSettings = [];

            foreach ($keysToReset as $key) {
                if (isset($defaultValues[$key])) {
                    $type = in_array($key, ['ratehawk_enable', 'ratehawk_cache_enabled', 'ratehawk_logging_enabled', 'ratehawk_webhooks_enabled']) 
                        ? 'boolean' : 'string';
                    
                    $setting = RateHawkSetting::set($key, $defaultValues[$key], $type);
                    $resetSettings[] = [
                        'key' => $key,
                        'value' => $setting->value,
                        'type' => $setting->type,
                    ];
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Settings reset to defaults successfully',
                'data' => $resetSettings,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reset settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export settings
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $includePrivate = $request->boolean('include_private', false);
            $includeEncrypted = $request->boolean('include_encrypted', false);

            $query = RateHawkSetting::query();

            if (!$includePrivate) {
                $query->where('is_public', true);
            }

            if (!$includeEncrypted) {
                $query->where('is_encrypted', false);
            }

            $settings = $query->get()->map(function ($setting) {
                return [
                    'key' => $setting->key,
                    'value' => $setting->value,
                    'type' => $setting->type,
                    'description' => $setting->description,
                    'is_public' => $setting->is_public,
                    'is_encrypted' => $setting->is_encrypted,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => $settings,
                'export_info' => [
                    'exported_at' => now()->toISOString(),
                    'include_private' => $includePrivate,
                    'include_encrypted' => $includeEncrypted,
                    'total_settings' => $settings->count(),
                ],
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export settings: ' . $e->getMessage()
            ], 500);
        }
    }
}
