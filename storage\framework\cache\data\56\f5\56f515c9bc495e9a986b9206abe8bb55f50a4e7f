9999999999O:30:"Modules\Media\Models\MediaFile":37:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:11:"media_files";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:20:{s:2:"id";i:140;s:9:"file_name";s:8:"Speed Km";s:9:"file_path";s:26:"demo/car/feature/Speed.svg";s:9:"file_size";N;s:9:"file_type";s:9:"image/svg";s:14:"file_extension";s:3:"svg";s:6:"driver";N;s:10:"is_private";i:0;s:11:"create_user";N;s:11:"update_user";N;s:10:"deleted_at";N;s:6:"app_id";N;s:11:"app_user_id";N;s:10:"file_width";N;s:11:"file_height";N;s:10:"created_at";N;s:10:"updated_at";N;s:9:"folder_id";i:0;s:9:"author_id";N;s:9:"file_edit";i:0;}s:11:" * original";a:20:{s:2:"id";i:140;s:9:"file_name";s:8:"Speed Km";s:9:"file_path";s:26:"demo/car/feature/Speed.svg";s:9:"file_size";N;s:9:"file_type";s:9:"image/svg";s:14:"file_extension";s:3:"svg";s:6:"driver";N;s:10:"is_private";i:0;s:11:"create_user";N;s:11:"update_user";N;s:10:"deleted_at";N;s:6:"app_id";N;s:11:"app_user_id";N;s:10:"file_width";N;s:11:"file_height";N;s:10:"created_at";N;s:10:"updated_at";N;s:9:"folder_id";i:0;s:9:"author_id";N;s:9:"file_edit";i:0;}s:10:" * changes";a:0:{}s:8:" * casts";a:1:{s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";s:11:"Y-m-d H:i:s";s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:12:" * slugField";s:0:"";s:16:" * slugFromField";s:0:"";s:14:" * cleanFields";a:0:{}s:11:" * seo_type";N;s:21:"translationForeignKey";s:9:"origin_id";s:20:" * translation_class";N;s:16:" * forceDeleting";b:0;}