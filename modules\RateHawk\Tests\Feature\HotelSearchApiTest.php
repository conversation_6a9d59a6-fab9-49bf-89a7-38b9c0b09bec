<?php

namespace Modules\RateHawk\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Modules\RateHawk\Models\RateHawkSetting;
use Modules\RateHawk\Models\RateHawkApiLog;

class HotelSearchApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up basic configuration
        RateHawkSetting::create([
            'key' => 'ratehawk_enable',
            'value' => '1',
            'type' => 'boolean',
        ]);
        
        RateHawkSetting::create([
            'key' => 'ratehawk_environment',
            'value' => 'test',
            'type' => 'string',
        ]);
    }

    /** @test */
    public function it_can_search_hotels_by_region()
    {
        // Arrange
        $searchData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ],
            'currency' => 'USD',
            'language' => 'en'
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/region', $searchData);

        // Assert
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'message',
                     'data' => [
                         'search_hash',
                         'hotels'
                     ],
                     'timestamp'
                 ]);
    }

    /** @test */
    public function it_validates_required_search_parameters()
    {
        // Arrange
        $invalidData = [
            'checkin' => '2024-08-01',
            // Missing checkout, region_id, and guests
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/region', $invalidData);

        // Assert
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['checkout', 'region_id', 'guests']);
    }

    /** @test */
    public function it_validates_date_format()
    {
        // Arrange
        $invalidData = [
            'checkin' => 'invalid-date',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/region', $invalidData);

        // Assert
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['checkin']);
    }

    /** @test */
    public function it_validates_guest_configuration()
    {
        // Arrange
        $invalidData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 0] // Invalid: no adults
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/region', $invalidData);

        // Assert
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['guests.0.adults']);
    }

    /** @test */
    public function it_can_search_hotels_by_coordinates()
    {
        // Arrange
        $searchData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'latitude' => 48.8566,
            'longitude' => 2.3522,
            'radius' => 5,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/coordinates', $searchData);

        // Assert
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'search_hash',
                         'hotels'
                     ]
                 ]);
    }

    /** @test */
    public function it_can_get_hotel_suggestions()
    {
        // Arrange
        $suggestionData = [
            'query' => 'Paris',
            'language' => 'en'
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/suggest', $suggestionData);

        // Assert
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'hotels',
                         'regions'
                     ]
                 ]);
    }

    /** @test */
    public function it_requires_minimum_query_length_for_suggestions()
    {
        // Arrange
        $suggestionData = [
            'query' => 'P', // Too short
            'language' => 'en'
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/suggest', $suggestionData);

        // Assert
        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false,
                     'message' => 'Query must be at least 2 characters long'
                 ]);
    }

    /** @test */
    public function it_can_get_regions_list()
    {
        // Act
        $response = $this->getJson('/api/ratehawk/static/regions');

        // Assert
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         '*' => [
                             'id',
                             'name',
                             'type'
                         ]
                     ]
                 ]);
    }

    /** @test */
    public function it_can_filter_regions_by_country()
    {
        // Act
        $response = $this->getJson('/api/ratehawk/static/regions?country_code=FR');

        // Assert
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data'
                 ]);
    }

    /** @test */
    public function it_logs_api_requests()
    {
        // Arrange
        $searchData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/region', $searchData);

        // Assert
        $this->assertDatabaseHas('ratehawk_api_logs', [
            'method' => 'POST',
            'endpoint' => '/api/b2b/v3/hotel/search/region/',
            'status' => 'completed'
        ]);
    }

    /** @test */
    public function it_handles_api_errors_gracefully()
    {
        // This test would require mocking the API client to return an error
        // For now, we'll test with invalid credentials
        
        RateHawkSetting::where('key', 'ratehawk_key_id')->update(['value' => 'invalid']);
        RateHawkSetting::where('key', 'ratehawk_api_key')->update(['value' => 'invalid']);

        // Arrange
        $searchData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/region', $searchData);

        // Assert
        $response->assertStatus(400)
                 ->assertJson([
                     'success' => false
                 ]);
    }

    /** @test */
    public function it_respects_rate_limiting()
    {
        // This test would require implementing rate limiting middleware
        $this->markTestIncomplete('Rate limiting test requires middleware implementation');
    }

    /** @test */
    public function it_returns_cached_results_when_available()
    {
        // This test would require mocking the cache
        $this->markTestIncomplete('Cache testing requires additional setup');
    }

    /** @test */
    public function it_can_search_specific_hotels()
    {
        // Arrange
        $searchData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'ids' => ['hotel_123', 'hotel_456'],
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/hotels', $searchData);

        // Assert
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'success',
                     'data' => [
                         'search_hash',
                         'hotels'
                     ]
                 ]);
    }

    /** @test */
    public function it_validates_hotel_ids_array()
    {
        // Arrange
        $invalidData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'ids' => 'not_an_array',
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/hotels', $invalidData);

        // Assert
        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['ids']);
    }

    /** @test */
    public function it_includes_csrf_token_in_responses()
    {
        // Act
        $response = $this->get('/ratehawk');

        // Assert
        $response->assertStatus(200)
                 ->assertViewHas('csrf_token');
    }

    /** @test */
    public function it_handles_children_ages_correctly()
    {
        // Arrange
        $searchData = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                [
                    'adults' => 2, 
                    'children' => [8, 12, 15]
                ]
            ]
        ];

        // Act
        $response = $this->postJson('/api/ratehawk/search/region', $searchData);

        // Assert
        $response->assertStatus(200);
        
        // Verify the request was logged with correct children data
        $this->assertDatabaseHas('ratehawk_api_logs', [
            'method' => 'POST',
            'endpoint' => '/api/b2b/v3/hotel/search/region/'
        ]);
    }
}
