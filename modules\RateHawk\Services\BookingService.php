<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Validator;
use Modules\RateHawk\Exceptions\RateHawkApiException;
use Modules\RateHawk\Models\RateHawkBooking;

class BookingService
{
    protected $apiClient;

    public function __construct(RateHawkApiClient $apiClient)
    {
        $this->apiClient = $apiClient;
    }

    /**
     * Create a new booking process
     */
    public function createBookingProcess(array $params): array
    {
        $this->validateCreateBookingParams($params);

        $endpoint = '/api/b2b/v3/booking/new/';
        $response = $this->apiClient->post($endpoint, $params);

        // Store booking in local database
        if (isset($response['data']['order_id'])) {
            $this->storeBooking($response['data'], $params);
        }

        return $response;
    }

    /**
     * Start booking process
     */
    public function startBookingProcess(array $params): array
    {
        $this->validateStartBookingParams($params);

        $endpoint = '/api/b2b/v3/booking/start/';
        $response = $this->apiClient->post($endpoint, $params);

        // Update booking status
        if (isset($params['order_id'])) {
            $this->updateBookingStatus($params['order_id'], 'started', $response);
        }

        return $response;
    }

    /**
     * Check booking status
     */
    public function checkBookingStatus(string $orderId): array
    {
        $endpoint = '/api/b2b/v3/booking/check/';
        $response = $this->apiClient->post($endpoint, ['order_id' => $orderId]);

        // Update local booking status
        if (isset($response['data'])) {
            $this->updateBookingStatus($orderId, $response['data']['status'] ?? 'unknown', $response);
        }

        return $response;
    }

    /**
     * Cancel booking
     */
    public function cancelBooking(string $orderId): array
    {
        $endpoint = '/api/b2b/v3/booking/cancel/';
        $response = $this->apiClient->post($endpoint, ['order_id' => $orderId]);

        // Update local booking status
        $this->updateBookingStatus($orderId, 'cancelled', $response);

        return $response;
    }

    /**
     * Get booking voucher
     */
    public function getVoucher(string $orderId): array
    {
        $endpoint = '/api/b2b/v3/booking/voucher/';
        return $this->apiClient->post($endpoint, ['order_id' => $orderId]);
    }

    /**
     * Get booking invoice
     */
    public function getInvoice(string $orderId): array
    {
        $endpoint = '/api/b2b/v3/booking/invoice/';
        return $this->apiClient->post($endpoint, ['order_id' => $orderId]);
    }

    /**
     * Retrieve bookings list
     */
    public function getBookings(array $params = []): array
    {
        $this->validateGetBookingsParams($params);

        $endpoint = '/api/b2b/v3/booking/list/';
        return $this->apiClient->post($endpoint, $params);
    }

    /**
     * Create credit card token
     */
    public function createCreditCardToken(array $params): array
    {
        $this->validateCreditCardParams($params);

        $endpoint = '/api/b2b/v3/booking/cc_token/';
        return $this->apiClient->post($endpoint, $params);
    }

    /**
     * Validate create booking parameters
     */
    protected function validateCreateBookingParams(array $params): void
    {
        $validator = Validator::make($params, [
            'search_hash' => 'required|string',
            'match_hash' => 'required|string',
            'language' => 'nullable|string|size:2',
            'user_ip' => 'required|ip',
            'partner_order_id' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid create booking parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Validate start booking parameters
     */
    protected function validateStartBookingParams(array $params): void
    {
        $validator = Validator::make($params, [
            'order_id' => 'required|string',
            'book_hash' => 'required|string',
            'guests' => 'required|array|min:1',
            'guests.*.first_name' => 'required|string|max:100',
            'guests.*.last_name' => 'required|string|max:100',
            'guests.*.email' => 'nullable|email',
            'guests.*.phone' => 'nullable|string|max:20',
            'payment_type' => 'required|string|in:deposit,full_payment',
            'cc_token' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid start booking parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Validate get bookings parameters
     */
    protected function validateGetBookingsParams(array $params): void
    {
        $validator = Validator::make($params, [
            'from_date' => 'nullable|date',
            'to_date' => 'nullable|date|after_or_equal:from_date',
            'status' => 'nullable|string',
            'limit' => 'nullable|integer|min:1|max:100',
            'offset' => 'nullable|integer|min:0',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid get bookings parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Validate credit card parameters
     */
    protected function validateCreditCardParams(array $params): void
    {
        $validator = Validator::make($params, [
            'cc_number' => 'required|string|regex:/^\d{13,19}$/',
            'cc_holder_name' => 'required|string|max:100',
            'cc_expiry_month' => 'required|integer|between:1,12',
            'cc_expiry_year' => 'required|integer|min:' . date('Y'),
            'cc_cvc' => 'required|string|regex:/^\d{3,4}$/',
        ]);

        if ($validator->fails()) {
            throw new RateHawkApiException('Invalid credit card parameters: ' . implode(', ', $validator->errors()->all()));
        }
    }

    /**
     * Store booking in local database
     */
    protected function storeBooking(array $bookingData, array $originalParams): void
    {
        try {
            RateHawkBooking::create([
                'order_id' => $bookingData['order_id'],
                'partner_order_id' => $originalParams['partner_order_id'] ?? null,
                'search_hash' => $originalParams['search_hash'],
                'match_hash' => $originalParams['match_hash'],
                'book_hash' => $bookingData['book_hash'] ?? null,
                'status' => 'created',
                'hotel_data' => $bookingData['hotel'] ?? null,
                'room_data' => $bookingData['room'] ?? null,
                'price_data' => $bookingData['price'] ?? null,
                'guest_data' => null,
                'payment_data' => null,
                'api_response' => $bookingData,
                'user_ip' => $originalParams['user_ip'],
                'created_at' => now(),
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to store RateHawk booking: ' . $e->getMessage());
        }
    }

    /**
     * Update booking status in local database
     */
    protected function updateBookingStatus(string $orderId, string $status, array $responseData): void
    {
        try {
            $booking = RateHawkBooking::where('order_id', $orderId)->first();
            if ($booking) {
                $booking->update([
                    'status' => $status,
                    'api_response' => $responseData,
                    'updated_at' => now(),
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to update RateHawk booking status: ' . $e->getMessage());
        }
    }

    /**
     * Get local booking by order ID
     */
    public function getLocalBooking(string $orderId): ?RateHawkBooking
    {
        return RateHawkBooking::where('order_id', $orderId)->first();
    }

    /**
     * Get local bookings with filters
     */
    public function getLocalBookings(array $filters = []): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        $query = RateHawkBooking::query();

        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['from_date'])) {
            $query->where('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->where('created_at', '<=', $filters['to_date']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('order_id', 'like', '%' . $filters['search'] . '%')
                  ->orWhere('partner_order_id', 'like', '%' . $filters['search'] . '%');
            });
        }

        return $query->orderBy('created_at', 'desc')
                    ->paginate($filters['per_page'] ?? 15);
    }
}
