{"__meta": {"id": "X1223b851ff005aa771a202071b31148a", "datetime": "2025-07-10 21:15:33", "utime": **********.316582, "method": "GET", "uri": "/api/search/destinations?query=par", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752182132.010827, "end": **********.316627, "duration": 1.305799961090088, "duration_str": "1.31s", "measures": [{"label": "Booting", "start": 1752182132.010827, "relative_start": 0, "end": **********.156097, "relative_end": **********.156097, "duration": 1.1452698707580566, "duration_str": "1.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": **********.156169, "relative_start": 1.1453418731689453, "end": **********.316633, "relative_end": 5.9604644775390625e-06, "duration": 0.16046404838562012, "duration_str": "160ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5463840, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/search/destinations", "middleware": "web", "controller": "App\\Http\\Controllers\\HomeRateHawkController@searchDestinations", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "api.search.destinations", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeRateHawkController.php&line=77\" onclick=\"\">app/Http/Controllers/HomeRateHawkController.php:77-107</a>"}, "queries": {"nb_statements": 1, "nb_failed_statements": 0, "accumulated_duration": 0.02248, "accumulated_duration_str": "22.48ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `bravo_locations` where `name` LIKE '%par%' and `status` = 'publish' and `bravo_locations`.`deleted_at` is null limit 10", "type": "query", "params": [], "bindings": ["%par%", "publish"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/HomeRateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeRateHawkController.php", "line": 283}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/HomeRateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Controllers\\HomeRateHawkController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.264387, "duration": 0.02248, "duration_str": "22.48ms", "memory": 0, "memory_str": null, "filename": "HomeRateHawkController.php:283", "source": "app/Http/Controllers/HomeRateHawkController.php:283", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FHomeRateHawkController.php&line=283", "ajax": false, "filename": "HomeRateHawkController.php", "line": "283"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Location\\Models\\Location": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FLocation%2FModels%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "BcFfFvnWCqY5HO7Juhl56dhdVDeMpZEyey7ogXME", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/api/search/destinations?query=par\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/api/search/destinations", "status_code": "<pre class=sf-dump id=sf-dump-97282613 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-97282613\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1654931858 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>query</span>\" => \"<span class=sf-dump-str title=\"3 characters\">par</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1654931858\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1573501179 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1573501179\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1010770317 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">curl/8.9.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010770317\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-369692393 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-369692393\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1673571972 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 21:15:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlE1a0hMU29QTUxCZWt4OTg1QStubmc9PSIsInZhbHVlIjoiQkxSc3BjU1Exc0ptOUVrM0hKTnZIMUlIZ09KNVJyR2pidDFicmlwQm54MkU5RUpHbHA3UzFmaHJ4OGVyMXhPSmJNWHcrYXFNZC9xMWVBK2plWGdqRTRDZnFmMGhYM1ZGZXJDRmdjc1RpbERyZ3VRSDI2TnR3RVpzTjZKeW9nbmwiLCJtYWMiOiJjODc3NTY3MWMzMjgzYzlhYzI3MzNmNmZkYjUyYWI4NTExZDUzMzIwNzFlYzQ2Yzk3MTk0NmFmOWY1MTllNDliIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:15:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IjYvVG91Qzg5NGttNjR2N3luUEpQNUE9PSIsInZhbHVlIjoidElhTDdTK2hOQzNOWDh0Yk1Qbllkdm1mRkxyRlpzeDIxMzJTY0VLV21QbVZ6cFROL3o5eWdkWm9BNGNKTzF1eVRnbG1qeno3ZE9yZ1JlU2hxWXh5ZUxEWTdUTzMwTUIwcVNKekhBTCtHYkZIOXBxRW10b3dxTGlQUDVlVDVXeXYiLCJtYWMiOiI3ODA2YTAzODA1ZWUyZjJiODMwMTJjZTc3Njk2ZjI2MGZjYmFiYjUzYmFjMzBkY2IzMTJkY2E2ZTA0ZGNjODhkIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:15:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlE1a0hMU29QTUxCZWt4OTg1QStubmc9PSIsInZhbHVlIjoiQkxSc3BjU1Exc0ptOUVrM0hKTnZIMUlIZ09KNVJyR2pidDFicmlwQm54MkU5RUpHbHA3UzFmaHJ4OGVyMXhPSmJNWHcrYXFNZC9xMWVBK2plWGdqRTRDZnFmMGhYM1ZGZXJDRmdjc1RpbERyZ3VRSDI2TnR3RVpzTjZKeW9nbmwiLCJtYWMiOiJjODc3NTY3MWMzMjgzYzlhYzI3MzNmNmZkYjUyYWI4NTExZDUzMzIwNzFlYzQ2Yzk3MTk0NmFmOWY1MTllNDliIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:15:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IjYvVG91Qzg5NGttNjR2N3luUEpQNUE9PSIsInZhbHVlIjoidElhTDdTK2hOQzNOWDh0Yk1Qbllkdm1mRkxyRlpzeDIxMzJTY0VLV21QbVZ6cFROL3o5eWdkWm9BNGNKTzF1eVRnbG1qeno3ZE9yZ1JlU2hxWXh5ZUxEWTdUTzMwTUIwcVNKekhBTCtHYkZIOXBxRW10b3dxTGlQUDVlVDVXeXYiLCJtYWMiOiI3ODA2YTAzODA1ZWUyZjJiODMwMTJjZTc3Njk2ZjI2MGZjYmFiYjUzYmFjMzBkY2IzMTJkY2E2ZTA0ZGNjODhkIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:15:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1673571972\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2117206863 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">BcFfFvnWCqY5HO7Juhl56dhdVDeMpZEyey7ogXME</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://127.0.0.1:8000/api/search/destinations?query=par</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117206863\", {\"maxDepth\":0})</script>\n"}}