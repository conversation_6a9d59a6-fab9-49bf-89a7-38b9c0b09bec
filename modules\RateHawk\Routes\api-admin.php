<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Admin API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register admin API routes for RateHawk module.
|
*/

Route::group(['prefix' => 'ratehawk', 'middleware' => ['auth:sanctum']], function () {
    
    // Dashboard API
    Route::get('/dashboard/stats', 'Api\DashboardController@stats')->name('ratehawk.api.admin.dashboard.stats');
    Route::get('/dashboard/recent-bookings', 'Api\DashboardController@recentBookings')->name('ratehawk.api.admin.dashboard.recent-bookings');
    
    // Bookings Management API
    Route::group(['prefix' => 'bookings'], function () {
        Route::get('/', 'Api\BookingController@index')->name('ratehawk.api.admin.bookings.index');
        Route::get('/{id}', 'Api\BookingController@show')->name('ratehawk.api.admin.bookings.show');
        Route::post('/{id}/cancel', 'Api\BookingController@cancel')->name('ratehawk.api.admin.bookings.cancel');
        Route::get('/{id}/logs', 'Api\BookingController@logs')->name('ratehawk.api.admin.bookings.logs');
    });
    
    // Settings API
    Route::group(['prefix' => 'settings'], function () {
        Route::get('/', 'Api\SettingsController@index')->name('ratehawk.api.admin.settings.index');
        Route::post('/', 'Api\SettingsController@update')->name('ratehawk.api.admin.settings.update');
        Route::post('/test-connection', 'Api\SettingsController@testConnection')->name('ratehawk.api.admin.settings.test-connection');
    });
    
    // API Logs
    Route::group(['prefix' => 'logs'], function () {
        Route::get('/', 'Api\LogController@index')->name('ratehawk.api.admin.logs.index');
        Route::get('/{id}', 'Api\LogController@show')->name('ratehawk.api.admin.logs.show');
        Route::delete('/{id}', 'Api\LogController@destroy')->name('ratehawk.api.admin.logs.destroy');
        Route::post('/clear', 'Api\LogController@clear')->name('ratehawk.api.admin.logs.clear');
    });
    
    // Statistics
    Route::group(['prefix' => 'statistics'], function () {
        Route::get('/overview', 'Api\StatisticsController@overview')->name('ratehawk.api.admin.statistics.overview');
        Route::get('/bookings', 'Api\StatisticsController@bookings')->name('ratehawk.api.admin.statistics.bookings');
        Route::get('/revenue', 'Api\StatisticsController@revenue')->name('ratehawk.api.admin.statistics.revenue');
        Route::get('/api-usage', 'Api\StatisticsController@apiUsage')->name('ratehawk.api.admin.statistics.api-usage');
    });
});
