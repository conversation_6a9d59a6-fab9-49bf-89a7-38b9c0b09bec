<?php

namespace Modules\RateHawk\Tests\Unit;

use Tests\TestCase;
use Mockery;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Services\RateHawkApiClient;
use Modules\RateHawk\Exceptions\RateHawkApiException;
use Illuminate\Foundation\Testing\RefreshDatabase;

class HotelSearchServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $apiClientMock;
    protected $hotelSearchService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->apiClientMock = Mockery::mock(RateHawkApiClient::class);
        $this->hotelSearchService = new HotelSearchService($this->apiClientMock);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_search_hotels_by_region()
    {
        // Arrange
        $searchParams = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ],
            'currency' => 'USD',
            'language' => 'en',
            'residency' => 'us'
        ];

        $expectedResponse = [
            'data' => [
                'search_hash' => 'test_search_hash',
                'hotels' => [
                    [
                        'id' => 'hotel_123',
                        'name' => 'Test Hotel',
                        'star_rating' => 4,
                        'rates' => [
                            [
                                'match_hash' => 'test_match_hash',
                                'room_name' => 'Standard Room',
                                'price' => ['amount' => 150.00, 'currency' => 'USD']
                            ]
                        ]
                    ]
                ]
            ]
        ];

        $this->apiClientMock
            ->shouldReceive('post')
            ->once()
            ->with('/api/b2b/v3/hotel/search/region/', $searchParams)
            ->andReturn($expectedResponse);

        // Act
        $result = $this->hotelSearchService->searchByRegion($searchParams);

        // Assert
        $this->assertEquals($expectedResponse, $result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('search_hash', $result['data']);
        $this->assertArrayHasKey('hotels', $result['data']);
    }

    /** @test */
    public function it_validates_search_parameters()
    {
        // Arrange
        $invalidParams = [
            'checkin' => 'invalid-date',
            'checkout' => '2024-08-05',
            'region_id' => 'invalid',
            'guests' => []
        ];

        // Act & Assert
        $this->expectException(RateHawkApiException::class);
        $this->expectExceptionMessage('Invalid search parameters');
        
        $this->hotelSearchService->searchByRegion($invalidParams);
    }

    /** @test */
    public function it_can_search_hotels_by_coordinates()
    {
        // Arrange
        $searchParams = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'latitude' => 48.8566,
            'longitude' => 2.3522,
            'radius' => 5,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        $expectedResponse = [
            'data' => [
                'search_hash' => 'test_search_hash',
                'hotels' => []
            ]
        ];

        $this->apiClientMock
            ->shouldReceive('post')
            ->once()
            ->with('/api/b2b/v3/hotel/search/coordinates/', $searchParams)
            ->andReturn($expectedResponse);

        // Act
        $result = $this->hotelSearchService->searchByCoordinates($searchParams);

        // Assert
        $this->assertEquals($expectedResponse, $result);
    }

    /** @test */
    public function it_can_get_hotel_suggestions()
    {
        // Arrange
        $params = [
            'query' => 'Paris',
            'language' => 'en'
        ];

        $expectedResponse = [
            'data' => [
                'hotels' => [
                    [
                        'id' => 'hotel_123',
                        'name' => 'Hotel Paris',
                        'location' => 'Paris, France'
                    ]
                ]
            ]
        ];

        $this->apiClientMock
            ->shouldReceive('post')
            ->once()
            ->with('/api/b2b/v3/hotel/suggest/', $params)
            ->andReturn($expectedResponse);

        // Act
        $result = $this->hotelSearchService->suggest($params);

        // Assert
        $this->assertEquals($expectedResponse, $result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('hotels', $result['data']);
    }

    /** @test */
    public function it_handles_api_errors_gracefully()
    {
        // Arrange
        $searchParams = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        $this->apiClientMock
            ->shouldReceive('post')
            ->once()
            ->andThrow(new RateHawkApiException('API Error', 500));

        // Act & Assert
        $this->expectException(RateHawkApiException::class);
        $this->expectExceptionMessage('API Error');
        
        $this->hotelSearchService->searchByRegion($searchParams);
    }

    /** @test */
    public function it_can_prebook_a_rate()
    {
        // Arrange
        $prebookParams = [
            'search_hash' => 'test_search_hash',
            'match_hash' => 'test_match_hash',
            'language' => 'en'
        ];

        $expectedResponse = [
            'data' => [
                'book_hash' => 'test_book_hash',
                'hotel' => [
                    'name' => 'Test Hotel',
                    'address' => '123 Test St'
                ],
                'room' => [
                    'name' => 'Standard Room'
                ],
                'price' => [
                    'amount' => 150.00,
                    'currency' => 'USD'
                ]
            ]
        ];

        $this->apiClientMock
            ->shouldReceive('post')
            ->once()
            ->with('/api/b2b/v3/hotel/prebook/', $prebookParams)
            ->andReturn($expectedResponse);

        // Act
        $result = $this->hotelSearchService->prebook($prebookParams);

        // Assert
        $this->assertEquals($expectedResponse, $result);
        $this->assertArrayHasKey('data', $result);
        $this->assertArrayHasKey('book_hash', $result['data']);
    }

    /** @test */
    public function it_caches_search_results_when_enabled()
    {
        // This test would require mocking the cache
        // and verifying that results are stored and retrieved from cache
        $this->markTestIncomplete('Cache testing requires additional setup');
    }

    /** @test */
    public function it_validates_guest_configuration()
    {
        // Arrange
        $invalidGuestParams = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 0] // Invalid: no adults
            ]
        ];

        // Act & Assert
        $this->expectException(RateHawkApiException::class);
        
        $this->hotelSearchService->searchByRegion($invalidGuestParams);
    }

    /** @test */
    public function it_validates_date_range()
    {
        // Arrange
        $invalidDateParams = [
            'checkin' => '2024-08-05',
            'checkout' => '2024-08-01', // Checkout before checkin
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => []]
            ]
        ];

        // Act & Assert
        $this->expectException(RateHawkApiException::class);
        
        $this->hotelSearchService->searchByRegion($invalidDateParams);
    }

    /** @test */
    public function it_formats_children_ages_correctly()
    {
        // Arrange
        $searchParams = [
            'checkin' => '2024-08-01',
            'checkout' => '2024-08-05',
            'region_id' => 6176,
            'guests' => [
                ['adults' => 2, 'children' => [8, 12]]
            ]
        ];

        $this->apiClientMock
            ->shouldReceive('post')
            ->once()
            ->with('/api/b2b/v3/hotel/search/region/', Mockery::on(function ($params) {
                return isset($params['guests'][0]['children']) && 
                       is_array($params['guests'][0]['children']) &&
                       $params['guests'][0]['children'] === [8, 12];
            }))
            ->andReturn(['data' => ['search_hash' => 'test', 'hotels' => []]]);

        // Act
        $this->hotelSearchService->searchByRegion($searchParams);

        // Assert is handled by the mock expectation
    }
}
