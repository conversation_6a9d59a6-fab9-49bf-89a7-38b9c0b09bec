<?php

namespace Modules\RateHawk\Controllers\Api;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use Modules\RateHawk\Services\HotelSearchService;
use Modules\RateHawk\Services\StaticContentService;
use Modules\RateHawk\Exceptions\RateHawkApiException;

class SearchController extends Controller
{
    protected $hotelSearchService;
    protected $staticContentService;

    public function __construct(
        HotelSearchService $hotelSearchService,
        StaticContentService $staticContentService
    ) {
        $this->hotelSearchService = $hotelSearchService;
        $this->staticContentService = $staticContentService;
    }

    /**
     * Search hotels by region
     */
    public function searchByRegion(Request $request): JsonResponse
    {
        try {
            $params = $this->prepareSearchParams($request);
            $results = $this->hotelSearchService->searchByRegion($params);

            return $this->successResponse($results, 'Hotels found successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Search failed: ' . $e->getMessage());
        }
    }

    /**
     * Search hotels by hotel IDs
     */
    public function searchByHotels(Request $request): JsonResponse
    {
        try {
            $params = $this->prepareSearchParams($request);
            $results = $this->hotelSearchService->searchByHotels($params);

            return $this->successResponse($results, 'Hotels found successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Search failed: ' . $e->getMessage());
        }
    }

    /**
     * Search hotels by coordinates
     */
    public function searchByCoordinates(Request $request): JsonResponse
    {
        try {
            $params = $this->prepareSearchParams($request);
            $results = $this->hotelSearchService->searchByCoordinates($params);

            return $this->successResponse($results, 'Hotels found successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Search failed: ' . $e->getMessage());
        }
    }

    /**
     * Get hotel and region suggestions
     */
    public function suggest(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query');
            $language = $request->input('language', config('ratehawk.defaults.language'));

            if (strlen($query) < 2) {
                return $this->errorResponse('Query must be at least 2 characters long', 400);
            }

            // Get both hotel suggestions and region suggestions
            $hotelSuggestions = $this->hotelSearchService->suggest([
                'query' => $query,
                'language' => $language
            ]);

            $regionSuggestions = $this->staticContentService->searchRegions($query, 5);

            $results = [
                'hotels' => $hotelSuggestions['data'] ?? [],
                'regions' => $regionSuggestions,
                'query' => $query
            ];

            return $this->successResponse($results, 'Suggestions retrieved successfully');

        } catch (RateHawkApiException $e) {
            return $this->errorResponse($e->getMessage(), $e->getStatusCode(), $e->getErrorDetails());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to get suggestions: ' . $e->getMessage());
        }
    }

    /**
     * Prepare search parameters with defaults
     */
    protected function prepareSearchParams(Request $request): array
    {
        $params = $request->all();

        // Set default values
        $params['currency'] = $params['currency'] ?? config('ratehawk.defaults.currency');
        $params['language'] = $params['language'] ?? config('ratehawk.defaults.language');
        $params['residency'] = $params['residency'] ?? config('ratehawk.defaults.residency');
        $params['hotels_limit'] = $params['hotels_limit'] ?? config('ratehawk.defaults.hotels_limit');

        // Add user IP if not provided
        if (!isset($params['user_ip'])) {
            $params['user_ip'] = $request->ip();
        }

        return $params;
    }

    /**
     * Return success response
     */
    protected function successResponse(array $data, string $message = 'Success'): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Return error response
     */
    protected function errorResponse(
        string $message, 
        int $statusCode = 500, 
        array $errorDetails = []
    ): JsonResponse {
        $response = [
            'success' => false,
            'message' => $message,
            'timestamp' => now()->toISOString()
        ];

        if (!empty($errorDetails)) {
            $response['error_details'] = $errorDetails;
        }

        return response()->json($response, $statusCode);
    }
}
