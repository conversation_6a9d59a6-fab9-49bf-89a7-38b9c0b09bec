<?php

namespace Modules\RateHawk\Services;

use Illuminate\Support\Facades\Log;
use Modules\RateHawk\Exceptions\RateHawkApiException;
use Modules\RateHawk\Exceptions\RateHawkAuthException;
use Modules\RateHawk\Exceptions\RateHawkRateLimitException;
use Modules\RateHawk\Models\RateHawkApiLog;
use Modules\RateHawk\Helpers\ConfigHelper;

class ErrorHandlingService
{
    protected $loggingEnabled;
    protected $logChannel;

    public function __construct()
    {
        $loggingConfig = ConfigHelper::getLoggingConfig();
        $this->loggingEnabled = $loggingConfig['enabled'];
        $this->logChannel = $loggingConfig['channel'];
    }

    /**
     * Handle API exception and log appropriately
     */
    public function handleApiException(\Exception $exception, array $context = []): array
    {
        $errorData = $this->extractErrorData($exception);
        
        // Log the error
        if ($this->loggingEnabled) {
            $this->logError($exception, $context);
        }

        // Store in database if it's an API log
        if (isset($context['request_id'])) {
            $this->storeErrorInDatabase($exception, $context);
        }

        // Send notifications for critical errors
        if ($this->isCriticalError($exception)) {
            $this->sendCriticalErrorNotification($exception, $context);
        }

        return $errorData;
    }

    /**
     * Extract structured error data from exception
     */
    public function extractErrorData(\Exception $exception): array
    {
        $errorData = [
            'type' => get_class($exception),
            'message' => $exception->getMessage(),
            'code' => $exception->getCode(),
            'file' => $exception->getFile(),
            'line' => $exception->getLine(),
            'trace' => $exception->getTraceAsString(),
        ];

        // Add specific data for RateHawk exceptions
        if ($exception instanceof RateHawkApiException) {
            $errorData['api_error'] = true;
            $errorData['status_code'] = $exception->getStatusCode();
            $errorData['response_data'] = $exception->getResponseData();
            $errorData['error_details'] = $exception->getErrorDetails();
            $errorData['validation_errors'] = $exception->getValidationErrors();
        }

        if ($exception instanceof RateHawkAuthException) {
            $errorData['auth_error'] = true;
            $errorData['severity'] = 'critical';
        }

        if ($exception instanceof RateHawkRateLimitException) {
            $errorData['rate_limit_error'] = true;
            $errorData['severity'] = 'warning';
        }

        return $errorData;
    }

    /**
     * Log error with appropriate level
     */
    protected function logError(\Exception $exception, array $context = []): void
    {
        $level = $this->determineLogLevel($exception);
        $message = $this->formatErrorMessage($exception);
        $logContext = array_merge($context, $this->extractErrorData($exception));

        Log::channel($this->logChannel)->log($level, $message, $logContext);
    }

    /**
     * Store error in database
     */
    protected function storeErrorInDatabase(\Exception $exception, array $context): void
    {
        try {
            if (isset($context['request_id'])) {
                RateHawkApiLog::where('request_id', $context['request_id'])->update([
                    'status' => 'error',
                    'error_type' => get_class($exception),
                    'error_message' => $exception->getMessage(),
                    'response_data' => $exception instanceof RateHawkApiException 
                        ? $exception->getResponseData() 
                        : null,
                    'updated_at' => now(),
                ]);
            }
        } catch (\Exception $e) {
            Log::error('Failed to store error in database: ' . $e->getMessage());
        }
    }

    /**
     * Determine appropriate log level for exception
     */
    protected function determineLogLevel(\Exception $exception): string
    {
        if ($exception instanceof RateHawkAuthException) {
            return 'critical';
        }

        if ($exception instanceof RateHawkRateLimitException) {
            return 'warning';
        }

        if ($exception instanceof RateHawkApiException) {
            $statusCode = $exception->getStatusCode();
            if ($statusCode >= 500) {
                return 'error';
            } elseif ($statusCode >= 400) {
                return 'warning';
            }
        }

        return 'error';
    }

    /**
     * Format error message for logging
     */
    protected function formatErrorMessage(\Exception $exception): string
    {
        $message = 'RateHawk API Error: ' . $exception->getMessage();

        if ($exception instanceof RateHawkApiException) {
            $statusCode = $exception->getStatusCode();
            if ($statusCode) {
                $message .= " (HTTP {$statusCode})";
            }
        }

        return $message;
    }

    /**
     * Check if error is critical
     */
    protected function isCriticalError(\Exception $exception): bool
    {
        return $exception instanceof RateHawkAuthException ||
               ($exception instanceof RateHawkApiException && $exception->getStatusCode() >= 500);
    }

    /**
     * Send critical error notification
     */
    protected function sendCriticalErrorNotification(\Exception $exception, array $context): void
    {
        try {
            // This could be extended to send emails, Slack notifications, etc.
            Log::critical('RateHawk Critical Error Notification', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'context' => $context,
                'timestamp' => now()->toISOString(),
            ]);

            // You could add email notifications here
            // Mail::to(config('mail.admin_email'))->send(new CriticalErrorNotification($exception, $context));

        } catch (\Exception $e) {
            Log::error('Failed to send critical error notification: ' . $e->getMessage());
        }
    }

    /**
     * Get error statistics
     */
    public function getErrorStatistics(int $days = 7): array
    {
        $startDate = now()->subDays($days)->startOfDay();

        $errorLogs = RateHawkApiLog::where('created_at', '>=', $startDate)
            ->where('status', 'error')
            ->get();

        $stats = [
            'total_errors' => $errorLogs->count(),
            'by_type' => $errorLogs->groupBy('error_type')->map->count(),
            'by_endpoint' => $errorLogs->groupBy('endpoint')->map->count(),
            'by_day' => [],
            'most_common_errors' => [],
            'critical_errors' => 0,
        ];

        // Group by day
        for ($date = $startDate->copy(); $date->lte(now()); $date->addDay()) {
            $dayStart = $date->copy()->startOfDay();
            $dayEnd = $date->copy()->endOfDay();
            
            $dayErrors = $errorLogs->whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $stats['by_day'][] = [
                'date' => $date->format('Y-m-d'),
                'errors' => $dayErrors,
            ];
        }

        // Most common error messages
        $stats['most_common_errors'] = $errorLogs
            ->groupBy('error_message')
            ->map->count()
            ->sortDesc()
            ->take(10)
            ->toArray();

        // Count critical errors (auth errors, 5xx errors)
        $stats['critical_errors'] = $errorLogs->filter(function ($log) {
            return $log->error_type === RateHawkAuthException::class ||
                   ($log->status_code && $log->status_code >= 500);
        })->count();

        return $stats;
    }

    /**
     * Analyze error patterns
     */
    public function analyzeErrorPatterns(int $days = 30): array
    {
        $startDate = now()->subDays($days)->startOfDay();

        $errorLogs = RateHawkApiLog::where('created_at', '>=', $startDate)
            ->where('status', 'error')
            ->get();

        $patterns = [
            'recurring_errors' => [],
            'error_trends' => [],
            'problematic_endpoints' => [],
            'time_patterns' => [],
            'recommendations' => [],
        ];

        // Find recurring errors (same error message appearing multiple times)
        $errorCounts = $errorLogs->groupBy('error_message')->map->count();
        $patterns['recurring_errors'] = $errorCounts->filter(function ($count) {
            return $count >= 5; // Errors that occurred 5+ times
        })->sortDesc()->take(5)->toArray();

        // Analyze error trends over time
        $dailyErrors = [];
        for ($date = $startDate->copy(); $date->lte(now()); $date->addDay()) {
            $dayStart = $date->copy()->startOfDay();
            $dayEnd = $date->copy()->endOfDay();
            
            $dayErrorCount = $errorLogs->whereBetween('created_at', [$dayStart, $dayEnd])->count();
            $dailyErrors[] = [
                'date' => $date->format('Y-m-d'),
                'count' => $dayErrorCount,
            ];
        }
        $patterns['error_trends'] = $dailyErrors;

        // Find problematic endpoints
        $endpointErrors = $errorLogs->groupBy('endpoint')->map(function ($errors, $endpoint) {
            return [
                'endpoint' => $endpoint,
                'error_count' => $errors->count(),
                'error_rate' => 0, // Would need total requests to calculate
                'common_errors' => $errors->groupBy('error_type')->map->count()->sortDesc()->take(3)->toArray(),
            ];
        })->sortByDesc('error_count')->take(5)->values();
        $patterns['problematic_endpoints'] = $endpointErrors;

        // Analyze time patterns (errors by hour of day)
        $hourlyErrors = $errorLogs->groupBy(function ($log) {
            return $log->created_at->format('H');
        })->map->count()->sortKeys();
        $patterns['time_patterns'] = $hourlyErrors->toArray();

        // Generate recommendations
        $patterns['recommendations'] = $this->generateRecommendations($patterns);

        return $patterns;
    }

    /**
     * Generate recommendations based on error patterns
     */
    protected function generateRecommendations(array $patterns): array
    {
        $recommendations = [];

        // Check for high error rates
        if (count($patterns['recurring_errors']) > 0) {
            $recommendations[] = [
                'type' => 'recurring_errors',
                'priority' => 'high',
                'message' => 'Multiple recurring errors detected. Review error handling for common failure scenarios.',
                'action' => 'Implement retry logic and better error handling for frequent errors.',
            ];
        }

        // Check for problematic endpoints
        if (count($patterns['problematic_endpoints']) > 0) {
            $topProblematic = array_values($patterns['problematic_endpoints'])[0];
            if ($topProblematic['error_count'] > 10) {
                $recommendations[] = [
                    'type' => 'problematic_endpoint',
                    'priority' => 'medium',
                    'message' => "Endpoint '{$topProblematic['endpoint']}' has high error rate.",
                    'action' => 'Review API usage patterns and implement specific error handling for this endpoint.',
                ];
            }
        }

        // Check for time-based patterns
        $maxHourlyErrors = max($patterns['time_patterns']);
        $avgHourlyErrors = array_sum($patterns['time_patterns']) / count($patterns['time_patterns']);
        if ($maxHourlyErrors > $avgHourlyErrors * 3) {
            $peakHour = array_search($maxHourlyErrors, $patterns['time_patterns']);
            $recommendations[] = [
                'type' => 'time_pattern',
                'priority' => 'low',
                'message' => "Error spike detected at hour {$peakHour}:00.",
                'action' => 'Consider implementing rate limiting or load balancing during peak hours.',
            ];
        }

        return $recommendations;
    }

    /**
     * Create error report
     */
    public function createErrorReport(int $days = 7): array
    {
        $statistics = $this->getErrorStatistics($days);
        $patterns = $this->analyzeErrorPatterns($days);

        return [
            'period' => [
                'days' => $days,
                'start_date' => now()->subDays($days)->format('Y-m-d'),
                'end_date' => now()->format('Y-m-d'),
            ],
            'summary' => [
                'total_errors' => $statistics['total_errors'],
                'critical_errors' => $statistics['critical_errors'],
                'avg_daily_errors' => round($statistics['total_errors'] / $days, 2),
            ],
            'statistics' => $statistics,
            'patterns' => $patterns,
            'generated_at' => now()->toISOString(),
        ];
    }
}
