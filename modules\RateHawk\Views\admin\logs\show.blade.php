@extends('admin.layouts.app')

@section('content')
    <div class="container-fluid">
        <div class="d-flex justify-content-between">
            <h1 class="h3 mb-3 text-gray-800">{{$page_title}}</h1>
            <div>
                <a href="{{route('ratehawk.admin.logs.index')}}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> {{__('Back to Logs')}}
                </a>
            </div>
        </div>
        @include('admin.message')

        <!-- Log Details -->
        <div class="row">
            <div class="col-lg-8">
                <!-- Request Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Request Information')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Request ID')}}:</strong><br>
                                <code>{{$log->request_id}}</code>
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Status')}}:</strong><br>
                                @if($log->status == 'completed')
                                    <span class="badge badge-success">{{__('Completed')}}</span>
                                @elseif($log->status == 'error')
                                    <span class="badge badge-danger">{{__('Error')}}</span>
                                @else
                                    <span class="badge badge-warning">{{__('Pending')}}</span>
                                @endif
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('Method')}}:</strong><br>
                                <span class="badge badge-secondary">{{$log->method}}</span>
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('Response Time')}}:</strong><br>
                                @if($log->response_time)
                                    {{$log->response_time}}ms
                                @else
                                    -
                                @endif
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-md-12">
                                <strong>{{__('Endpoint')}}:</strong><br>
                                <code>{{$log->endpoint}}</code>
                            </div>
                        </div>
                        @if($log->user)
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <strong>{{__('User')}}:</strong><br>
                                {{$log->user->name}} ({{$log->user->email}})
                            </div>
                            <div class="col-md-6">
                                <strong>{{__('User ID')}}:</strong><br>
                                {{$log->user_id}}
                            </div>
                        </div>
                        @endif
                    </div>
                </div>

                <!-- Request Data -->
                @if($log->request_data)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Request Data')}}</h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code>{{json_encode(json_decode($log->request_data), JSON_PRETTY_PRINT)}}</code></pre>
                    </div>
                </div>
                @endif

                <!-- Response Data -->
                @if($log->response_data)
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Response Data')}}</h6>
                    </div>
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded"><code>{{json_encode(json_decode($log->response_data), JSON_PRETTY_PRINT)}}</code></pre>
                    </div>
                </div>
                @endif

                <!-- Error Information -->
                @if($log->error_message)
                <div class="card shadow mb-4 border-left-danger">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-danger">{{__('Error Information')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <strong>{{__('Error Message')}}:</strong><br>
                            {{$log->error_message}}
                        </div>
                        @if($log->error_code)
                        <p><strong>{{__('Error Code')}}:</strong> {{$log->error_code}}</p>
                        @endif
                    </div>
                </div>
                @endif
            </div>

            <div class="col-lg-4">
                <!-- Timeline -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Timeline')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="timeline">
                            <div class="timeline-item">
                                <div class="timeline-marker bg-primary"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{__('Request Created')}}</h6>
                                    <p class="timeline-text">{{$log->created_at->format('Y-m-d H:i:s')}}</p>
                                </div>
                            </div>
                            @if($log->updated_at != $log->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="timeline-title">{{__('Last Updated')}}</h6>
                                    <p class="timeline-text">{{$log->updated_at->format('Y-m-d H:i:s')}}</p>
                                </div>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Actions')}}</h6>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-danger btn-block" onclick="deleteLog()">
                            <i class="fas fa-trash"></i> {{__('Delete Log')}}
                        </button>
                        @if($log->status == 'error')
                        <button type="button" class="btn btn-warning btn-block mt-2" onclick="retryRequest()">
                            <i class="fas fa-redo"></i> {{__('Retry Request')}}
                        </button>
                        @endif
                    </div>
                </div>

                <!-- Statistics -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">{{__('Statistics')}}</h6>
                    </div>
                    <div class="card-body">
                        <div class="text-center">
                            <div class="mb-2">
                                <strong>{{__('Request Size')}}</strong><br>
                                @if($log->request_data)
                                    {{number_format(strlen($log->request_data))}} {{__('bytes')}}
                                @else
                                    -
                                @endif
                            </div>
                            <div class="mb-2">
                                <strong>{{__('Response Size')}}</strong><br>
                                @if($log->response_data)
                                    {{number_format(strlen($log->response_data))}} {{__('bytes')}}
                                @else
                                    -
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .timeline {
            position: relative;
            padding-left: 30px;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e3e6f0;
        }
        .timeline-item {
            position: relative;
            margin-bottom: 20px;
        }
        .timeline-marker {
            position: absolute;
            left: -22px;
            top: 0;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid #fff;
        }
        .timeline-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .timeline-text {
            font-size: 12px;
            color: #6c757d;
            margin: 0;
        }
    </style>

    <script>
        function deleteLog() {
            if (confirm('{{__("Are you sure you want to delete this log?")}}')) {
                $.ajax({
                    url: '{{route("ratehawk.admin.logs.destroy", $log->id)}}',
                    method: 'DELETE',
                    data: {
                        _token: '{{csrf_token()}}'
                    }
                }).done(function(response) {
                    if (response.success) {
                        window.location.href = '{{route("ratehawk.admin.logs.index")}}';
                    } else {
                        alert(response.message || '{{__("Error occurred")}}');
                    }
                });
            }
        }

        function retryRequest() {
            if (confirm('{{__("Are you sure you want to retry this request?")}}')) {
                // Implementation for retrying the request
                alert('{{__("Retry functionality not implemented yet")}}');
            }
        }
    </script>
@endsection
