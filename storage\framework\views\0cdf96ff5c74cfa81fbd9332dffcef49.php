<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title"><?php echo e(__("RateHawk API Configuration")); ?></h3>
        <p class="form-group-desc"><?php echo e(__('Configure RateHawk B2B API integration for hotel bookings')); ?></p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">
                
                
                <div class="form-group">
                    <label><?php echo e(__("Enable RateHawk")); ?></label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_enable" value="1" <?php if(setting_item('ratehawk_enable') == '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Enable")); ?></label>
                        <label><input type="radio" name="ratehawk_enable" value="0" <?php if(setting_item('ratehawk_enable') != '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Disable")); ?></label>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("Environment")); ?></label>
                    <div class="form-controls">
                        <select name="ratehawk_environment" class="form-control">
                            <option value="test" <?php if(setting_item('ratehawk_environment') == 'test'): ?> selected <?php endif; ?>><?php echo e(__("Test/Sandbox")); ?></option>
                            <option value="production" <?php if(setting_item('ratehawk_environment') == 'production'): ?> selected <?php endif; ?>><?php echo e(__("Production")); ?></option>
                        </select>
                        <small class="form-text text-muted"><?php echo e(__('Select the API environment to use')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("API Key ID")); ?></label>
                    <div class="form-controls">
                        <input type="text" name="ratehawk_key_id" value="<?php echo e(setting_item('ratehawk_key_id')); ?>" class="form-control" placeholder="<?php echo e(__('Enter your RateHawk API Key ID')); ?>"/>
                        <small class="form-text text-muted"><?php echo e(__('Your RateHawk API Key ID from partner panel')); ?></small>
                    </div>
                </div>

                <div class="form-group">
                    <label><?php echo e(__("API Key")); ?></label>
                    <div class="form-controls">
                        <input type="password" name="ratehawk_api_key" value="<?php echo e(setting_item('ratehawk_api_key')); ?>" class="form-control" placeholder="<?php echo e(__('Enter your RateHawk API Key')); ?>"/>
                        <small class="form-text text-muted"><?php echo e(__('Your RateHawk API Key (will be encrypted when saved)')); ?></small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title"><?php echo e(__("Default Settings")); ?></h3>
        <p class="form-group-desc"><?php echo e(__('Configure default parameters for hotel searches')); ?></p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">

                
                <div class="form-group">
                    <label><?php echo e(__("Default Currency")); ?></label>
                    <div class="form-controls">
                        <select name="ratehawk_default_currency" class="form-control">
                            <?php
                                $currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'SEK', 'NZD'];
                                $current_currency = setting_item('ratehawk_default_currency', 'USD');
                            ?>
                            <?php $__currentLoopData = $currencies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $currency): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($currency); ?>" <?php if($current_currency == $currency): ?> selected <?php endif; ?>><?php echo e($currency); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <small class="form-text text-muted"><?php echo e(__('Default currency for hotel searches')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("Default Language")); ?></label>
                    <div class="form-controls">
                        <select name="ratehawk_default_language" class="form-control">
                            <?php
                                $languages = [
                                    'en' => 'English',
                                    'es' => 'Spanish', 
                                    'fr' => 'French',
                                    'de' => 'German',
                                    'it' => 'Italian',
                                    'pt' => 'Portuguese',
                                    'ru' => 'Russian',
                                    'zh_CN' => 'Chinese'
                                ];
                                $current_language = setting_item('ratehawk_default_language', 'en');
                            ?>
                            <?php $__currentLoopData = $languages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $code => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($code); ?>" <?php if($current_language == $code): ?> selected <?php endif; ?>><?php echo e($name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                        <small class="form-text text-muted"><?php echo e(__('Default language for hotel searches')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("Default Residency")); ?></label>
                    <div class="form-controls">
                        <input type="text" name="ratehawk_default_residency" value="<?php echo e(setting_item('ratehawk_default_residency', 'us')); ?>" class="form-control" maxlength="2" placeholder="us"/>
                        <small class="form-text text-muted"><?php echo e(__('Default residency country code (2 letters)')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("Hotels Limit")); ?></label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_hotels_limit" value="<?php echo e(setting_item('ratehawk_hotels_limit', '50')); ?>" class="form-control" min="10" max="200"/>
                        <small class="form-text text-muted"><?php echo e(__('Maximum number of hotels to return in search results')); ?></small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title"><?php echo e(__("Performance Settings")); ?></h3>
        <p class="form-group-desc"><?php echo e(__('Configure caching and performance optimization')); ?></p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">

                
                <div class="form-group">
                    <label><?php echo e(__("Enable Caching")); ?></label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_cache_enabled" value="1" <?php if(setting_item('ratehawk_cache_enabled') == '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Enable")); ?></label>
                        <label><input type="radio" name="ratehawk_cache_enabled" value="0" <?php if(setting_item('ratehawk_cache_enabled') != '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Disable")); ?></label>
                        <small class="form-text text-muted"><?php echo e(__('Cache API responses to improve performance')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("Search Results Cache (seconds)")); ?></label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_cache_search_ttl" value="<?php echo e(setting_item('ratehawk_cache_search_ttl', '300')); ?>" class="form-control" min="60" max="3600"/>
                        <small class="form-text text-muted"><?php echo e(__('How long to cache search results (300 = 5 minutes)')); ?></small>
                    </div>
                </div>

                <div class="form-group">
                    <label><?php echo e(__("Hotel Data Cache (seconds)")); ?></label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_cache_hotel_ttl" value="<?php echo e(setting_item('ratehawk_cache_hotel_ttl', '3600')); ?>" class="form-control" min="300" max="86400"/>
                        <small class="form-text text-muted"><?php echo e(__('How long to cache hotel static data (3600 = 1 hour)')); ?></small>
                    </div>
                </div>

                <div class="form-group">
                    <label><?php echo e(__("Regions Cache (seconds)")); ?></label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_cache_regions_ttl" value="<?php echo e(setting_item('ratehawk_cache_regions_ttl', '86400')); ?>" class="form-control" min="3600" max="604800"/>
                        <small class="form-text text-muted"><?php echo e(__('How long to cache regions data (86400 = 24 hours)')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("API Timeout (seconds)")); ?></label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_api_timeout" value="<?php echo e(setting_item('ratehawk_api_timeout', '30')); ?>" class="form-control" min="5" max="120"/>
                        <small class="form-text text-muted"><?php echo e(__('Maximum time to wait for API responses')); ?></small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title"><?php echo e(__("Advanced Settings")); ?></h3>
        <p class="form-group-desc"><?php echo e(__('Configure logging, webhooks and other advanced features')); ?></p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">

                
                <div class="form-group">
                    <label><?php echo e(__("Enable API Logging")); ?></label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_logging_enabled" value="1" <?php if(setting_item('ratehawk_logging_enabled') == '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Enable")); ?></label>
                        <label><input type="radio" name="ratehawk_logging_enabled" value="0" <?php if(setting_item('ratehawk_logging_enabled') != '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Disable")); ?></label>
                        <small class="form-text text-muted"><?php echo e(__('Log all API requests and responses for debugging')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("Enable Webhooks")); ?></label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_webhooks_enabled" value="1" <?php if(setting_item('ratehawk_webhooks_enabled') == '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Enable")); ?></label>
                        <label><input type="radio" name="ratehawk_webhooks_enabled" value="0" <?php if(setting_item('ratehawk_webhooks_enabled') != '1'): ?> checked <?php endif; ?>/> <?php echo e(__("Disable")); ?></label>
                        <small class="form-text text-muted"><?php echo e(__('Receive booking status updates via webhooks')); ?></small>
                    </div>
                </div>

                
                <div class="form-group">
                    <label><?php echo e(__("Webhook Secret")); ?></label>
                    <div class="form-controls">
                        <input type="password" name="ratehawk_webhook_secret" value="<?php echo e(setting_item('ratehawk_webhook_secret')); ?>" class="form-control" placeholder="<?php echo e(__('Enter webhook secret for signature verification')); ?>"/>
                        <small class="form-text text-muted"><?php echo e(__('Secret key for webhook signature verification (optional but recommended)')); ?></small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title"><?php echo e(__("Connection Test")); ?></h3>
        <p class="form-group-desc"><?php echo e(__('Test your API connection and configuration')); ?></p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">
                <div class="form-group">
                    <button type="button" class="btn btn-info" id="test-ratehawk-connection">
                        <i class="fa fa-plug"></i> <?php echo e(__("Test API Connection")); ?>

                    </button>
                    <div id="ratehawk-connection-result" class="mt-3"></div>
                </div>
                
                <div class="form-group">
                    <a href="<?php echo e(url('/admin/ratehawk')); ?>" class="btn btn-primary" target="_blank">
                        <i class="fa fa-external-link"></i> <?php echo e(__("Open RateHawk Dashboard")); ?>

                    </a>
                    <small class="form-text text-muted"><?php echo e(__('Access the full RateHawk management interface')); ?></small>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('js'); ?>
<script>
$(document).ready(function() {
    $('#test-ratehawk-connection').click(function() {
        var btn = $(this);
        var originalText = btn.html();
        var resultDiv = $('#ratehawk-connection-result');
        
        btn.html('<i class="fa fa-spinner fa-spin"></i> <?php echo e(__("Testing...")); ?>').prop('disabled', true);
        resultDiv.html('');
        
        $.ajax({
            url: '<?php echo e(url("/admin/ratehawk/test-connection")); ?>',
            method: 'POST',
            data: {
                _token: '<?php echo e(csrf_token()); ?>'
            },
            success: function(response) {
                if (response.success) {
                    resultDiv.html(
                        '<div class="alert alert-success">' +
                        '<i class="fa fa-check-circle"></i> ' + response.message +
                        '</div>'
                    );
                } else {
                    resultDiv.html(
                        '<div class="alert alert-danger">' +
                        '<i class="fa fa-times-circle"></i> ' + response.message +
                        '</div>'
                    );
                }
            },
            error: function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '<?php echo e(__("Connection test failed")); ?>';
                resultDiv.html(
                    '<div class="alert alert-danger">' +
                    '<i class="fa fa-times-circle"></i> ' + message +
                    '</div>'
                );
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\wamp64\www\mazar\modules/RateHawk/Views/admin/settings/ratehawk.blade.php ENDPATH**/ ?>