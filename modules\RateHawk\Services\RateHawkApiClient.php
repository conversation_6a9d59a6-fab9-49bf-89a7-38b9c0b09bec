<?php

namespace Modules\RateHawk\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Exception\ConnectException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Modules\RateHawk\Exceptions\RateHawkApiException;
use Modules\RateHawk\Exceptions\RateHawkAuthException;
use Modules\RateHawk\Exceptions\RateHawkRateLimitException;
use Modules\RateHawk\Models\RateHawkApiLog;
use Modules\RateHawk\Helpers\ConfigHelper;
use Modules\RateHawk\Services\LoggingService;
use Modules\RateHawk\Services\ErrorHandlingService;

class RateHawkApiClient
{
    protected $client;
    protected $baseUrl;
    protected $keyId;
    protected $apiKey;
    protected $environment;
    protected $timeout;
    protected $loggingEnabled;
    protected $loggingService;
    protected $errorHandlingService;

    public function __construct(
        LoggingService $loggingService = null,
        ErrorHandlingService $errorHandlingService = null
    ) {
        $apiConfig = ConfigHelper::getApiConfig();
        $credentials = ConfigHelper::getApiCredentials();
        $loggingConfig = ConfigHelper::getLoggingConfig();

        $this->baseUrl = $apiConfig['base_url'];
        $this->keyId = $credentials['key_id'];
        $this->apiKey = $credentials['api_key'];
        $this->environment = $credentials['environment'];
        $this->timeout = $apiConfig['timeout'];
        $this->loggingEnabled = $loggingConfig['enabled'];

        $this->loggingService = $loggingService;
        $this->errorHandlingService = $errorHandlingService;

        $this->client = new Client([
            'base_uri' => $this->baseUrl,
            'timeout' => $this->timeout,
            'auth' => [$this->keyId, $this->apiKey],
            'headers' => [
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'User-Agent' => 'Mazar-RateHawk-Client/1.0',
            ],
        ]);
    }

    /**
     * Make a GET request to the RateHawk API
     */
    public function get(string $endpoint, array $params = []): array
    {
        return $this->makeRequest('GET', $endpoint, ['query' => $params]);
    }

    /**
     * Make a POST request to the RateHawk API
     */
    public function post(string $endpoint, array $data = []): array
    {
        return $this->makeRequest('POST', $endpoint, ['json' => $data]);
    }

    /**
     * Make a PUT request to the RateHawk API
     */
    public function put(string $endpoint, array $data = []): array
    {
        return $this->makeRequest('PUT', $endpoint, ['json' => $data]);
    }

    /**
     * Make a DELETE request to the RateHawk API
     */
    public function delete(string $endpoint): array
    {
        return $this->makeRequest('DELETE', $endpoint);
    }

    /**
     * Make a request to the RateHawk API
     */
    protected function makeRequest(string $method, string $endpoint, array $options = []): array
    {
        $startTime = microtime(true);
        $requestId = uniqid('rh_', true);

        try {
            // Check rate limits
            $this->checkRateLimit($endpoint);

            // Log request if enabled
            if ($this->loggingEnabled && $this->loggingService) {
                $this->loggingService->logApiRequest($requestId, $method, $endpoint, $options);
            }

            $response = $this->client->request($method, $endpoint, $options);
            $responseData = json_decode($response->getBody()->getContents(), true);

            $duration = microtime(true) - $startTime;

            // Log response if enabled
            if ($this->loggingEnabled && $this->loggingService) {
                $this->loggingService->logApiResponse($requestId, $response->getStatusCode(), $responseData, $duration);
            }

            // Handle API errors
            if (isset($responseData['error']) && $responseData['error'] !== null) {
                throw new RateHawkApiException(
                    $responseData['error'],
                    $response->getStatusCode(),
                    $responseData
                );
            }

            // Update rate limit counters
            $this->updateRateLimit($endpoint, $response->getHeaders());

            return $responseData;

        } catch (ConnectException $e) {
            $duration = microtime(true) - $startTime;

            if ($this->loggingEnabled && $this->loggingService) {
                $this->loggingService->logApiError($requestId, 'Connection Error', $e->getMessage(), $duration);
            }

            if ($this->errorHandlingService) {
                $this->errorHandlingService->handleApiException($e, ['request_id' => $requestId]);
            }

            throw new RateHawkApiException(
                'Failed to connect to RateHawk API: ' . $e->getMessage(),
                0,
                null,
                $e
            );

        } catch (RequestException $e) {
            $duration = microtime(true) - $startTime;
            $statusCode = $e->getResponse() ? $e->getResponse()->getStatusCode() : 0;
            $responseBody = $e->getResponse() ? $e->getResponse()->getBody()->getContents() : '';

            if ($this->loggingEnabled) {
                $this->logError($requestId, 'Request Error', $e->getMessage(), $duration, $statusCode, $responseBody);
            }

            // Handle authentication errors
            if ($statusCode === 401) {
                throw new RateHawkAuthException('Authentication failed. Please check your API credentials.');
            }

            // Handle rate limit errors
            if ($statusCode === 429) {
                throw new RateHawkRateLimitException('Rate limit exceeded. Please try again later.');
            }

            throw new RateHawkApiException(
                'RateHawk API request failed: ' . $e->getMessage(),
                $statusCode,
                $responseBody ? json_decode($responseBody, true) : null,
                $e
            );
        }
    }

    /**
     * Check if we're within rate limits
     */
    protected function checkRateLimit(string $endpoint): void
    {
        $rateLimitKey = $this->getRateLimitKey($endpoint);
        $rateLimitConfig = $this->getRateLimitConfig($endpoint);

        $currentCount = Cache::get($rateLimitKey, 0);

        if ($currentCount >= $rateLimitConfig['requests']) {
            throw new RateHawkRateLimitException(
                "Rate limit exceeded for endpoint: {$endpoint}. " .
                "Limit: {$rateLimitConfig['requests']} requests per {$rateLimitConfig['seconds']} seconds."
            );
        }
    }

    /**
     * Update rate limit counters
     */
    protected function updateRateLimit(string $endpoint, array $headers): void
    {
        $rateLimitKey = $this->getRateLimitKey($endpoint);
        $rateLimitConfig = $this->getRateLimitConfig($endpoint);

        $currentCount = Cache::get($rateLimitKey, 0);
        Cache::put($rateLimitKey, $currentCount + 1, $rateLimitConfig['seconds']);

        // Store API rate limit headers if available
        if (isset($headers['X-RateLimit-Remaining'][0])) {
            Cache::put($rateLimitKey . '_remaining', $headers['X-RateLimit-Remaining'][0], 300);
        }
        if (isset($headers['X-RateLimit-Reset'][0])) {
            Cache::put($rateLimitKey . '_reset', $headers['X-RateLimit-Reset'][0], 300);
        }
    }

    /**
     * Get rate limit cache key
     */
    protected function getRateLimitKey(string $endpoint): string
    {
        return 'ratehawk_rate_limit_' . md5($endpoint . $this->keyId);
    }

    /**
     * Get rate limit configuration for endpoint
     */
    protected function getRateLimitConfig(string $endpoint): array
    {
        $rateLimits = ConfigHelper::getRateLimitConfig();

        if (strpos($endpoint, 'search') !== false) {
            return $rateLimits['search'];
        } elseif (strpos($endpoint, 'booking') !== false) {
            return $rateLimits['booking'];
        }

        return $rateLimits['general'];
    }

    /**
     * Log API request
     */
    protected function logRequest(string $requestId, string $method, string $endpoint, array $options): void
    {
        try {
            RateHawkApiLog::create([
                'request_id' => $requestId,
                'method' => $method,
                'endpoint' => $endpoint,
                'request_data' => $options,
                'status' => 'pending',
                'created_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log RateHawk API request: ' . $e->getMessage());
        }
    }

    /**
     * Log API response
     */
    protected function logResponse(string $requestId, int $statusCode, array $responseData, float $duration): void
    {
        try {
            RateHawkApiLog::where('request_id', $requestId)->update([
                'status_code' => $statusCode,
                'response_data' => $responseData,
                'duration' => $duration,
                'status' => 'completed',
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log RateHawk API response: ' . $e->getMessage());
        }
    }

    /**
     * Log API error
     */
    protected function logError(string $requestId, string $errorType, string $errorMessage, float $duration, int $statusCode = 0, string $responseBody = ''): void
    {
        try {
            RateHawkApiLog::where('request_id', $requestId)->update([
                'status_code' => $statusCode,
                'error_type' => $errorType,
                'error_message' => $errorMessage,
                'response_data' => $responseBody ? json_decode($responseBody, true) : null,
                'duration' => $duration,
                'status' => 'error',
                'updated_at' => now(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to log RateHawk API error: ' . $e->getMessage());
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        try {
            $response = $this->get('/api/b2b/v3/overview/');
            return [
                'success' => true,
                'message' => 'Connection successful',
                'data' => $response
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            ];
        }
    }

    /**
     * Get API endpoints
     */
    public function getEndpoints(): array
    {
        return $this->get('/api/b2b/v3/overview/');
    }
}
