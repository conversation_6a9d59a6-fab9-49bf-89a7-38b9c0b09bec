# RateHawk Module Installation Guide

## Prerequisites

Before installing the RateHawk module, ensure your system meets the following requirements:

### System Requirements

- **PHP**: 8.0 or higher
- **Laravel**: 8.0 or higher
- **Database**: MySQL 5.7+ or PostgreSQL 10+
- **Memory**: Minimum 512MB PHP memory limit
- **Extensions**: cURL, JSON, OpenSSL, PDO

### RateHawk API Access

- Active RateHawk partner account
- API credentials (Key ID and API Key)
- Webhook endpoint configuration (optional)

## Installation Steps

### 1. Module Installation

If the module is part of your existing Laravel application, it should already be in the `modules/RateHawk` directory.

If installing as a separate package:

```bash
# Clone or copy the module to your modules directory
cp -r ratehawk-module modules/RateHawk
```

### 2. Database Setup

Run the database migrations to create the required tables:

```bash
php artisan migrate
```

This will create the following tables:
- `ratehawk_bookings` - Store booking information
- `ratehawk_api_logs` - Log API requests and responses
- `ratehawk_settings` - Store configuration settings
- `ratehawk_cache` - Custom cache storage (optional)

### 3. Configuration

#### Environment Variables

Add the following variables to your `.env` file:

```env
# Basic Configuration
RATEHAWK_ENABLE=true
RATEHAWK_ENVIRONMENT=test
RATEHAWK_KEY_ID=your_key_id_here
RATEHAWK_API_KEY=your_api_key_here

# Default Settings
RATEHAWK_DEFAULT_CURRENCY=USD
RATEHAWK_DEFAULT_LANGUAGE=en
RATEHAWK_DEFAULT_RESIDENCY=us

# Performance Settings
RATEHAWK_CACHE_ENABLED=true
RATEHAWK_LOGGING_ENABLED=true

# Webhook Settings (Optional)
RATEHAWK_WEBHOOKS_ENABLED=true
RATEHAWK_WEBHOOK_SECRET=your_webhook_secret
```

#### Admin Configuration

1. Access the admin panel at `/admin/ratehawk`
2. Navigate to Settings
3. Configure your API credentials and preferences
4. Test the API connection

### 4. Route Registration

Ensure the module routes are registered in your application. Add to your main routes file or service provider:

```php
// In routes/web.php or a service provider
Route::group(['prefix' => 'ratehawk'], function () {
    require base_path('modules/RateHawk/Routes/web.php');
});

Route::group(['prefix' => 'api/ratehawk'], function () {
    require base_path('modules/RateHawk/Routes/api.php');
});

Route::group(['prefix' => 'admin/ratehawk'], function () {
    require base_path('modules/RateHawk/Routes/admin.php');
});
```

### 5. Service Provider Registration

Register the module's service provider in `config/app.php`:

```php
'providers' => [
    // Other providers...
    Modules\RateHawk\Providers\RateHawkServiceProvider::class,
],
```

### 6. Permissions Setup

If using a permission system, create the following permissions:

```php
// In your permission seeder or admin panel
$permissions = [
    'ratehawk_view',        // View bookings and logs
    'ratehawk_manage',      // Manage bookings and perform admin actions
    'ratehawk_settings',    // Modify configuration settings
    'ratehawk_bookings_view',    // View bookings
    'ratehawk_bookings_cancel',  // Cancel bookings
];

foreach ($permissions as $permission) {
    Permission::create(['name' => $permission]);
}
```

### 7. Queue Configuration

For optimal performance, configure queues for background processing:

```env
QUEUE_CONNECTION=redis
```

Add to your queue worker:

```bash
php artisan queue:work --queue=default,ratehawk
```

### 8. Webhook Setup (Optional)

If using webhooks for real-time booking updates:

1. Configure your webhook URL in the RateHawk partner panel:
   ```
   https://yourdomain.com/api/ratehawk/webhook/booking-status
   ```

2. Set up webhook secret for security:
   ```env
   RATEHAWK_WEBHOOK_SECRET=your_secure_secret_key
   ```

3. Ensure your server can receive POST requests on the webhook endpoint

## Verification

### 1. Test API Connection

Visit `/admin/ratehawk/settings` and click "Test Connection" to verify your API credentials.

### 2. Run Test Search

Use the API testing interface at `/admin/ratehawk/api-test` to perform a test hotel search.

### 3. Check Logs

Verify that API requests are being logged in `/admin/ratehawk/logs`.

### 4. Database Verification

Check that the database tables were created correctly:

```sql
SHOW TABLES LIKE 'ratehawk_%';
```

## Configuration Options

### Cache Settings

Configure caching for better performance:

```env
RATEHAWK_CACHE_ENABLED=true
RATEHAWK_CACHE_SEARCH_TTL=300      # 5 minutes
RATEHAWK_CACHE_HOTEL_TTL=3600      # 1 hour
RATEHAWK_CACHE_REGIONS_TTL=86400   # 24 hours
```

### Rate Limiting

Configure API rate limits:

```env
RATEHAWK_RATE_LIMIT_SEARCH_REQUESTS=10
RATEHAWK_RATE_LIMIT_SEARCH_SECONDS=60
RATEHAWK_RATE_LIMIT_BOOKING_REQUESTS=5
RATEHAWK_RATE_LIMIT_BOOKING_SECONDS=60
```

### Logging

Configure logging levels and channels:

```env
RATEHAWK_LOGGING_ENABLED=true
RATEHAWK_LOG_LEVEL=info
RATEHAWK_LOG_CHANNEL=daily
```

## Troubleshooting

### Common Issues

#### 1. API Connection Failed

**Problem**: Cannot connect to RateHawk API

**Solutions**:
- Verify API credentials are correct
- Check environment setting (test vs production)
- Ensure cURL extension is installed
- Check firewall/network connectivity

#### 2. Database Migration Errors

**Problem**: Migration fails to run

**Solutions**:
- Ensure database connection is configured
- Check database user permissions
- Verify MySQL/PostgreSQL version compatibility

#### 3. Permission Denied Errors

**Problem**: Access denied to admin interface

**Solutions**:
- Verify user has required permissions
- Check authentication middleware
- Ensure permission system is properly configured

#### 4. Webhook Not Working

**Problem**: Booking status updates not received

**Solutions**:
- Verify webhook URL is accessible from internet
- Check webhook secret configuration
- Review webhook logs for errors
- Ensure POST requests are allowed

### Debug Mode

Enable debug mode for detailed error information:

```env
APP_DEBUG=true
RATEHAWK_LOGGING_ENABLED=true
RATEHAWK_LOG_LEVEL=debug
```

### Log Files

Check the following log files for errors:

- `storage/logs/laravel.log` - General application logs
- `storage/logs/ratehawk.log` - RateHawk specific logs (if configured)

## Performance Optimization

### 1. Enable Caching

```env
RATEHAWK_CACHE_ENABLED=true
CACHE_DRIVER=redis
```

### 2. Configure Queue Workers

```bash
# Start queue workers for background processing
php artisan queue:work --queue=ratehawk,default --tries=3
```

### 3. Database Indexing

Ensure proper database indexes are in place (handled by migrations).

### 4. Log Cleanup

Set up automatic log cleanup:

```bash
# Add to crontab
0 2 * * * php /path/to/artisan ratehawk:cleanup-logs --days=30
```

## Security Considerations

### 1. API Credentials

- Store API credentials securely using Laravel's encryption
- Never commit credentials to version control
- Use environment variables for sensitive data

### 2. Webhook Security

- Always use webhook secrets for signature verification
- Validate webhook source IP addresses if possible
- Implement rate limiting on webhook endpoints

### 3. Data Protection

- Encrypt sensitive booking data
- Implement proper access controls
- Regular security audits

## Maintenance

### Regular Tasks

1. **Log Cleanup**: Remove old API logs to save disk space
2. **Cache Clearing**: Clear expired cache entries
3. **Database Optimization**: Optimize database tables periodically
4. **Security Updates**: Keep dependencies updated

### Monitoring

Set up monitoring for:
- API response times
- Error rates
- Booking success rates
- System resource usage

## Support

### Getting Help

1. Check the module documentation
2. Review API logs for error details
3. Use the debug interface at `/admin/ratehawk/api-test`
4. Contact technical support with specific error messages

### Useful Commands

```bash
# Clear module cache
php artisan cache:clear

# Run migrations
php artisan migrate

# Check queue status
php artisan queue:status

# Test API connection
php artisan ratehawk:test-connection

# Cleanup old logs
php artisan ratehawk:cleanup-logs
```

## Next Steps

After successful installation:

1. Configure your hotel search interface
2. Set up booking workflows
3. Customize email templates for booking confirmations
4. Configure payment processing integration
5. Set up monitoring and alerting
6. Train staff on the admin interface

For detailed usage instructions, refer to the [API Documentation](API.md) and [README](../README.md).
