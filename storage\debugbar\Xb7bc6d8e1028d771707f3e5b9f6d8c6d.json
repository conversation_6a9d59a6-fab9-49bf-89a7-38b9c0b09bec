{"__meta": {"id": "Xb7bc6d8e1028d771707f3e5b9f6d8c6d", "datetime": "2025-07-10 21:36:12", "utime": 1752183372.38228, "method": "GET", "uri": "/api/ratehawk-booking/history?email=<EMAIL>", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752183371.211118, "end": 1752183372.382325, "duration": 1.1712069511413574, "duration_str": "1.17s", "measures": [{"label": "Booting", "start": 1752183371.211118, "relative_start": 0, "end": 1752183372.270396, "relative_end": 1752183372.270396, "duration": 1.0592780113220215, "duration_str": "1.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752183372.270432, "relative_start": 1.0593140125274658, "end": 1752183372.382331, "relative_end": 5.9604644775390625e-06, "duration": 0.11189889907836914, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 5419920, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET api/ratehawk-booking/{reference}", "middleware": "web", "controller": "App\\Http\\Controllers\\BookingController@getBooking", "namespace": "App\\Http\\Controllers", "prefix": "", "where": [], "as": "api.ratehawk.booking.get", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FHttp%2FControllers%2FBookingController.php&line=103\" onclick=\"\">app/Http/Controllers/BookingController.php:103-135</a>"}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "null", "sanctum": "null"}, "names": ""}, "gate": {"count": 0, "messages": []}, "session": {"_token": "MY9uswsCZ0Bur5yAOJHSAwUCmSPvUJ7uI6YkRysm", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/api/ratehawk-booking/history?email=test%40example.com\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]"}, "request": {"path_info": "/api/ratehawk-booking/history", "status_code": "<pre class=sf-dump id=sf-dump-1358570709 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1358570709\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1374175193 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"16 characters\"><EMAIL></span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1374175193\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1024529406 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1024529406\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2043173028 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">curl/8.9.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2043173028\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1110495904 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1110495904\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1067610906 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 10 Jul 2025 21:36:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IklteUtGYkE3aE5lWjR6NGxhK0gwemc9PSIsInZhbHVlIjoiVW1rRjlBZkRXQXUvR1crSmFhL0FqblYydzdXY0pybVFLamVRdlRPR3lpb2xobVZvbEdDYTV2VUcwb0VWVEg3NkJ3YjJNUklEa3dZNjF5b25XV01Ua3BDaDlQRUtNMUR4cmxUN04rK0Vhd1FWUzlPWk45RlBhSlhNQXRwWWVCN3QiLCJtYWMiOiI5MDg3MGNmNTY5OWFiZTI3YmU5OTViYzY0YmI0NWI2MTU3ZjUzNDcxOWI2OTMwOTk3NDlkODE2NmMyNDhhZmRjIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:36:12 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IkdEOUtMVzNVeU4yaUpzV05HenZya2c9PSIsInZhbHVlIjoiSWlscFFsSUpoeHZHc1pvMHpiZzlhK0dUTzZxY0p3VDg2L3lhNXIvd3ZzRFpJb1NDdGFRT25aWVJ3VGN1dVBWY2k3T1RVOFc3TFZXVmNhNVJXL3oveU9oRE45YjBtZHRONzBLOGRhOHZLZFRZSVVrNFdPOFQ2NlNvbHYxQ1B3MTIiLCJtYWMiOiJlZjI1ZGQ4ODZkMmFkMTA1ZDQ0NjM1NDYzOTAzMWJkNWQ2MTA5MTEwNDA0YmZkY2NmMjY2MDAxOGRjY2JmMzVlIiwidGFnIjoiIn0%3D; expires=Thu, 10 Jul 2025 23:36:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IklteUtGYkE3aE5lWjR6NGxhK0gwemc9PSIsInZhbHVlIjoiVW1rRjlBZkRXQXUvR1crSmFhL0FqblYydzdXY0pybVFLamVRdlRPR3lpb2xobVZvbEdDYTV2VUcwb0VWVEg3NkJ3YjJNUklEa3dZNjF5b25XV01Ua3BDaDlQRUtNMUR4cmxUN04rK0Vhd1FWUzlPWk45RlBhSlhNQXRwWWVCN3QiLCJtYWMiOiI5MDg3MGNmNTY5OWFiZTI3YmU5OTViYzY0YmI0NWI2MTU3ZjUzNDcxOWI2OTMwOTk3NDlkODE2NmMyNDhhZmRjIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:36:12 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IkdEOUtMVzNVeU4yaUpzV05HenZya2c9PSIsInZhbHVlIjoiSWlscFFsSUpoeHZHc1pvMHpiZzlhK0dUTzZxY0p3VDg2L3lhNXIvd3ZzRFpJb1NDdGFRT25aWVJ3VGN1dVBWY2k3T1RVOFc3TFZXVmNhNVJXL3oveU9oRE45YjBtZHRONzBLOGRhOHZLZFRZSVVrNFdPOFQ2NlNvbHYxQ1B3MTIiLCJtYWMiOiJlZjI1ZGQ4ODZkMmFkMTA1ZDQ0NjM1NDYzOTAzMWJkNWQ2MTA5MTEwNDA0YmZkY2NmMjY2MDAxOGRjY2JmMzVlIiwidGFnIjoiIn0%3D; expires=Thu, 10-Jul-2025 23:36:12 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1067610906\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1980982935 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">MY9uswsCZ0Bur5yAOJHSAwUCmSPvUJ7uI6YkRysm</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"75 characters\">http://127.0.0.1:8000/api/ratehawk-booking/history?email=test%40example.com</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1980982935\", {\"maxDepth\":0})</script>\n"}}