<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title">{{__("RateHawk API Configuration")}}</h3>
        <p class="form-group-desc">{{__('Configure RateHawk B2B API integration for hotel bookings')}}</p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">
                
                {{-- Enable/Disable Module --}}
                <div class="form-group">
                    <label>{{__("Enable RateHawk")}}</label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_enable" value="1" @if(setting_item('ratehawk_enable') == '1') checked @endif/> {{__("Enable")}}</label>
                        <label><input type="radio" name="ratehawk_enable" value="0" @if(setting_item('ratehawk_enable') != '1') checked @endif/> {{__("Disable")}}</label>
                    </div>
                </div>

                {{-- Environment --}}
                <div class="form-group">
                    <label>{{__("Environment")}}</label>
                    <div class="form-controls">
                        <select name="ratehawk_environment" class="form-control">
                            <option value="test" @if(setting_item('ratehawk_environment') == 'test') selected @endif>{{__("Test/Sandbox")}}</option>
                            <option value="production" @if(setting_item('ratehawk_environment') == 'production') selected @endif>{{__("Production")}}</option>
                        </select>
                        <small class="form-text text-muted">{{__('Select the API environment to use')}}</small>
                    </div>
                </div>

                {{-- API Credentials --}}
                <div class="form-group">
                    <label>{{__("API Key ID")}}</label>
                    <div class="form-controls">
                        <input type="text" name="ratehawk_key_id" value="{{setting_item('ratehawk_key_id')}}" class="form-control" placeholder="{{__('Enter your RateHawk API Key ID')}}"/>
                        <small class="form-text text-muted">{{__('Your RateHawk API Key ID from partner panel')}}</small>
                    </div>
                </div>

                <div class="form-group">
                    <label>{{__("API Key")}}</label>
                    <div class="form-controls">
                        <input type="password" name="ratehawk_api_key" value="{{setting_item('ratehawk_api_key')}}" class="form-control" placeholder="{{__('Enter your RateHawk API Key')}}"/>
                        <small class="form-text text-muted">{{__('Your RateHawk API Key (will be encrypted when saved)')}}</small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title">{{__("Default Settings")}}</h3>
        <p class="form-group-desc">{{__('Configure default parameters for hotel searches')}}</p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">

                {{-- Default Currency --}}
                <div class="form-group">
                    <label>{{__("Default Currency")}}</label>
                    <div class="form-controls">
                        <select name="ratehawk_default_currency" class="form-control">
                            @php
                                $currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CAD', 'AUD', 'CHF', 'CNY', 'SEK', 'NZD'];
                                $current_currency = setting_item('ratehawk_default_currency', 'USD');
                            @endphp
                            @foreach($currencies as $currency)
                                <option value="{{$currency}}" @if($current_currency == $currency) selected @endif>{{$currency}}</option>
                            @endforeach
                        </select>
                        <small class="form-text text-muted">{{__('Default currency for hotel searches')}}</small>
                    </div>
                </div>

                {{-- Default Language --}}
                <div class="form-group">
                    <label>{{__("Default Language")}}</label>
                    <div class="form-controls">
                        <select name="ratehawk_default_language" class="form-control">
                            @php
                                $languages = [
                                    'en' => 'English',
                                    'es' => 'Spanish', 
                                    'fr' => 'French',
                                    'de' => 'German',
                                    'it' => 'Italian',
                                    'pt' => 'Portuguese',
                                    'ru' => 'Russian',
                                    'zh_CN' => 'Chinese'
                                ];
                                $current_language = setting_item('ratehawk_default_language', 'en');
                            @endphp
                            @foreach($languages as $code => $name)
                                <option value="{{$code}}" @if($current_language == $code) selected @endif>{{$name}}</option>
                            @endforeach
                        </select>
                        <small class="form-text text-muted">{{__('Default language for hotel searches')}}</small>
                    </div>
                </div>

                {{-- Default Residency --}}
                <div class="form-group">
                    <label>{{__("Default Residency")}}</label>
                    <div class="form-controls">
                        <input type="text" name="ratehawk_default_residency" value="{{setting_item('ratehawk_default_residency', 'us')}}" class="form-control" maxlength="2" placeholder="us"/>
                        <small class="form-text text-muted">{{__('Default residency country code (2 letters)')}}</small>
                    </div>
                </div>

                {{-- Hotels Limit --}}
                <div class="form-group">
                    <label>{{__("Hotels Limit")}}</label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_hotels_limit" value="{{setting_item('ratehawk_hotels_limit', '50')}}" class="form-control" min="10" max="200"/>
                        <small class="form-text text-muted">{{__('Maximum number of hotels to return in search results')}}</small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title">{{__("Performance Settings")}}</h3>
        <p class="form-group-desc">{{__('Configure caching and performance optimization')}}</p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">

                {{-- Enable Caching --}}
                <div class="form-group">
                    <label>{{__("Enable Caching")}}</label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_cache_enabled" value="1" @if(setting_item('ratehawk_cache_enabled') == '1') checked @endif/> {{__("Enable")}}</label>
                        <label><input type="radio" name="ratehawk_cache_enabled" value="0" @if(setting_item('ratehawk_cache_enabled') != '1') checked @endif/> {{__("Disable")}}</label>
                        <small class="form-text text-muted">{{__('Cache API responses to improve performance')}}</small>
                    </div>
                </div>

                {{-- Cache TTL Settings --}}
                <div class="form-group">
                    <label>{{__("Search Results Cache (seconds)")}}</label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_cache_search_ttl" value="{{setting_item('ratehawk_cache_search_ttl', '300')}}" class="form-control" min="60" max="3600"/>
                        <small class="form-text text-muted">{{__('How long to cache search results (300 = 5 minutes)')}}</small>
                    </div>
                </div>

                <div class="form-group">
                    <label>{{__("Hotel Data Cache (seconds)")}}</label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_cache_hotel_ttl" value="{{setting_item('ratehawk_cache_hotel_ttl', '3600')}}" class="form-control" min="300" max="86400"/>
                        <small class="form-text text-muted">{{__('How long to cache hotel static data (3600 = 1 hour)')}}</small>
                    </div>
                </div>

                <div class="form-group">
                    <label>{{__("Regions Cache (seconds)")}}</label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_cache_regions_ttl" value="{{setting_item('ratehawk_cache_regions_ttl', '86400')}}" class="form-control" min="3600" max="604800"/>
                        <small class="form-text text-muted">{{__('How long to cache regions data (86400 = 24 hours)')}}</small>
                    </div>
                </div>

                {{-- API Timeout --}}
                <div class="form-group">
                    <label>{{__("API Timeout (seconds)")}}</label>
                    <div class="form-controls">
                        <input type="number" name="ratehawk_api_timeout" value="{{setting_item('ratehawk_api_timeout', '30')}}" class="form-control" min="5" max="120"/>
                        <small class="form-text text-muted">{{__('Maximum time to wait for API responses')}}</small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title">{{__("Advanced Settings")}}</h3>
        <p class="form-group-desc">{{__('Configure logging, webhooks and other advanced features')}}</p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">

                {{-- Enable Logging --}}
                <div class="form-group">
                    <label>{{__("Enable API Logging")}}</label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_logging_enabled" value="1" @if(setting_item('ratehawk_logging_enabled') == '1') checked @endif/> {{__("Enable")}}</label>
                        <label><input type="radio" name="ratehawk_logging_enabled" value="0" @if(setting_item('ratehawk_logging_enabled') != '1') checked @endif/> {{__("Disable")}}</label>
                        <small class="form-text text-muted">{{__('Log all API requests and responses for debugging')}}</small>
                    </div>
                </div>

                {{-- Enable Webhooks --}}
                <div class="form-group">
                    <label>{{__("Enable Webhooks")}}</label>
                    <div class="form-controls">
                        <label><input type="radio" name="ratehawk_webhooks_enabled" value="1" @if(setting_item('ratehawk_webhooks_enabled') == '1') checked @endif/> {{__("Enable")}}</label>
                        <label><input type="radio" name="ratehawk_webhooks_enabled" value="0" @if(setting_item('ratehawk_webhooks_enabled') != '1') checked @endif/> {{__("Disable")}}</label>
                        <small class="form-text text-muted">{{__('Receive booking status updates via webhooks')}}</small>
                    </div>
                </div>

                {{-- Webhook Secret --}}
                <div class="form-group">
                    <label>{{__("Webhook Secret")}}</label>
                    <div class="form-controls">
                        <input type="password" name="ratehawk_webhook_secret" value="{{setting_item('ratehawk_webhook_secret')}}" class="form-control" placeholder="{{__('Enter webhook secret for signature verification')}}"/>
                        <small class="form-text text-muted">{{__('Secret key for webhook signature verification (optional but recommended)')}}</small>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

{{-- Test Connection Section --}}
<div class="row">
    <div class="col-sm-4">
        <h3 class="form-group-title">{{__("Connection Test")}}</h3>
        <p class="form-group-desc">{{__('Test your API connection and configuration')}}</p>
    </div>
    <div class="col-sm-8">
        <div class="panel">
            <div class="panel-body">
                <div class="form-group">
                    <button type="button" class="btn btn-info" id="test-ratehawk-connection">
                        <i class="fa fa-plug"></i> {{__("Test API Connection")}}
                    </button>
                    <div id="ratehawk-connection-result" class="mt-3"></div>
                </div>
                
                <div class="form-group">
                    <a href="{{url('/admin/ratehawk')}}" class="btn btn-primary" target="_blank">
                        <i class="fa fa-external-link"></i> {{__("Open RateHawk Dashboard")}}
                    </a>
                    <small class="form-text text-muted">{{__('Access the full RateHawk management interface')}}</small>
                </div>
            </div>
        </div>
    </div>
</div>

@push('js')
<script>
$(document).ready(function() {
    $('#test-ratehawk-connection').click(function() {
        var btn = $(this);
        var originalText = btn.html();
        var resultDiv = $('#ratehawk-connection-result');
        
        btn.html('<i class="fa fa-spinner fa-spin"></i> {{__("Testing...")}}').prop('disabled', true);
        resultDiv.html('');
        
        $.ajax({
            url: '{{url("/admin/ratehawk/test-connection")}}',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                _token: '{{csrf_token()}}'
            },
            success: function(response) {
                if (response.success) {
                    resultDiv.html(
                        '<div class="alert alert-success">' +
                        '<i class="fa fa-check-circle"></i> ' + response.message +
                        '</div>'
                    );
                } else {
                    resultDiv.html(
                        '<div class="alert alert-danger">' +
                        '<i class="fa fa-times-circle"></i> ' + response.message +
                        '</div>'
                    );
                }
            },
            error: function(xhr) {
                var message = xhr.responseJSON ? xhr.responseJSON.message : '{{__("Connection test failed")}}';
                resultDiv.html(
                    '<div class="alert alert-danger">' +
                    '<i class="fa fa-times-circle"></i> ' + message +
                    '</div>'
                );
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });
});
</script>
@endpush
