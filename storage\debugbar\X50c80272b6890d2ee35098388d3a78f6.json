{"__meta": {"id": "X50c80272b6890d2ee35098388d3a78f6", "datetime": "2025-07-09 18:02:33", "utime": 1752084153.341027, "method": "POST", "uri": "/admin/ratehawk/test-connection", "ip": "127.0.0.1"}, "php": {"version": "8.2.13", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[18:02:32] LOG.info: [RateHawk] API Request {\n    \"request_id\": \"rh_686eaeb8464d25.34357689\",\n    \"method\": \"GET\",\n    \"endpoint\": \"\\/api\\/b2b\\/v3\\/overview\\/\",\n    \"data\": {\n        \"query\": []\n    }\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.398002, "xdebug_link": null, "collector": "log"}, {"message": "[18:02:33] LOG.info: [RateHawk] API Response {\n    \"request_id\": \"rh_686eaeb8464d25.34357689\",\n    \"status_code\": 200,\n    \"duration\": 0.9844710826873779,\n    \"response_size\": 5471\n}", "message_html": null, "is_string": false, "label": "info", "time": 1752084153.27385, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752084151.068896, "end": 1752084153.341064, "duration": 2.272167921066284, "duration_str": "2.27s", "measures": [{"label": "Booting", "start": 1752084151.068896, "relative_start": 0, "end": 1752084151.766496, "relative_end": 1752084151.766496, "duration": 0.6975998878479004, "duration_str": "698ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1752084151.766517, "relative_start": 0.6976208686828613, "end": 1752084153.341067, "relative_end": 3.0994415283203125e-06, "duration": 1.5745501518249512, "duration_str": "1.57s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 6347160, "peak_usage_str": "6MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "POST admin/ratehawk/test-connection", "middleware": "web, dashboard, auth", "controller": "Modules\\RateHawk\\Admin\\RateHawkController@testConnection", "namespace": "Modules\\RateHawk\\Admin", "prefix": "admin/ratehawk", "where": [], "as": "ratehawk.admin.test-connection", "file": "<a href=\"phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FAdmin%2FRateHawkController.php&line=167\" onclick=\"\">modules/RateHawk/Admin/RateHawkController.php:167-194</a>"}, "queries": {"nb_statements": 78, "nb_failed_statements": 0, "accumulated_duration": 0.08532000000000001, "accumulated_duration_str": "85.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `core_settings` where `name` = 'update_to_110' limit 1", "type": "query", "params": [], "bindings": ["update_to_110"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.775811, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_120' limit 1", "type": "query", "params": [], "bindings": ["update_to_120"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.7888901, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_130' limit 1", "type": "query", "params": [], "bindings": ["update_to_130"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.799288, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_140' limit 1", "type": "query", "params": [], "bindings": ["update_to_140"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.80791, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_150' limit 1", "type": "query", "params": [], "bindings": ["update_to_150"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.819639, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_151' limit 1", "type": "query", "params": [], "bindings": ["update_to_151"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.828568, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_160' limit 1", "type": "query", "params": [], "bindings": ["update_to_160"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.8389, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_170' limit 1", "type": "query", "params": [], "bindings": ["update_to_170"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.848958, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_182' limit 1", "type": "query", "params": [], "bindings": ["update_to_182"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.864004, "duration": 0.00654, "duration_str": "6.54ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_190' limit 1", "type": "query", "params": [], "bindings": ["update_to_190"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.886299, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_200' limit 1", "type": "query", "params": [], "bindings": ["update_to_200"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.899706, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_210' limit 1", "type": "query", "params": [], "bindings": ["update_to_210"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.908107, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_220' limit 1", "type": "query", "params": [], "bindings": ["update_to_220"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.916949, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_230' limit 1", "type": "query", "params": [], "bindings": ["update_to_230"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.925559, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_240' limit 1", "type": "query", "params": [], "bindings": ["update_to_240"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.9339612, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_250' limit 1", "type": "query", "params": [], "bindings": ["update_to_250"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.9424078, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_300' limit 1", "type": "query", "params": [], "bindings": ["update_to_300"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.9517899, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_310' limit 1", "type": "query", "params": [], "bindings": ["update_to_310"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.959605, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_340' limit 1", "type": "query", "params": [], "bindings": ["update_to_340"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.968151, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_350' limit 1", "type": "query", "params": [], "bindings": ["update_to_350"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.976839, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'update_to_360' limit 1", "type": "query", "params": [], "bindings": ["update_to_360"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": 1752084151.985662, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_logging_enabled' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_logging_enabled"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.010591, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.020725, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.027883, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.0349078, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.041672, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.0493479, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.054045, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.059004, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.067677, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.074111, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.08187, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_api_timeout' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_timeout"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.088231, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_key_id' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_key_id"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.097296, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_api_key' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_key"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.106131, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_environment' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_environment"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.115568, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.1237278, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.1297338, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.137118, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.141373, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.148268, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.1534212, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.1579082, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.164139, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.1719398, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.177066, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.18369, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 17}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.188225, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.193542, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/ErrorHandlingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\ErrorHandlingService.php", "line": 19}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.199282, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_base_url' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_base_url"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 178}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.203631, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_api_version' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_api_version"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 179}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 34}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.20887, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_level' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_level"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 132}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.217195, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_log_channel' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_log_channel"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 133}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}], "start": **********.2217648, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `users` where `id` = 7 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\Authenticate.php", "line": 28}], "start": **********.2491891, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php:59", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mazar_travel"}, {"sql": "select * from `core_roles` where `core_roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}, {"index": 22, "namespace": null, "name": "app/Http/Middleware/HideDebugbar.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\HideDebugbar.php", "line": 25}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "app/Http/Middleware/RedirectForMultiLanguage.php", "file": "C:\\wamp64\\www\\mazar\\app\\Http\\Middleware\\RedirectForMultiLanguage.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.257139, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:23", "source": "modules/User/Traits/HasRoles.php:23", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FTraits%2FHasRoles.php&line=23", "ajax": false, "filename": "HasRoles.php", "line": "23"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'system_log_view')", "type": "query", "params": [], "bindings": ["1", "system_log_view"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.2676098, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select count(`id`) as aggregate from `core_role_permissions` where (`role_id` = 1 and `permission` = 'dashboard_access')", "type": "query", "params": [], "bindings": ["1", "dashboard_access"], "hints": [], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 34}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/User/Models/Role.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Models\\Role.php", "line": 30}, {"index": 21, "namespace": null, "name": "modules/User/Traits/HasRoles.php", "file": "C:\\wamp64\\www\\mazar\\modules\\User\\Traits\\HasRoles.php", "line": 23}], "start": **********.281559, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Role.php:34", "source": "modules/User/Models/Role.php:34", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=34", "ajax": false, "filename": "Role.php", "line": "34"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_rate_limit_search_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.291266, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 158}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.3010712, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_rate_limit_search_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.3115678, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 159}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.320748, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_rate_limit_booking_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.3304129, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 162}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.339362, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_rate_limit_booking_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.350106, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 163}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.3586469, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_rate_limit_general_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.366748, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 166}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.373603, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `core_settings` where `name` = 'ratehawk_rate_limit_general_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 32}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 437}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\wamp64\\www\\mazar\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 429}, {"index": 20, "namespace": null, "name": "modules/Core/Models/Settings.php", "file": "C:\\wamp64\\www\\mazar\\modules\\Core\\Models\\Settings.php", "line": 31}, {"index": 21, "namespace": null, "name": "app/Helpers/AppHelper.php", "file": "C:\\wamp64\\www\\mazar\\app\\Helpers\\AppHelper.php", "line": 18}], "start": **********.3822842, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Settings.php:32", "source": "modules/Core/Models/Settings.php:32", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=32", "ajax": false, "filename": "Settings.php", "line": "32"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 167}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 185}], "start": **********.3885388, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "insert into `ratehawk_api_logs` (`request_id`, `method`, `endpoint`, `request_data`, `status`, `user_id`, `ip_address`, `user_agent`, `updated_at`, `created_at`) values ('rh_686eaeb8464d25.34357689', 'GET', '/api/b2b/v3/overview/', '{\\\"query\\\":[]}', 'pending', 7, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-09 18:02:32', '2025-07-09 18:02:32')", "type": "query", "params": [], "bindings": ["rh_686eaeb8464d25.34357689", "GET", "/api/b2b/v3/overview/", "{&quot;query&quot;:[]}", "pending", "7", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "2025-07-09 18:02:32", "2025-07-09 18:02:32"], "hints": [], "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 42}, {"index": 23, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 106}, {"index": 24, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 65}, {"index": 25, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 304}, {"index": 26, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 224}], "start": **********.401509, "duration": 0.009349999999999999, "duration_str": "9.35ms", "memory": 0, "memory_str": null, "filename": "LoggingService.php:42", "source": "modules/RateHawk/Services/LoggingService.php:42", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FServices%2FLoggingService.php&line=42", "ajax": false, "filename": "LoggingService.php", "line": "42"}, "connection": "mazar_travel"}, {"sql": "update `ratehawk_api_logs` set `status_code` = 200, `response_data` = '{\\\"data\\\":[{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/info\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/create\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/delete\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/disable\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/edit\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/list\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/profiles\\/restore\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/ordergroup\\/document\\/invoice\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/document\\/info_invoice\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/document\\/single_act\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/create\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/list\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/incremental_reviews\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/info\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/info\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/info\\/incremental_dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/custom\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/status\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":false,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/form\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/cancel\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/document\\/voucher\\/download\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/order\\/info\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/reviews\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/poi\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/region\\/dump\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/static\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/serp\\/hotels\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":150,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/hp\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":10,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/multicomplete\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/overview\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":100,\\\"seconds_number\\\":86400},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/serp\\/region\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":10,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/search\\/serp\\/geo\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":10,\\\"seconds_number\\\":60},{\\\"endpoint\\\":\\\"api\\/b2b\\/v3\\/hotel\\/prebook\\/\\\",\\\"is_active\\\":true,\\\"is_debug_mode\\\":false,\\\"is_limited\\\":true,\\\"requests_number\\\":30,\\\"seconds_number\\\":60}],\\\"debug\\\":null,\\\"error\\\":null,\\\"status\\\":\\\"ok\\\"}', `duration` = 0.98447108268738, `status` = 'completed', `ratehawk_api_logs`.`updated_at` = '2025-07-09 18:02:33' where `request_id` = 'rh_686eaeb8464d25.34357689'", "type": "query", "params": [], "bindings": ["200", "{&quot;data&quot;:[{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/general\\/document\\/closing_documents\\/info\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/create\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/delete\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/disable\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/edit\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/list\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/profiles\\/restore\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/ordergroup\\/document\\/invoice\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/document\\/info_invoice\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/document\\/single_act\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/create\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/tickets\\/list\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/incremental_reviews\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/info\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/info\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/info\\/incremental_dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/custom\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/finish\\/status\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:false,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/booking\\/form\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/cancel\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/document\\/voucher\\/download\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/order\\/info\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/reviews\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/poi\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/region\\/dump\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/static\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/serp\\/hotels\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:150,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/hp\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:10,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/multicomplete\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/overview\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:100,&quot;seconds_number&quot;:86400},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/serp\\/region\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:10,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/search\\/serp\\/geo\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:10,&quot;seconds_number&quot;:60},{&quot;endpoint&quot;:&quot;api\\/b2b\\/v3\\/hotel\\/prebook\\/&quot;,&quot;is_active&quot;:true,&quot;is_debug_mode&quot;:false,&quot;is_limited&quot;:true,&quot;requests_number&quot;:30,&quot;seconds_number&quot;:60}],&quot;debug&quot;:null,&quot;error&quot;:null,&quot;status&quot;:&quot;ok&quot;}", "0.98447108268738", "completed", "2025-07-09 18:02:33", "rh_686eaeb8464d25.34357689"], "hints": [], "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "modules/RateHawk/Services/LoggingService.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\LoggingService.php", "line": 78}, {"index": 13, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 116}, {"index": 14, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 65}, {"index": 15, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 304}, {"index": 16, "namespace": null, "name": "modules/RateHawk/Admin/RateHawkController.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Admin\\RateHawkController.php", "line": 224}], "start": 1752084153.277245, "duration": 0.00864, "duration_str": "8.64ms", "memory": 0, "memory_str": null, "filename": "LoggingService.php:78", "source": "modules/RateHawk/Services/LoggingService.php:78", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FServices%2FLoggingService.php&line=78", "ajax": false, "filename": "LoggingService.php", "line": "78"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 158}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": 1752084153.29514, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_search_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_search_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 159}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": 1752084153.3025641, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 162}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": 1752084153.308395, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_booking_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_booking_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 163}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": 1752084153.3146622, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_requests' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_requests"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 166}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": 1752084153.320082, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}, {"sql": "select * from `ratehawk_settings` where `key` = 'ratehawk_rate_limit_general_seconds' limit 1", "type": "query", "params": [], "bindings": ["ratehawk_rate_limit_general_seconds"], "hints": ["Use <code>SELECT *</code> only if you need all columns from table", "<code>LIMIT</code> without <code>ORDER BY</code> causes non-deterministic results, depending on the query execution plan"], "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "modules/RateHawk/Models/RateHawkSetting.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Models\\RateHawkSetting.php", "line": 141}, {"index": 17, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 27}, {"index": 18, "namespace": null, "name": "modules/RateHawk/Helpers/ConfigHelper.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Helpers\\ConfigHelper.php", "line": 167}, {"index": 19, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 230}, {"index": 20, "namespace": null, "name": "modules/RateHawk/Services/RateHawkApiClient.php", "file": "C:\\wamp64\\www\\mazar\\modules\\RateHawk\\Services\\RateHawkApiClient.php", "line": 203}], "start": 1752084153.324889, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "RateHawkSetting.php:141", "source": "modules/RateHawk/Models/RateHawkSetting.php:141", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FRateHawk%2FModels%2FRateHawkSetting.php&line=141", "ajax": false, "filename": "RateHawkSetting.php", "line": "141"}, "connection": "mazar_travel"}]}, "models": {"data": {"Modules\\Core\\Models\\Settings": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FCore%2FModels%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Modules\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2Fwamp64%2Fwww%2Fmazar%2Fmodules%2FUser%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 28, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "auth": {"guards": {"web": "array:2 [\n  \"name\" => \"<EMAIL>\"\n  \"user\" => array:44 [\n    \"id\" => 7\n    \"name\" => \"<EMAIL> \"\n    \"first_name\" => \"<EMAIL>\"\n    \"last_name\" => null\n    \"business_name\" => null\n    \"email\" => \"<EMAIL>\"\n    \"email_verified_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"two_factor_secret\" => null\n    \"two_factor_recovery_codes\" => null\n    \"address\" => null\n    \"address2\" => null\n    \"phone\" => null\n    \"birthday\" => null\n    \"city\" => null\n    \"state\" => null\n    \"country\" => null\n    \"zip_code\" => null\n    \"last_login_at\" => null\n    \"avatar_id\" => null\n    \"bio\" => null\n    \"status\" => \"publish\"\n    \"review_score\" => null\n    \"create_user\" => null\n    \"update_user\" => null\n    \"vendor_commission_amount\" => null\n    \"vendor_commission_type\" => null\n    \"need_update_pw\" => 0\n    \"role_id\" => 1\n    \"deleted_at\" => null\n    \"created_at\" => \"2025-07-04T18:17:21.000000Z\"\n    \"updated_at\" => \"2025-07-04T19:05:56.000000Z\"\n    \"payment_gateway\" => null\n    \"total_guests\" => null\n    \"locale\" => null\n    \"user_name\" => null\n    \"verify_submit_status\" => null\n    \"is_verified\" => null\n    \"active_status\" => 0\n    \"dark_mode\" => 0\n    \"messenger_color\" => \"#2180f3\"\n    \"stripe_customer_id\" => null\n    \"total_before_fees\" => null\n    \"credit_balance\" => null\n    \"role\" => array:10 [\n      \"id\" => 1\n      \"name\" => \"administrator\"\n      \"code\" => \"administrator\"\n      \"commission\" => null\n      \"commission_type\" => \"default\"\n      \"create_user\" => null\n      \"update_user\" => null\n      \"status\" => null\n      \"created_at\" => \"2025-07-04T18:17:11.000000Z\"\n      \"updated_at\" => \"2025-07-04T18:17:11.000000Z\"\n    ]\n  ]\n]", "sanctum": "null"}, "names": "web: <EMAIL>"}, "gate": {"count": 0, "messages": []}, "session": {"_token": "OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/custom-css\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "bc_current_currency": "", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/admin/ratehawk/test-connection", "status_code": "<pre class=sf-dump id=sf-dump-1440127123 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1440127123\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1565045131 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1565045131\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1180330759 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180330759\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1566172587 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">http://127.0.0.1:8000/admin/module/core/settings/index/ratehawk</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2130 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Im9sVVdmTWtFN1NLamZVZmFxWjhNc3c9PSIsInZhbHVlIjoiY2REc0NMbjNnb1UweUsrTnJNTHA0U0dmaTNhOXZDUmYvaXU3N1J2eVhUUXBEWVN4bVVmaS9adXVEaUpBS3ZnWFh4cHd0NUk5MFFGZURmYTM3M2MrdzJ0UXhjakZoeTBVamNtVWpkR1FyZ1RvWDJ6UVB6MXNzWEVGemIvdi8xeCtNc3FmYVJCQUM1YTJwNEF4aHhONmg5YlBWTVpXdDh4dTJoZkd2VlZTamh4bXhHNFM3S1FVbmpQOFhqUkVrWS9JeTVvWWJZbnJ0aUpkdGtnQUp3Qk1FeEdKSzdUQnNRTlgrQnNvdys5MGxTaz0iLCJtYWMiOiI4MzAxM2QxNzNlMTM4M2U4OTU5ZTUxNGRmNTkzZTczMTMzOThhODFhM2UwZjk1YTNiNzYwYmNlMTkzOWU1Y2U2IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlAxb1JxQVl5YU9IZURrbGhEbVpqMUE9PSIsInZhbHVlIjoia2gyUFVaOEtZc2FaYS9vNGx6S084V0EvVy9Ia0V4QmRTRStpNEdlSXpEd2pGSkxSdTJDVXUyRmFyQVl5RjhIbmFkbTRWYjVPZllqZHQzQTZJWW5oZytpMkZvMWZxKy9MYVJBVWdmNE0veFVpcjh1SkhqWTJRaTd5K3EwWThXZEciLCJtYWMiOiI2ZTZhYWU1OWM2M2RjZWYzOTUyODdkZjUwNmRiNDEzNmQwZTFhZDU1ZDMzYTJiZmFjNWY1NGUzN2E4ZDA3MDNlIiwidGFnIjoiIn0%3D; mazar_travel_session=eyJpdiI6IlNrUDlrOEJWZGNNQUp5WFNaVk5ORHc9PSIsInZhbHVlIjoiOFdFS2pzSkQySUNsU3JvS3RXN0Y3ajA3YUFuNEhPT1VLQ0VoNElVeUp6Sk1XMVNtV0UwOGJUYmp3QURQcHpjcFdYRXQ5djQ5RWo5NEs2QnNidjVhSzNIdmVGbDFFNTlwWjdvQlJ0WXE3MWpYbDg0WW41QXdNblRLOUU2dFVFd24iLCJtYWMiOiJmYTFlYjEwY2NhNmM1MWMxYjg5MzA3YmE5Y2NiZjBkNjY1MGRiNzRmMTJkMjcxOWViNDlmZTczYWYyMDY5YTljIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1566172587\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">7|vX3RoSA0qOc4VgYDAYzOvUTSuPSMrZmFDItJQkp6PjQyISsilBRveMNnBtmQ|$2y$12$Me4M6gZJZiPQe8JQDJSxMuwt/LRdqv1bZzH.QFWpmFwpsQOPVBTk6</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>mazar_travel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ncJc6JL7D4OUdNqnMyW2JaSVePbveurWfiadoF7Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1316769398 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 09 Jul 2025 18:02:33 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImpnNEEyRUZLTUtiZFVEb0pialJkVkE9PSIsInZhbHVlIjoiZWVqRjl4QUdvMjFBQ0JFb053ZEFnT2tzK3pidXFpZllzMmkrYWdkOU9MMmFCajhVcTNxZ1liRW82Rmwwd1dYUkVjOG5HL0JHTkkyRmNGZUJtSEJxWHFISGFpblZQdmRpTklUUG1jVW9HKzRJMGZuZlVKaklrSDN1ck42UlY2dDYiLCJtYWMiOiJkNTRlNDVhNTg4YTViZGVkMWRjM2U2ZTBlZmIyOTEwN2UwMWUwNWJhMmVkYTVkYTUwODcwNmI2MTU5ZDE4MDE0IiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:02:33 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">mazar_travel_session=eyJpdiI6IlY4RHdmL0hXdTliS01KWGV5VFNjZXc9PSIsInZhbHVlIjoieXJjOE1pT2IxSmRIOWptYVpQT0c0bWsxQ1hiQXJJZm0zV0ZPeGhlajU3bVAvYnk1bENmL0NqUlNFOGpTaEhycjVwb0JSN2FtUWtsQllTdituNGJWbjFtUmRMY3FYNXN6VXJDZHl4K29VT3dkdmlCc1FpZ1hWRWNsZHJsOUdwWk8iLCJtYWMiOiI1MDRkMTRlN2I0MmFhM2IxYjUyOTRmYTFmNGQyZjAzMDRlYTc1MTA0MTdmYjExZWQ4ZjQxOWI0ODUzMjllOGE3IiwidGFnIjoiIn0%3D; expires=Wed, 09 Jul 2025 20:02:33 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImpnNEEyRUZLTUtiZFVEb0pialJkVkE9PSIsInZhbHVlIjoiZWVqRjl4QUdvMjFBQ0JFb053ZEFnT2tzK3pidXFpZllzMmkrYWdkOU9MMmFCajhVcTNxZ1liRW82Rmwwd1dYUkVjOG5HL0JHTkkyRmNGZUJtSEJxWHFISGFpblZQdmRpTklUUG1jVW9HKzRJMGZuZlVKaklrSDN1ck42UlY2dDYiLCJtYWMiOiJkNTRlNDVhNTg4YTViZGVkMWRjM2U2ZTBlZmIyOTEwN2UwMWUwNWJhMmVkYTVkYTUwODcwNmI2MTU5ZDE4MDE0IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:02:33 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">mazar_travel_session=eyJpdiI6IlY4RHdmL0hXdTliS01KWGV5VFNjZXc9PSIsInZhbHVlIjoieXJjOE1pT2IxSmRIOWptYVpQT0c0bWsxQ1hiQXJJZm0zV0ZPeGhlajU3bVAvYnk1bENmL0NqUlNFOGpTaEhycjVwb0JSN2FtUWtsQllTdituNGJWbjFtUmRMY3FYNXN6VXJDZHl4K29VT3dkdmlCc1FpZ1hWRWNsZHJsOUdwWk8iLCJtYWMiOiI1MDRkMTRlN2I0MmFhM2IxYjUyOTRmYTFmNGQyZjAzMDRlYTc1MTA0MTdmYjExZWQ4ZjQxOWI0ODUzMjllOGE3IiwidGFnIjoiIn0%3D; expires=Wed, 09-Jul-2025 20:02:33 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1316769398\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1375967659 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OwniJfS3ouDz1ze6zR2V6d1oQOocvRriczcVySbz</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://127.0.0.1:8000/custom-css</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>bc_current_currency</span>\" => \"\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1375967659\", {\"maxDepth\":0})</script>\n"}}