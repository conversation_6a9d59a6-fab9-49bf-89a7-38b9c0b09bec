<?php

namespace Modules\RateHawk\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RateHawkBooking extends Model
{
    use HasFactory;

    protected $table = 'ratehawk_bookings';

    protected $fillable = [
        'order_id',
        'partner_order_id',
        'search_hash',
        'match_hash',
        'book_hash',
        'status',
        'hotel_data',
        'room_data',
        'price_data',
        'guest_data',
        'payment_data',
        'api_response',
        'user_id',
        'user_ip',
        'checkin_date',
        'checkout_date',
        'total_amount',
        'currency',
        'commission_amount',
        'cancellation_deadline',
        'voucher_url',
        'invoice_url',
        'notes',
    ];

    protected $casts = [
        'hotel_data' => 'array',
        'room_data' => 'array',
        'price_data' => 'array',
        'guest_data' => 'array',
        'payment_data' => 'array',
        'api_response' => 'array',
        'checkin_date' => 'date',
        'checkout_date' => 'date',
        'total_amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'cancellation_deadline' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Get the user that made the booking
     */
    public function user()
    {
        return $this->belongsTo(\App\User::class);
    }

    /**
     * Get related API logs
     */
    public function apiLogs()
    {
        return $this->hasMany(RateHawkApiLog::class, 'request_id', 'order_id');
    }

    /**
     * Scope for specific status
     */
    public function scopeStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope for checkin date range
     */
    public function scopeCheckinRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('checkin_date', [$startDate, $endDate]);
    }

    /**
     * Scope for user bookings
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        switch ($this->status) {
            case 'created':
                return 'badge-info';
            case 'started':
                return 'badge-warning';
            case 'confirmed':
                return 'badge-success';
            case 'cancelled':
                return 'badge-danger';
            case 'completed':
                return 'badge-primary';
            case 'failed':
                return 'badge-danger';
            default:
                return 'badge-secondary';
        }
    }

    /**
     * Get formatted status
     */
    public function getFormattedStatusAttribute(): string
    {
        return ucfirst(str_replace('_', ' ', $this->status));
    }

    /**
     * Get hotel name
     */
    public function getHotelNameAttribute(): ?string
    {
        return $this->hotel_data['name'] ?? null;
    }

    /**
     * Get hotel address
     */
    public function getHotelAddressAttribute(): ?string
    {
        return $this->hotel_data['address'] ?? null;
    }

    /**
     * Get room name
     */
    public function getRoomNameAttribute(): ?string
    {
        return $this->room_data['name'] ?? null;
    }

    /**
     * Get guest names
     */
    public function getGuestNamesAttribute(): array
    {
        if (!$this->guest_data) {
            return [];
        }

        $names = [];
        foreach ($this->guest_data as $guest) {
            if (isset($guest['first_name']) && isset($guest['last_name'])) {
                $names[] = $guest['first_name'] . ' ' . $guest['last_name'];
            }
        }

        return $names;
    }

    /**
     * Get primary guest name
     */
    public function getPrimaryGuestNameAttribute(): ?string
    {
        $names = $this->getGuestNamesAttribute();
        return $names[0] ?? null;
    }

    /**
     * Get nights count
     */
    public function getNightsAttribute(): ?int
    {
        if (!$this->checkin_date || !$this->checkout_date) {
            return null;
        }

        return $this->checkin_date->diffInDays($this->checkout_date);
    }

    /**
     * Check if booking can be cancelled
     */
    public function canBeCancelled(): bool
    {
        if (in_array($this->status, ['cancelled', 'completed', 'failed'])) {
            return false;
        }

        if ($this->cancellation_deadline && now()->isAfter($this->cancellation_deadline)) {
            return false;
        }

        return true;
    }

    /**
     * Check if booking is active
     */
    public function isActive(): bool
    {
        return in_array($this->status, ['created', 'started', 'confirmed']);
    }

    /**
     * Check if booking is completed
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if booking is cancelled
     */
    public function isCancelled(): bool
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if booking failed
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Get formatted total amount
     */
    public function getFormattedTotalAmountAttribute(): string
    {
        if (!$this->total_amount || !$this->currency) {
            return 'N/A';
        }

        return $this->currency . ' ' . number_format($this->total_amount, 2);
    }

    /**
     * Get booking summary for display
     */
    public function getSummaryAttribute(): array
    {
        return [
            'order_id' => $this->order_id,
            'hotel_name' => $this->hotel_name,
            'room_name' => $this->room_name,
            'guest_name' => $this->primary_guest_name,
            'checkin_date' => $this->checkin_date?->format('Y-m-d'),
            'checkout_date' => $this->checkout_date?->format('Y-m-d'),
            'nights' => $this->nights,
            'total_amount' => $this->formatted_total_amount,
            'status' => $this->formatted_status,
            'can_cancel' => $this->canBeCancelled(),
        ];
    }
}
